import { NextRequest, NextResponse } from "next/server";
import { getBackendUrl } from "@/utils/port-config-server";

export async function GET(
  request: NextRequest,
  { params }: { params: { path: string[] } }
) {
  try {
    const BACKEND_URL = "http://localhost:3574";
    const previewPath = params.path.join("/");
    const url = `${BACKEND_URL}/previews/${previewPath}`;

    // Forward the request to the backend
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "User-Agent": request.headers.get("User-Agent") || "",
      },
    });

    if (!response.ok) {
      return new NextResponse("Preview not found", { status: 404 });
    }

    // Get the response data as a buffer for binary data
    const data = await response.arrayBuffer();

    // Return response with proper headers for image serving
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        "Content-Type": response.headers.get("Content-Type") || "image/jpeg",
        "Cache-Control": "public, max-age=3600", // Cache for 1 hour
        "Cross-Origin-Resource-Policy": "cross-origin",
      },
    });
  } catch (error) {
    console.error("Preview proxy error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to load preview",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
