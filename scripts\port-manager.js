#!/usr/bin/env node

/**
 * Port Manager Utility
 * Handles automatic port conflict detection, resolution, and configuration updates
 */

const fs = require('fs');
const path = require('path');
const { exec, execSync } = require('child_process');
const util = require('util');

const execAsync = util.promisify(exec);

class PortManager {
    constructor() {
        this.defaultPorts = {
            frontend: 3030,
            backend: 3003,  // Fixed backend port
            ngrok: 4040
        };
        this.projectRoot = path.resolve(__dirname, '..');
        this.nextConfigPath = path.join(this.projectRoot, 'next.config.js');
    }

    /**
     * Check if a port is in use (OPTIMIZED)
     * @param {number} port - Port number to check
     * @returns {Promise<boolean>} - True if port is in use
     */
    async isPortInUse(port) {
        try {
            // Faster TCP connection test instead of netstat
            const net = require('net');
            return new Promise((resolve) => {
                const server = net.createServer();
                server.listen(port, () => {
                    server.once('close', () => resolve(false));
                    server.close();
                });
                server.on('error', () => resolve(true));
                
                // Timeout after 500ms to prevent hanging
                setTimeout(() => {
                    server.close();
                    resolve(true);
                }, 500);
            });
        } catch (error) {
            return false;
        }
    }

    /**
     * Get the process ID using a specific port (OPTIMIZED)
     * @param {number} port - Port number
     * @returns {Promise<number|null>} - Process ID or null if not found
     */
    async getProcessUsingPort(port) {
        try {
            // Use faster PowerShell command with timeout
            const { stdout } = await execAsync(`powershell -Command "& {Get-NetTCPConnection -LocalPort ${port} -ErrorAction SilentlyContinue | Select-Object -First 1 -ExpandProperty OwningProcess}"`, { timeout: 2000 });
            const pid = parseInt(stdout.trim());
            return isNaN(pid) ? null : pid;
        } catch (error) {
            // Fallback to netstat if PowerShell fails
            try {
                const { stdout } = await execAsync(`netstat -ano | findstr ":${port}" | findstr LISTENING`, { timeout: 1000 });
                const lines = stdout.trim().split('\n');
                if (lines.length > 0) {
                    const parts = lines[0].trim().split(/\s+/);
                    const pid = parts[parts.length - 1];
                    if (pid && pid.match(/^\d+$/)) {
                        return parseInt(pid);
                    }
                }
            } catch (fallbackError) {
                // No process found
            }
        }
        return null;
    }

    /**
     * Get detailed process information for a PID
     * @param {number} pid - Process ID
     * @returns {Promise<Object|null>} - Process details or null
     */
    async getProcessDetails(pid) {
        try {
            const { stdout } = await execAsync(`powershell -Command "& {Get-Process -Id ${pid} -ErrorAction SilentlyContinue | Select-Object Name, ProcessName, Id, Path | ConvertTo-Json}"`, { timeout: 2000 });
            const processInfo = JSON.parse(stdout.trim());
            return processInfo;
        } catch (error) {
            return null;
        }
    }

    /**
     * Kill a process by PID with better error reporting
     * @param {number} pid - Process ID to kill
     * @param {string} service - Service name for logging
     * @returns {Promise<boolean>} - True if successful
     */
    async killProcess(pid, service = 'unknown') {
        try {
            const processDetails = await this.getProcessDetails(pid);
            const processName = processDetails ? processDetails.ProcessName : 'unknown';
            
            console.log(`🔪 Killing ${processName} (PID: ${pid}) using ${service} port...`);
            execSync(`taskkill /PID ${pid} /F`, { stdio: 'ignore' });
            console.log(`✅ Successfully terminated ${processName} (PID: ${pid})`);
            return true;
        } catch (error) {
            console.warn(`❌ Failed to kill process ${pid} for ${service}:`, error.message);
            return false;
        }
    }

    /**
     * Find an available port starting from a base port (OPTIMIZED)
     * @param {number} basePort - Starting port number
     * @param {number} maxTries - Maximum number of ports to try
     * @param {Array<number>} excludePorts - Ports to exclude from consideration
     * @returns {Promise<number>} - Available port number
     */
    async findAvailablePort(basePort, maxTries = 20, excludePorts = []) {
        // Try a smaller, smarter range of ports first
        const smartPorts = [basePort, basePort + 1, basePort + 2, basePort + 10, basePort + 20];
        
        // First, try the smart ports
        for (const port of smartPorts) {
            if (!excludePorts.includes(port) && !(await this.isPortInUse(port))) {
                return port;
            }
        }
        
        // If smart ports don't work, try sequential search with reduced range
        for (let port = basePort; port < basePort + maxTries; port++) {
            if (excludePorts.includes(port)) {
                continue;
            }
            if (!(await this.isPortInUse(port))) {
                return port;
            }
        }
        
        throw new Error(`No available port found in range ${basePort}-${basePort + maxTries}`);
    }

    /**
     * Resolve port conflicts by killing processes or finding alternatives (OPTIMIZED)
     * @param {Object} options - Configuration options
     * @returns {Promise<Object>} - Final port configuration
     */
    async resolvePortConflicts(options = {}) {
        const { 
            killExisting = false,  // Changed default to false for safety
            findAlternatives = true,
            force = false
        } = options;

        const result = {
            frontend: this.defaultPorts.frontend,
            backend: this.defaultPorts.backend,
            ngrok: this.defaultPorts.ngrok,
            conflicts: [],
            killed: [],
            alternatives: {}
        };

        // Track allocated ports to avoid conflicts between services
        const allocatedPorts = [];

        console.log('🔍 Checking port conflicts (fast mode)...');

        // OPTIMIZATION: Check all ports in parallel for faster detection
        const portChecks = await Promise.all([
            this.isPortInUse(this.defaultPorts.frontend).then(inUse => ({ service: 'frontend', port: this.defaultPorts.frontend, inUse })),
            this.isPortInUse(this.defaultPorts.backend).then(inUse => ({ service: 'backend', port: this.defaultPorts.backend, inUse })),
            this.isPortInUse(this.defaultPorts.ngrok).then(inUse => ({ service: 'ngrok', port: this.defaultPorts.ngrok, inUse }))
        ]);

        // Process results
        for (const { service, port, inUse } of portChecks) {
            
            if (inUse) {
                result.conflicts.push({ service, port });
                console.log(`⚠️  Port ${port} (${service}) is in use`);

                const pid = await this.getProcessUsingPort(port);
                let processDetails = null;
                
                if (pid) {
                    processDetails = await this.getProcessDetails(pid);
                    const processName = processDetails ? processDetails.ProcessName : 'Unknown Process';
                    const processPath = processDetails ? processDetails.Path : 'Unknown Path';
                    
                    console.log(`🔍 Port ${port} is used by: ${processName} (PID: ${pid})`);
                    if (processPath && processPath !== 'Unknown Path') {
                        console.log(`   Path: ${processPath}`);
                    }
                    
                    if (killExisting) {
                        const killed = await this.killProcess(pid, service);
                        
                        if (killed) {
                            result.killed.push({ service, port, pid, processName });
                            
                            // Wait a moment for the port to be released
                            await new Promise(resolve => setTimeout(resolve, 1000));
                            
                            // Verify port is now available
                            const stillInUse = await this.isPortInUse(port);
                            if (!stillInUse) {
                                console.log(`✅ Port ${port} is now available for ${service}`);
                                continue;
                            }
                        }
                    } else {
                        console.log(`⚠️  Process will not be killed (killExisting = false)`);
                        console.log(`   To kill this process, run: taskkill /PID ${pid} /F`);
                        console.log(`   Or restart with --kill-existing flag`);
                    }
                } else {
                    console.log(`⚠️  Could not identify process using port ${port}`);
                }

                if (findAlternatives) {
                    console.log(`🔍 Finding alternative port for ${service}...`);
                    // Use a different base port for each service to avoid conflicts
                    const basePort = service === 'frontend' ? port + 1 : 
                                    service === 'backend' ? port + 10 : 
                                    port + 20;
                    
                    try {
                        const alternativePort = await this.findAvailablePort(basePort, 10, allocatedPorts);
                        result[service] = alternativePort;
                        allocatedPorts.push(alternativePort);
                        result.alternatives[service] = {
                            original: port,
                            alternative: alternativePort
                        };
                        console.log(`✅ Found alternative port ${alternativePort} for ${service}`);
                    } catch (error) {
                        console.warn(`⚠️  Could not find alternative for ${service}, using original port ${port}`);
                        // Keep original port and hope for the best
                        result[service] = port;
                        allocatedPorts.push(port);
                    }
                } else {
                    console.warn(`⚠️  Port ${port} (${service}) is in use but proceeding anyway`);
                    result[service] = port;
                    allocatedPorts.push(port);
                }
            } else {
                console.log(`✅ Port ${port} (${service}) is available`);
                allocatedPorts.push(port); // Track the default port as allocated
            }
        }

        return result;
    }

    /**
     * Update Next.js configuration with new backend port
     * @param {number} backendPort - Backend port number
     * @returns {Promise<boolean>} - True if successful
     */
    async updateNextConfig(backendPort) {
        try {
            if (!fs.existsSync(this.nextConfigPath)) {
                throw new Error('next.config.js not found');
            }

            let configContent = fs.readFileSync(this.nextConfigPath, 'utf8');
            
            // Update the API proxy destination
            const oldPattern = /destination:\s*['"`]http:\/\/localhost:\d+\/api\/:path\*['"`]/;
            const newDestination = `destination: 'http://localhost:${backendPort}/api/:path*'`;
            
            if (oldPattern.test(configContent)) {
                configContent = configContent.replace(oldPattern, newDestination);
                fs.writeFileSync(this.nextConfigPath, configContent);
                console.log(`✅ Updated next.config.js to use backend port ${backendPort}`);
                return true;
            } else {
                console.warn('⚠️  Could not find API proxy configuration in next.config.js');
                return false;
            }
        } catch (error) {
            console.error('❌ Failed to update next.config.js:', error.message);
            return false;
        }
    }

    /**
     * Update package.json scripts with dynamic ports
     * @param {Object} ports - Port configuration
     * @returns {Promise<boolean>} - True if successful
     */
    async updatePackageJsonScripts(ports) {
        try {
            const packagePath = path.join(this.projectRoot, 'package.json');
            
            if (!fs.existsSync(packagePath)) {
                throw new Error('package.json not found');
            }

            const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            
            // Update scripts with dynamic ports
            packageContent.scripts['dev:frontend'] = `next dev -p ${ports.frontend}`;
            packageContent.scripts['dev:backend'] = `nodemon backend/server.js`;
            
            fs.writeFileSync(packagePath, JSON.stringify(packageContent, null, 2));
            console.log(`✅ Updated package.json scripts with ports: frontend=${ports.frontend}, backend=${ports.backend}`);
            return true;
        } catch (error) {
            console.error('❌ Failed to update package.json scripts:', error.message);
            return false;
        }
    }

    /**
     * Create environment variables for the launcher
     * @param {Object} ports - Port configuration
     * @returns {Object} - Environment variables
     */
    createEnvironmentVariables(ports) {
        return {
            FRONTEND_PORT: ports.frontend.toString(),
            BACKEND_PORT: ports.backend.toString(),
            NGROK_PORT: ports.ngrok.toString(),
            PORT: ports.frontend.toString() // Standard port environment variable
        };
    }

    /**
     * Save port configuration to file
     * @param {Object} config - Port configuration
     */
    async savePortConfig(config) {
        const configPath = path.join(this.projectRoot, 'data', 'port-config.json');
        
        // Ensure data directory exists
        const dataDir = path.dirname(configPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
        console.log(`✅ Saved port configuration to ${configPath}`);
    }

    /**
     * Load saved port configuration
     * @returns {Object|null} - Saved configuration or null
     */
    loadPortConfig() {
        const configPath = path.join(this.projectRoot, 'data', 'port-config.json');
        
        if (fs.existsSync(configPath)) {
            try {
                const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
                console.log('📋 Loaded saved port configuration');
                return config;
            } catch (error) {
                console.warn('⚠️  Failed to load port configuration:', error.message);
            }
        }
        return null;
    }

    /**
     * Main port resolution function (OPTIMIZED)
     * @param {Object} options - Configuration options
     * @returns {Promise<Object>} - Final port configuration and environment
     */
    async resolveAndConfigure(options = {}) {
        const startTime = Date.now();
        
        // FAST MODE: Check if we have recent valid configuration
        const existingConfig = this.loadPortConfig();
        if (existingConfig && !options.force) {
            const configAge = Date.now() - new Date(existingConfig.timestamp).getTime();
            if (configAge < 5 * 60 * 1000) { // 5 minutes
                console.log('⚡ Using cached port configuration (fast mode)');
                return {
                    ports: existingConfig.ports,
                    environment: existingConfig.environment,
                    success: true,
                    cached: true
                };
            }
        }
        
        console.log('🚀 Starting port conflict resolution (optimized)...');
        
        try {
            // Set a global timeout for the entire operation
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('Port resolution timeout (10s)')), 10000);
            });
            
            const resolutionPromise = (async () => {
                // Resolve port conflicts
                const portConfig = await this.resolvePortConflicts(options);
                
                // Only update configs if ports actually changed
                const needsUpdate = portConfig.backend !== this.defaultPorts.backend || 
                                  portConfig.frontend !== this.defaultPorts.frontend;
                
                if (needsUpdate) {
                    // Update configs in parallel for speed
                    await Promise.all([
                        portConfig.backend !== this.defaultPorts.backend ? 
                            this.updateNextConfig(portConfig.backend) : Promise.resolve(),
                        this.updatePackageJsonScripts(portConfig)
                    ]);
                }
                
                // Create environment variables
                const env = this.createEnvironmentVariables(portConfig);
                
                // Save configuration for future use
                await this.savePortConfig({
                    ports: portConfig,
                    environment: env,
                    timestamp: new Date().toISOString()
                });
                
                const duration = Date.now() - startTime;
                console.log(`✅ Port configuration completed in ${duration}ms!`);
                console.log(`📊 Final configuration:
  Frontend: http://localhost:${portConfig.frontend}
  Backend:  http://localhost:${portConfig.backend}
  Ngrok:    Port ${portConfig.ngrok}`);
                
                if (Object.keys(portConfig.alternatives).length > 0) {
                    console.log('\n🔄 Port changes made:');
                    for (const [service, alt] of Object.entries(portConfig.alternatives)) {
                        console.log(`  ${service}: ${alt.original} → ${alt.alternative}`);
                    }
                }
                
                return {
                    ports: portConfig,
                    environment: env,
                    success: true
                };
            })();
            
            return await Promise.race([resolutionPromise, timeoutPromise]);
            
        } catch (error) {
            console.error('❌ Port resolution failed:', error.message);
            
            // FALLBACK: Use default ports if resolution fails
            const fallbackPorts = {
                frontend: this.defaultPorts.frontend,
                backend: this.defaultPorts.backend,
                ngrok: this.defaultPorts.ngrok
            };
            const fallbackEnv = this.createEnvironmentVariables(fallbackPorts);
            
            console.log('🔄 Using fallback port configuration');
            return {
                ports: fallbackPorts,
                environment: fallbackEnv,
                success: false,
                error: error.message,
                fallback: true
            };
        }
    }

    /**
     * Quick port status check
     */
    async checkPortStatus() {
        console.log('📊 Port Status Check:');
        
        for (const [service, port] of Object.entries(this.defaultPorts)) {
            const inUse = await this.isPortInUse(port);
            const status = inUse ? '🔴 IN USE' : '🟢 AVAILABLE';
            console.log(`  ${service.padEnd(10)} (${port}): ${status}`);
            
            if (inUse) {
                const pid = await this.getProcessUsingPort(port);
                if (pid) {
                    console.log(`    └─ Process ID: ${pid}`);
                }
            }
        }
    }
}

// CLI interface
if (require.main === module) {
    const portManager = new PortManager();
    const args = process.argv.slice(2);
    const command = args[0] || 'resolve';
    
    switch (command) {
        case 'check':
            portManager.checkPortStatus();
            break;
            
        case 'resolve':
            const options = {
                killExisting: args.includes('--kill-existing'),
                findAlternatives: !args.includes('--no-alternatives'),
                force: args.includes('--force')
            };
            
            portManager.resolveAndConfigure(options)
                .then(result => {
                    process.exit(result.success ? 0 : 1);
                })
                .catch(error => {
                    console.error('❌ Port manager failed:', error.message);
                    process.exit(1);
                });
            break;
            
        default:
            console.log(`
Port Manager - Automatic Port Conflict Resolution

Usage:
  node port-manager.js [command] [options]

Commands:
  check      - Check current port status
  resolve    - Resolve port conflicts (default)

Options:
  --kill-existing   - Kill processes using required ports
  --no-alternatives - Don't find alternative ports  
  --force           - Force resolution even with warnings

Examples:
  node port-manager.js check
  node port-manager.js resolve
  node port-manager.js resolve --kill-existing
`);
            break;
    }
}

module.exports = PortManager;