#!/usr/bin/env pwsh
# 🚀 Qwen Launcher - Advanced Model & Settings Manager
# Fixes model selection and provides easy configuration

param(
    [string]$Model = "qwen/qwen3-coder",
    [string]$Prompt,
    [switch]$Interactive,
    [switch]$Debug,
    [switch]$Settings,
    [switch]$ListModels,
    [switch]$Help
)

# Configuration
$QWEN_PATH = "C:\Users\<USER>\.mcp-modules\qwen.cmd"
$DEFAULT_API_KEY = "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"
$DEFAULT_BASE_URL = "https://openrouter.ai/api/v1"

function Show-Help {
    Write-Host "🚀 Qwen Launcher - Advanced Model & Settings Manager" -ForegroundColor Cyan
    Write-Host "===================================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "USAGE:" -ForegroundColor Yellow
    Write-Host "  qwen-launcher.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "OPTIONS:" -ForegroundColor Yellow
    Write-Host "  -Model <name>      Specify model (default: qwen/qwen3-coder)" -ForegroundColor Green
    Write-Host "  -Prompt <text>     Send a prompt directly" -ForegroundColor Green
    Write-Host "  -Interactive       Start interactive mode" -ForegroundColor Green
    Write-Host "  -Debug             Enable debug output" -ForegroundColor Green
    Write-Host "  -Settings          Configure Qwen settings" -ForegroundColor Green
    Write-Host "  -ListModels        Show available models" -ForegroundColor Green
    Write-Host "  -Help              Show this help" -ForegroundColor Green
    Write-Host ""
    Write-Host "EXAMPLES:" -ForegroundColor Yellow
    Write-Host "  qwen-launcher.ps1 -Interactive" -ForegroundColor Cyan
    Write-Host "  qwen-launcher.ps1 -Model 'qwen/qwen3-coder' -Prompt 'Hello'" -ForegroundColor Cyan
    Write-Host "  qwen-launcher.ps1 -Settings" -ForegroundColor Cyan
    Write-Host "  qwen-launcher.ps1 -ListModels" -ForegroundColor Cyan
}

function Show-AvailableModels {
    Write-Host "📋 Available Qwen Models on OpenRouter:" -ForegroundColor Cyan
    Write-Host "=======================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "PAID MODELS (with tool calling):" -ForegroundColor Green
    Write-Host "  qwen/qwen3-coder                   - Latest Qwen3-Coder (RECOMMENDED)" -ForegroundColor White
    Write-Host "  qwen/qwen-2.5-coder-32b-instruct  - Qwen 2.5 Coder 32B" -ForegroundColor White
    Write-Host "  qwen/qwen-2.5-72b-instruct        - Qwen 2.5 72B" -ForegroundColor White
    Write-Host ""
    Write-Host "FREE MODELS (limited tool calling):" -ForegroundColor Yellow
    Write-Host "  qwen/qwen3-coder:free             - Free version (may not support tools)" -ForegroundColor White
    Write-Host "  qwen/qwen3-30b-a3b:free           - Currently used (NO tool calling)" -ForegroundColor Red
    Write-Host "  qwen/qwen-2.5-coder-32b-instruct:free - Free Qwen 2.5 Coder" -ForegroundColor White
    Write-Host ""
    Write-Host "💡 For tool calling and agentic coding, use the PAID models!" -ForegroundColor Magenta
}

function Set-QwenEnvironment {
    param([string]$Model, [bool]$Verbose = $false)
    
    # Set environment variables for the session
    $env:OPENAI_API_KEY = $DEFAULT_API_KEY
    $env:OPENAI_BASE_URL = $DEFAULT_BASE_URL
    $env:GEMINI_MODEL = $Model
    $env:QWEN_MODEL = $Model
    $env:DEFAULT_MODEL = $Model
    
    if ($Verbose) {
        Write-Host "🔧 Environment Configuration:" -ForegroundColor Yellow
        Write-Host "  API Key: $($env:OPENAI_API_KEY.Substring(0,20))..." -ForegroundColor Cyan
        Write-Host "  Base URL: $env:OPENAI_BASE_URL" -ForegroundColor Cyan
        Write-Host "  Target Model: $Model" -ForegroundColor Cyan
        Write-Host "  Qwen Path: $QWEN_PATH" -ForegroundColor Cyan
    }
}

function Start-QwenWithModel {
    param(
        [string]$Model,
        [string]$Prompt = $null,
        [bool]$DebugMode = $false,
        [bool]$InteractiveMode = $false
    )
    
    Set-QwenEnvironment -Model $Model -Verbose $true
    
    # Build command arguments
    $arguments = @()
    
    if ($DebugMode) {
        $arguments += "--debug"
    }
    
    # Force the model parameter
    $arguments += "--model"
    $arguments += $Model
    
    if ($Prompt) {
        $arguments += "--prompt"
        $arguments += $Prompt
    }
    
    Write-Host ""
    Write-Host "🚀 Starting Qwen with model: $Model" -ForegroundColor Green
    Write-Host "Command: qwen $($arguments -join ' ')" -ForegroundColor Cyan
    Write-Host ""
    
    try {
        if ($arguments.Count -gt 0) {
            & $QWEN_PATH @arguments
        } else {
            & $QWEN_PATH
        }
    } catch {
        Write-Host "❌ Error running Qwen: $_" -ForegroundColor Red
        Write-Host ""
        Write-Host "💡 Troubleshooting:" -ForegroundColor Yellow
        Write-Host "  1. Check if Qwen is installed: qwen --version" -ForegroundColor White
        Write-Host "  2. Verify API key is correct" -ForegroundColor White
        Write-Host "  3. Try a different model with -Model parameter" -ForegroundColor White
    }
}

function Show-Settings {
    Write-Host "⚙️  Qwen Settings Configuration" -ForegroundColor Cyan
    Write-Host "==============================" -ForegroundColor Cyan
    Write-Host ""
    
    Write-Host "Current Configuration:" -ForegroundColor Yellow
    Write-Host "  API Key: $($DEFAULT_API_KEY.Substring(0,20))..." -ForegroundColor White
    Write-Host "  Base URL: $DEFAULT_BASE_URL" -ForegroundColor White
    Write-Host "  Default Model: qwen/qwen3-coder" -ForegroundColor White
    Write-Host "  Qwen Path: $QWEN_PATH" -ForegroundColor White
    Write-Host ""
    
    Write-Host "Available Actions:" -ForegroundColor Yellow
    Write-Host "  1. Test API Connection" -ForegroundColor Green
    Write-Host "  2. Change Default Model" -ForegroundColor Green
    Write-Host "  3. Update API Key" -ForegroundColor Green
    Write-Host "  4. Test Different Models" -ForegroundColor Green
    Write-Host "  5. Create Desktop Shortcut" -ForegroundColor Green
    Write-Host "  6. Return to Main Menu" -ForegroundColor Green
    Write-Host ""
    
    $choice = Read-Host "Select option (1-6)"
    
    switch ($choice) {
        "1" { Test-ApiConnection }
        "2" { Change-DefaultModel }
        "3" { Update-ApiKey }
        "4" { Test-DifferentModels }
        "5" { Create-DesktopShortcut }
        "6" { return }
        default { Write-Host "Invalid option. Please try again." -ForegroundColor Red }
    }
}

function Test-ApiConnection {
    Write-Host ""
    Write-Host "🧪 Testing API Connection..." -ForegroundColor Yellow
    
    $testResult = curl -X POST "https://openrouter.ai/api/v1/chat/completions" `
        -H "Content-Type: application/json" `
        -H "Authorization: Bearer $DEFAULT_API_KEY" `
        -d '{"model": "qwen/qwen3-coder", "messages": [{"role": "user", "content": "Test"}], "max_tokens": 10}' `
        2>$null
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ API connection successful!" -ForegroundColor Green
        Write-Host "Response: $testResult" -ForegroundColor Cyan
    } else {
        Write-Host "❌ API connection failed!" -ForegroundColor Red
    }
    
    Read-Host "Press Enter to continue"
}

function Change-DefaultModel {
    Write-Host ""
    Write-Host "📝 Change Default Model:" -ForegroundColor Yellow
    Show-AvailableModels
    Write-Host ""
    
    $newModel = Read-Host "Enter new default model (e.g., qwen/qwen3-coder)"
    
    if ($newModel) {
        Write-Host "✅ Default model changed to: $newModel" -ForegroundColor Green
        Write-Host "Note: This is temporary for this session." -ForegroundColor Yellow
        $script:Model = $newModel
    }
    
    Read-Host "Press Enter to continue"
}

function Test-DifferentModels {
    Write-Host ""
    Write-Host "🧪 Testing Different Models:" -ForegroundColor Yellow
    
    $models = @(
        "qwen/qwen3-coder",
        "qwen/qwen3-coder:free", 
        "qwen/qwen-2.5-coder-32b-instruct"
    )
    
    foreach ($testModel in $models) {
        Write-Host ""
        Write-Host "Testing $testModel..." -ForegroundColor Cyan
        Start-QwenWithModel -Model $testModel -Prompt "Just say 'Hello from $testModel'" -DebugMode $false
        Start-Sleep -Seconds 2
    }
    
    Read-Host "Press Enter to continue"
}

function Create-DesktopShortcut {
    Write-Host ""
    Write-Host "🔗 Creating Desktop Shortcut..." -ForegroundColor Yellow
    
    $desktopPath = [Environment]::GetFolderPath("Desktop")
    $shortcutPath = "$desktopPath\Qwen Launcher.lnk"
    $targetPath = "powershell.exe"
    $arguments = "-ExecutionPolicy Bypass -File `"$PSCommandPath`" -Interactive"
    
    $shell = New-Object -ComObject WScript.Shell
    $shortcut = $shell.CreateShortcut($shortcutPath)
    $shortcut.TargetPath = $targetPath
    $shortcut.Arguments = $arguments
    $shortcut.WorkingDirectory = "C:\claude\dl-organizer"
    $shortcut.IconLocation = "powershell.exe"
    $shortcut.Description = "Qwen AI Coding Assistant Launcher"
    $shortcut.Save()
    
    Write-Host "✅ Desktop shortcut created: $shortcutPath" -ForegroundColor Green
    Read-Host "Press Enter to continue"
}

# Main execution logic
if ($Help) {
    Show-Help
    exit
}

if ($ListModels) {
    Show-AvailableModels
    exit
}

if ($Settings) {
    Show-Settings
    exit
}

# Set working directory
Set-Location "C:\claude\dl-organizer"

# Show banner
Write-Host ""
Write-Host "🚀 Qwen Launcher v2.0" -ForegroundColor Cyan
Write-Host "=====================" -ForegroundColor Cyan
Write-Host "Advanced Model & Settings Manager" -ForegroundColor White
Write-Host ""

if ($Interactive -or (-not $Prompt)) {
    # Interactive mode
    Write-Host "Starting interactive mode with model: $Model" -ForegroundColor Green
    Start-QwenWithModel -Model $Model -InteractiveMode $true -DebugMode $Debug
} else {
    # Direct prompt mode
    Write-Host "Sending prompt with model: $Model" -ForegroundColor Green
    Start-QwenWithModel -Model $Model -Prompt $Prompt -DebugMode $Debug
}
