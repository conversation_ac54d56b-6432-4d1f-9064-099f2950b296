const express = require('express');
const path = require('path');
const fs = require('fs').promises;
const router = express.Router();
const defaultConfig = require('../config/default-config');

// Settings storage path
const SETTINGS_PATH = path.join(__dirname, '../../data/settings.json');

// Default settings - merge with the default config
const DEFAULT_SETTINGS = {
  openrouter: {
    ...defaultConfig.openRouter,
    ocrMode: 'driver_license'
  },
  ocr: {
    extractionType: 'auto-detect',
    processingOptions: {
      autoSave: true,
      showConfidence: true,
      batchMode: false,
      highAccuracy: false
    },
    qualitySettings: {
      minConfidence: 0.7,
      retryFailures: true,
      enhanceImages: false
    }
  },
  general: {
    theme: 'system',
    autoSaveInterval: 30000,
    thumbnailSize: 200,
    enableAutoBackup: true,
    ocrResultCaching: true,
    autoLoadCachedResults: true,
    showCacheIndicators: true,
    defaultFolderPath: '',
    gridSize: 4,
    enableHoverPreviews: true,
    autoRotateImages: false
  },
  ui: {
    sidebarCollapsed: false,
    showImageMetadata: true,
    showConfidenceScores: true,
    enableKeyboardShortcuts: true,
    compactMode: false,
    showProgressBar: true
  }
};

// Helper function to load settings
async function loadSettings() {
  try {
    const data = await fs.readFile(SETTINGS_PATH, 'utf8');
    return { ...DEFAULT_SETTINGS, ...JSON.parse(data) };
  } catch (error) {
    // If file doesn't exist, return defaults
    return DEFAULT_SETTINGS;
  }
}

// Helper function to save settings
async function saveSettings(settings) {
  try {
    // Ensure data directory exists
    await fs.mkdir(path.dirname(SETTINGS_PATH), { recursive: true });
    await fs.writeFile(SETTINGS_PATH, JSON.stringify(settings, null, 2));
    return true;
  } catch (error) {
    console.error('Error saving settings:', error);
    return false;
  }
}

// Get OpenRouter settings
router.get('/openrouter', async (req, res) => {
  try {
    const settings = await loadSettings();
    res.json(settings.openrouter);
  } catch (error) {
    console.error('Error loading OpenRouter settings:', error);
    res.status(500).json({ error: 'Failed to load settings' });
  }
});

// Update OpenRouter settings
router.post('/openrouter', async (req, res) => {
  try {
    const { apiKey, selectedModel, baseUrl, isEnabled, ocrMode, enableCostTracking, costLimit, fallbackModels } = req.body;
    
    // Validate required fields
    if (!apiKey || !selectedModel || !baseUrl) {
      return res.status(400).json({ error: 'Missing required fields' });
    }
    
    const settings = await loadSettings();
    settings.openrouter = {
      apiKey,
      selectedModel,
      baseUrl,
      isEnabled: isEnabled !== undefined ? isEnabled : true,
      ocrMode: ocrMode || 'driver_license',
      enableCostTracking: enableCostTracking !== undefined ? enableCostTracking : true,
      costLimit: costLimit || 10.0,
      fallbackModels: fallbackModels || defaultConfig.openRouter.fallbackModels
    };
    
    const success = await saveSettings(settings);
    if (success) {
      res.json({ success: true, message: 'Settings saved successfully' });
    } else {
      res.status(500).json({ error: 'Failed to save settings' });
    }
  } catch (error) {
    console.error('Error saving OpenRouter settings:', error);
    res.status(500).json({ error: 'Failed to save settings' });
  }
});

// Test OpenRouter connection
router.post('/openrouter/test', async (req, res) => {
  try {
    const { apiKey, model, baseUrl } = req.body;
    
    if (!apiKey || !model || !baseUrl) {
      return res.status(400).json({ error: 'Missing required fields for testing' });
    }
    
    // Test the connection by making a simple request to OpenRouter
    const fetch = require('node-fetch');
    
    const response = await fetch(`${baseUrl}/models`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:3030',
        'X-Title': 'DL Organizer'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      
      // Check if the selected model is available
      const modelExists = data.data && data.data.some(m => m.id === model);
      
      if (modelExists) {
        res.json({ 
          success: true, 
          message: 'Connection successful and model is available' 
        });
      } else {
        res.status(400).json({ 
          error: 'Model not available or not found',
          details: 'The selected model is not available in your OpenRouter account'
        });
      }
    } else {
      const errorData = await response.json().catch(() => ({}));
      res.status(response.status).json({ 
        error: 'Connection failed',
        details: errorData.error || 'Invalid API key or network error'
      });
    }
  } catch (error) {
    console.error('Error testing OpenRouter connection:', error);
    res.status(500).json({ 
      error: 'Connection test failed',
      details: error.message 
    });
  }
});

// Get all settings
router.get('/all', async (req, res) => {
  try {
    const settings = await loadSettings();
    res.json(settings);
  } catch (error) {
    console.error('Error loading all settings:', error);
    res.status(500).json({ error: 'Failed to load settings' });
  }
});

// Get OCR settings
router.get('/ocr', async (req, res) => {
  try {
    const settings = await loadSettings();
    res.json(settings.ocr);
  } catch (error) {
    console.error('Error loading OCR settings:', error);
    res.status(500).json({ error: 'Failed to load OCR settings' });
  }
});

// Update OCR settings
router.post('/ocr', async (req, res) => {
  try {
    const { extractionType, processingOptions, qualitySettings } = req.body;
    
    // Validate required fields
    if (!extractionType) {
      return res.status(400).json({ error: 'Missing extraction type' });
    }
    
    const settings = await loadSettings();
    settings.ocr = {
      extractionType,
      processingOptions: {
        autoSave: processingOptions?.autoSave !== undefined ? processingOptions.autoSave : DEFAULT_SETTINGS.ocr.processingOptions.autoSave,
        showConfidence: processingOptions?.showConfidence !== undefined ? processingOptions.showConfidence : DEFAULT_SETTINGS.ocr.processingOptions.showConfidence,
        batchMode: processingOptions?.batchMode !== undefined ? processingOptions.batchMode : DEFAULT_SETTINGS.ocr.processingOptions.batchMode,
        highAccuracy: processingOptions?.highAccuracy !== undefined ? processingOptions.highAccuracy : DEFAULT_SETTINGS.ocr.processingOptions.highAccuracy
      },
      qualitySettings: {
        minConfidence: qualitySettings?.minConfidence !== undefined ? qualitySettings.minConfidence : DEFAULT_SETTINGS.ocr.qualitySettings.minConfidence,
        retryFailures: qualitySettings?.retryFailures !== undefined ? qualitySettings.retryFailures : DEFAULT_SETTINGS.ocr.qualitySettings.retryFailures,
        enhanceImages: qualitySettings?.enhanceImages !== undefined ? qualitySettings.enhanceImages : DEFAULT_SETTINGS.ocr.qualitySettings.enhanceImages
      }
    };
    
    const success = await saveSettings(settings);
    if (success) {
      res.json({ success: true, message: 'OCR settings saved successfully' });
    } else {
      res.status(500).json({ error: 'Failed to save OCR settings' });
    }
  } catch (error) {
    console.error('Error saving OCR settings:', error);
    res.status(500).json({ error: 'Failed to save OCR settings' });
  }
});

// Get general settings
router.get('/general', async (req, res) => {
  try {
    const settings = await loadSettings();
    res.json(settings.general);
  } catch (error) {
    console.error('Error loading general settings:', error);
    res.status(500).json({ error: 'Failed to load general settings' });
  }
});

// Update general settings
router.post('/general', async (req, res) => {
  try {
    const { 
      theme, 
      autoSaveInterval, 
      thumbnailSize, 
      enableAutoBackup,
      ocrResultCaching,
      autoLoadCachedResults,
      showCacheIndicators,
      defaultFolderPath,
      gridSize,
      enableHoverPreviews,
      autoRotateImages
    } = req.body;
    
    const settings = await loadSettings();
    settings.general = {
      ...settings.general,
      ...(theme && { theme }),
      ...(autoSaveInterval && { autoSaveInterval }),
      ...(thumbnailSize && { thumbnailSize }),
      ...(enableAutoBackup !== undefined && { enableAutoBackup }),
      ...(ocrResultCaching !== undefined && { ocrResultCaching }),
      ...(autoLoadCachedResults !== undefined && { autoLoadCachedResults }),
      ...(showCacheIndicators !== undefined && { showCacheIndicators }),
      ...(defaultFolderPath !== undefined && { defaultFolderPath }),
      ...(gridSize && { gridSize }),
      ...(enableHoverPreviews !== undefined && { enableHoverPreviews }),
      ...(autoRotateImages !== undefined && { autoRotateImages })
    };
    
    const success = await saveSettings(settings);
    if (success) {
      res.json({ success: true, message: 'General settings saved successfully' });
    } else {
      res.status(500).json({ error: 'Failed to save general settings' });
    }
  } catch (error) {
    console.error('Error saving general settings:', error);
    res.status(500).json({ error: 'Failed to save general settings' });
  }
});

// Get UI settings
router.get('/ui', async (req, res) => {
  try {
    const settings = await loadSettings();
    res.json(settings.ui);
  } catch (error) {
    console.error('Error loading UI settings:', error);
    res.status(500).json({ error: 'Failed to load UI settings' });
  }
});

// Update UI settings
router.post('/ui', async (req, res) => {
  try {
    const { 
      sidebarCollapsed,
      showImageMetadata,
      showConfidenceScores,
      enableKeyboardShortcuts,
      compactMode,
      showProgressBar
    } = req.body;
    
    const settings = await loadSettings();
    settings.ui = {
      ...settings.ui,
      ...(sidebarCollapsed !== undefined && { sidebarCollapsed }),
      ...(showImageMetadata !== undefined && { showImageMetadata }),
      ...(showConfidenceScores !== undefined && { showConfidenceScores }),
      ...(enableKeyboardShortcuts !== undefined && { enableKeyboardShortcuts }),
      ...(compactMode !== undefined && { compactMode }),
      ...(showProgressBar !== undefined && { showProgressBar })
    };
    
    const success = await saveSettings(settings);
    if (success) {
      res.json({ success: true, message: 'UI settings saved successfully' });
    } else {
      res.status(500).json({ error: 'Failed to save UI settings' });
    }
  } catch (error) {
    console.error('Error saving UI settings:', error);
    res.status(500).json({ error: 'Failed to save UI settings' });
  }
});

module.exports = router;