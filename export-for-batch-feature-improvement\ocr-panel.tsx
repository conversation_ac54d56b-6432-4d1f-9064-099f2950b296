"use client"

import { useState, useRef, useEffect, useCallback } from 'react'
import { Eye, Settings, Save, X, FileText, AlertCircle, Bot, Cpu, Globe, ChevronDown, CheckCircle, Copy, Check, ExternalLink, FolderOpen, Shield } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import Image from 'next/image'
import { Input } from '@/components/ui/input'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { cn } from '@/lib/utils'
import { ImageFile, OCRResult, QualityWarning } from '@/types'
import OCRModelSelector from '../ocr-model-selector'
import EnhancedOCRSettings from './enhanced-ocr-settings'
import ReadySearchPanel from './readysearch-panel'
import { useSettingsStandalone } from '@/hooks/use-settings-sync'
import { useOCRMode } from '@/hooks/use-ocr-mode'
import { useModelValidation } from '@/hooks/use-model-validation'

interface OCRPanelProps {
  selectedImage: ImageFile | null
  onAnalyze: (imageId: string, modelId?: string, forceRefresh?: boolean) => void
  ocrResult: OCRResult | null
  isProcessing: boolean
  onSave: (data: OCRResult) => void
  onCancel: () => void
  className?: string
  countryMode?: 'us' | 'australian'
  images?: ImageFile[]
  onAnalyzeDual?: (frontId: string, backId: string, modelId?: string) => void
  onModeChange?: (mode: 'us' | 'australian') => void
  error?: string | null
  successMessage?: string | null
}

interface OCRFormProps {
  initialData: OCRResult
  onSave: (data: OCRResult) => void
  onCancel: () => void
  documentType: string
  isAustralianDocument: boolean
  error?: string | null
  successMessage?: string | null
  isSaving?: boolean
}

function OCRForm({ initialData, onSave, onCancel, documentType, isAustralianDocument, error, successMessage, isSaving }: OCRFormProps) {
  const [formData, setFormData] = useState<OCRResult>(initialData)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Update form data when initialData changes (including mode changes)
  useEffect(() => {
    setFormData(initialData)
  }, [initialData])

  const handleChange = (field: keyof OCRResult, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    // Relaxed validation - only require essential fields that are always needed
    // Allow saving partial OCR results to enable data correction workflows
    
    // Names are essential for identification
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required'
    }
    
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required'
    }
    
    // Mode-aware document number validation
    const isAustralian = isAustralianDocument
    
    if (isAustralian) {
      // Australian documents: require either cardNumber OR licenseNumber
      if (!formData.cardNumber?.trim() && !formData.licenseNumber?.trim()) {
        newErrors.cardNumber = 'Card number or license number is required'
        newErrors.licenseNumber = 'Card number or license number is required'
      }
    } else {
      // US documents: require licenseNumber
      if (!formData.licenseNumber?.trim()) {
        newErrors.licenseNumber = 'License number is required'
      }
    }
    
    // State validation - only require format if provided
    if (formData.state?.trim()) {
      const trimmedState = formData.state.trim()
      
      if (isAustralian) {
        // Australian states: 2-3 characters (NSW, VIC, QLD, etc.)
        if (trimmedState.length < 2 || trimmedState.length > 3) {
          newErrors.state = 'State must be 2-3 characters (e.g., NSW, VIC, QLD)'
        }
      } else {
        // US states: Accept 2-3 characters to be more flexible
        if (trimmedState.length < 2 || trimmedState.length > 3) {
          newErrors.state = 'State must be 2-3 characters (e.g., CA, NY, TX, WA)'
        }
      }
      
      // Optional: Validate against known state codes (warnings only)
      if (isAustralian) {
        const validAusStates = ['NSW', 'VIC', 'QLD', 'WA', 'SA', 'TAS', 'ACT', 'NT']
        if (!validAusStates.includes(trimmedState.toUpperCase())) {
          console.warn(`Unknown Australian state/territory: ${trimmedState}`)
        }
      } else {
        const validUSStates = ['AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA', 'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD', 'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ', 'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC', 'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY', 'DC']
        if (!validUSStates.includes(trimmedState.toUpperCase())) {
          console.warn(`Unknown US state: ${trimmedState}`)
        }
      }
    }
    
    // Validate date formats - only if provided (relaxed validation)
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/
    if (formData.dateOfBirth?.trim() && !dateRegex.test(formData.dateOfBirth.trim())) {
      newErrors.dateOfBirth = 'Date must be in YYYY-MM-DD format'
    }
    
    if (formData.expirationDate?.trim() && !dateRegex.test(formData.expirationDate.trim())) {
      newErrors.expirationDate = 'Date must be in YYYY-MM-DD format'
    }
    
    if (formData.issueDate?.trim() && !dateRegex.test(formData.issueDate.trim())) {
      newErrors.issueDate = 'Date must be in YYYY-MM-DD format'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      onSave(formData)
    }
  }

  return (
    <div className="space-y-4">
      {error && (
        <div className="bg-red-50 dark:bg-red-950/20 border border-red-300 dark:border-red-700 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-4 w-4 text-red-600 dark:text-red-400 flex-shrink-0 mt-0.5" />
            <span className="text-sm font-medium text-red-800 dark:text-red-200 break-words min-w-0 flex-1">
              {error}
            </span>
          </div>
        </div>
      )}

      {successMessage && (
        <div className="bg-green-50 dark:bg-green-950/20 border border-green-300 dark:border-green-700 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400 flex-shrink-0 mt-0.5" />
            <span className="text-sm font-medium text-green-800 dark:text-green-200 break-words min-w-0 flex-1">
              {successMessage}
            </span>
          </div>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-4">
      {isAustralianDocument ? (
        // Australian format: Surname and Given Names
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Surname *</label>
            <Input
              value={formData.lastName}
              onChange={(e) => handleChange('lastName', e.target.value)}
              placeholder=""
              className={cn(errors.lastName && "border-red-500")}
            />
            {errors.lastName && (
              <p className="text-sm text-red-500 mt-1">{errors.lastName}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Given Names *</label>
            <Input
              value={`${formData.firstName}${formData.middleName ? ' ' + formData.middleName : ''}`}
              onChange={(e) => {
                const names = e.target.value.split(' ')
                handleChange('firstName', names[0] || '')
                handleChange('middleName', names.slice(1).join(' '))
              }}
              placeholder=""
              className={cn(errors.firstName && "border-red-500")}
            />
            {errors.firstName && (
              <p className="text-sm text-red-500 mt-1">{errors.firstName}</p>
            )}
          </div>
        </div>
      ) : (
        // US format: First, Middle, Last Name
        <div className="grid grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">First Name *</label>
            <Input
              value={formData.firstName}
              onChange={(e) => handleChange('firstName', e.target.value)}
              placeholder=""
              className={cn(errors.firstName && "border-red-500")}
            />
            {errors.firstName && (
              <p className="text-sm text-red-500 mt-1">{errors.firstName}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Middle Name</label>
            <Input
              value={formData.middleName || ''}
              onChange={(e) => handleChange('middleName', e.target.value)}
              placeholder=""
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Last Name *</label>
            <Input
              value={formData.lastName}
              onChange={(e) => handleChange('lastName', e.target.value)}
              placeholder=""
              className={cn(errors.lastName && "border-red-500")}
            />
            {errors.lastName && (
              <p className="text-sm text-red-500 mt-1">{errors.lastName}</p>
            )}
          </div>
        </div>
      )}

      <div>
        <label className="block text-sm font-medium mb-1">Date of Birth</label>
        <Input
          type="date"
          value={formData.dateOfBirth}
          onChange={(e) => handleChange('dateOfBirth', e.target.value)}
          className={cn(errors.dateOfBirth && "border-red-500")}
        />
        {errors.dateOfBirth && (
          <p className="text-sm text-red-500 mt-1">{errors.dateOfBirth}</p>
        )}
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Address</label>
        <Input
          value={formData.address}
          onChange={(e) => handleChange('address', e.target.value)}
          placeholder="123 Main St, City, State 12345"
        />
      </div>

      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              {isAustralianDocument ? 'DL #' : 'License Number'} *
            </label>
            <Input
              value={formData.licenseNumber}
              onChange={(e) => handleChange('licenseNumber', e.target.value)}
              placeholder={isAustralianDocument ? "12345678" : "D123456789"}
              className={cn(errors.licenseNumber && "border-red-500")}
            />
            {errors.licenseNumber && (
              <p className="text-sm text-red-500 mt-1">{errors.licenseNumber}</p>
            )}
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">State *</label>
            <Input
              value={formData.state}
              onChange={(e) => handleChange('state', e.target.value.toUpperCase())}
              placeholder={isAustralianDocument ? "NSW" : "CA"}
              maxLength={isAustralianDocument ? 3 : 2}
              className={cn(errors.state && "border-red-500")}
            />
            {errors.state && (
              <p className="text-sm text-red-500 mt-1">{errors.state}</p>
            )}
          </div>
        </div>

        {isAustralianDocument && (
          <div>
            <label className="block text-sm font-medium mb-1">
              Card # <span className="text-sm text-muted-foreground">(state-dependent)</span>
            </label>
            <Input
              value={formData.cardNumber || ''}
              onChange={(e) => handleChange('cardNumber', e.target.value)}
              placeholder="Card number (if present)"
            />
            <p className="text-xs text-muted-foreground mt-1">
              Some states display a separate card number on front, back, or both sides
            </p>
          </div>
        )}
      </div>

      <div className={cn("grid gap-4", isAustralianDocument ? "grid-cols-1" : "grid-cols-2")}>
        {!isAustralianDocument && (
          <div>
            <label className="block text-sm font-medium mb-1">Issue Date</label>
            <Input
              type="date"
              value={formData.issueDate}
              onChange={(e) => handleChange('issueDate', e.target.value)}
              className={cn(errors.issueDate && "border-red-500")}
            />
            {errors.issueDate && (
              <p className="text-sm text-red-500 mt-1">{errors.issueDate}</p>
            )}
          </div>
        )}
        
        <div>
          <label className="block text-sm font-medium mb-1">Expiration Date</label>
          <Input
            type="date"
            value={formData.expirationDate}
            onChange={(e) => handleChange('expirationDate', e.target.value)}
            className={cn(errors.expirationDate && "border-red-500")}
          />
          {errors.expirationDate && (
            <p className="text-sm text-red-500 mt-1">{errors.expirationDate}</p>
          )}
        </div>
      </div>

      {isAustralianDocument && (
        <div>
          <label className="block text-sm font-medium mb-1">Card Side</label>
          <div className="grid grid-cols-2 gap-2">
            <Button
              type="button"
              variant={formData.cardSide === 'front' ? 'default' : 'outline'}
              className="h-8"
              onClick={() => setFormData(prev => ({ ...prev, cardSide: 'front' }))}
            >
              Front
            </Button>
            <Button
              type="button"
              variant={formData.cardSide === 'back' ? 'default' : 'outline'}
              className="h-8"
              onClick={() => setFormData(prev => ({ ...prev, cardSide: 'back' }))}
            >
              Back
            </Button>
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Specify which side of the license this data represents for field merging
          </p>
        </div>
      )}

      {formData.confidence > 0 && (
        <div>
          <label className="block text-sm font-medium mb-1">Confidence Score</label>
          <div className="flex items-center gap-2">
            <div className="flex-1 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-500 h-2 rounded-full"
                style={{ width: `${formData.confidence * 100}%` }}
              />
            </div>
            <span className="text-sm text-muted-foreground">
              {Math.round(formData.confidence * 100)}%
            </span>
          </div>
        </div>
      )}

      <div className="flex gap-2 pt-4">
        <Button type="submit" className="flex-1" disabled={isSaving}>
          {isSaving ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
              Saving...
            </>
          ) : (
            <>
              <Save className="w-4 h-4 mr-2" />
              Save OCR Data
            </>
          )}
        </Button>
        <Button type="button" variant="outline" onClick={onCancel} disabled={isSaving}>
          <X className="w-4 h-4 mr-2" />
          Cancel
        </Button>
      </div>
    </form>
    </div>
  )
}

export default function OCRPanel({
  selectedImage,
  onAnalyze,
  ocrResult,
  isProcessing,
  onSave,
  onCancel,
  className,
  countryMode = 'us',
  onModeChange,
  error,
  successMessage
}: OCRPanelProps) {
  const [showRawText, setShowRawText] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [savedData, setSavedData] = useState<any>(null)
  const [loadingSavedData, setLoadingSavedData] = useState(false)
  const [showSavedDataViewer, setShowSavedDataViewer] = useState(false)
  const [editableTxtContent, setEditableTxtContent] = useState('')
  const [copied, setCopied] = useState(false)
  const [savingTxtContent, setSavingTxtContent] = useState(false)
  
  // Settings hook for dynamic model display and global state management
  const { openRouterConfig, connectionStatus, updateModel, saveOpenRouterConfig, updateOCRMode, testConnection } = useSettingsStandalone()
  
  // OCR mode hook for centralized state management
  const { mode: ocrMode, documentType, setMode: setOCRMode, setDocumentType, getDocumentTypeOptions, isAustralianDocumentType, isUSDocumentType } = useOCRMode()
  
  // Sync OCR mode hook with country mode prop
  useEffect(() => {
    if (countryMode && countryMode !== ocrMode) {
      setOCRMode(countryMode)
    }
  }, [countryMode, ocrMode, setOCRMode])
  
  // Use OCR mode hook value, fallback to prop
  const currentMode = ocrMode || countryMode

  // Auto-test connection on component mount if API key is present
  useEffect(() => {
    if (openRouterConfig.apiKey && connectionStatus === 'unknown') {
      testConnection().catch(err => {
        console.error('Auto connection test failed:', err)
      })
    }
  }, [openRouterConfig.apiKey, connectionStatus, testConnection])

  // Load saved OCR data for the selected image
  const loadSavedData = useCallback(async () => {
    if (!selectedImage) return

    setLoadingSavedData(true)
    try {
      const response = await fetch(`/api/ocr/saved-data/${selectedImage.id}`)
      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          setSavedData(result.data)
          setEditableTxtContent(result.data.txtContent || '')
        } else {
          setSavedData(null)
        }
      } else {
        setSavedData(null)
      }
    } catch (error) {
      console.error('Error loading saved OCR data:', error)
      setSavedData(null)
    } finally {
      setLoadingSavedData(false)
    }
  }, [selectedImage])

  // Load saved data when selected image changes
  useEffect(() => {
    if (selectedImage) {
      loadSavedData()
    } else {
      setSavedData(null)
    }
  }, [selectedImage, loadSavedData])

  // Helper function to get current model display name  
  const getCurrentModelDisplay = () => {
    const modelId = openRouterConfig.selectedModel
    
    const visionModels = [
      { id: 'google/gemini-flash-1.5', name: 'Gemini Flash 1.5', provider: 'Google', tier: 'free' },
      { id: 'qwen/qwen-2-vl-7b-instruct', name: 'Qwen2-VL 7B', provider: 'Qwen', tier: 'free' },
      { id: 'meta-llama/llama-3.2-11b-vision-instruct', name: 'Llama 3.2 11B Vision', provider: 'Meta', tier: 'free' },
      { id: 'microsoft/phi-3.5-vision-instruct', name: 'Phi-3.5 Vision', provider: 'Microsoft', tier: 'free' },
      { id: 'openai/gpt-4o', name: 'GPT-4o', provider: 'OpenAI', tier: 'paid' },
      { id: 'openai/gpt-4o-mini', name: 'GPT-4o Mini', provider: 'OpenAI', tier: 'paid' },
      { id: 'anthropic/claude-3.5-sonnet', name: 'Claude 3.5 Sonnet', provider: 'Anthropic', tier: 'paid' },
      { id: 'google/gemini-pro-1.5', name: 'Gemini Pro 1.5', provider: 'Google', tier: 'paid' },
      { id: 'anthropic/claude-3-5-haiku', name: 'Claude 3.5 Haiku', provider: 'Anthropic', tier: 'paid' }
    ]

    const model = visionModels.find(m => m.id === modelId)
    if (model) {
      return {
        name: model.name,
        provider: model.provider,
        tier: model.tier,
        isCustom: false
      }
    }

    // Handle custom model
    if (openRouterConfig.customModel && modelId === openRouterConfig.customModel) {
      return {
        name: modelId.split('/').pop() || modelId,
        provider: modelId.split('/')[0] || 'Custom',
        tier: 'paid',
        isCustom: true
      }
    }

    // Fallback for unknown models
    return {
      name: modelId.split('/').pop() || modelId,
      provider: modelId.split('/')[0] || 'Unknown',
      tier: 'paid',
      isCustom: false
    }
  }
  const [showModelSelector, setShowModelSelector] = useState(false)
  const [showEnhancedSettings, setShowEnhancedSettings] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleAnalyze = () => {
    if (selectedImage) {
      onAnalyze(selectedImage.id, openRouterConfig.selectedModel)
    }
  }

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Handle file upload for OCR analysis
      // This would typically upload the file and then analyze it
      console.log('File uploaded:', file.name)
    }
  }

  const handleSettingsChange = (settings: any) => {
    // Settings are now managed globally through useSettingsStandalone
    console.log('OCR settings updated:', settings)
  }

  const handleSave = async (data: OCRResult) => {
    try {
      setIsSaving(true)
      await onSave(data)
      // Refresh saved data after successful save
      if (selectedImage) {
        loadSavedData()
      }
    } catch (error) {
      // Error handling is managed by parent component
      console.error('Save error in OCRPanel:', error)
    } finally {
      setIsSaving(false)
    }
  }

  // Save updated TXT content
  const saveTxtContent = async () => {
    if (!selectedImage || !savedData) return

    setSavingTxtContent(true)
    try {
      const response = await fetch(`/api/ocr/saved-data/${selectedImage.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          txtContent: editableTxtContent
        })
      })

      if (response.ok) {
        const result = await response.json()
        if (result.success) {
          // Update local saved data
          setSavedData((prev: any) => ({
            ...prev,
            txtContent: editableTxtContent
          }))
        }
      }
    } catch (error) {
      console.error('Error saving TXT content:', error)
    } finally {
      setSavingTxtContent(false)
    }
  }

  // Copy to clipboard functionality
  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  if (!selectedImage && !ocrResult) {
    return (
      <Card className={cn("h-full", className)}>
        <CardContent className="flex items-center justify-center h-full text-muted-foreground">
          <div className="text-center">
            <Eye className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p className="mb-2">No image selected</p>
            <p className="text-sm">Select an image to analyze with OCR</p>
            <div className="mt-4">
              <Button 
                variant="outline" 
                onClick={() => fileInputRef.current?.click()}
              >
                <FileText className="w-4 h-4 mr-2" />
                Upload Image
              </Button>
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileUpload}
                className="hidden"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("h-full w-full min-w-[440px] mr-4", className)}>
      <CardHeader className="pb-3 pr-6">
        <div className="flex flex-col space-y-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">OCR Analysis</CardTitle>
          </div>
          
          {/* Current Model Display */}
          <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg border border-muted-foreground/20">
            <span className="text-sm font-medium text-muted-foreground">Model:</span>
            {(() => {
              const modelDisplay = getCurrentModelDisplay()
              return (
                <div className="flex items-center gap-2 flex-1">
                  <Badge variant={modelDisplay.tier === 'free' ? 'default' : 'outline'} className="text-xs font-medium">
                    {modelDisplay.name}
                  </Badge>
                  <Badge variant="secondary" className="text-xs">
                    {modelDisplay.provider}
                  </Badge>
                  {modelDisplay.tier === 'free' && (
                    <Badge variant="success" className="text-xs">
                      FREE
                    </Badge>
                  )}
                  {modelDisplay.isCustom && (
                    <Badge variant="info" className="text-xs">
                      CUSTOM
                    </Badge>
                  )}
                  <div className="flex items-center gap-2 ml-auto">
                    <div className={cn(
                      "h-3 w-3 rounded-full transition-all duration-300",
                      connectionStatus === 'connected' 
                        ? 'bg-green-500 shadow-lg shadow-green-500/60 ring-2 ring-green-500/30' 
                        : connectionStatus === 'failed' 
                        ? 'bg-red-500 shadow-lg shadow-red-500/60 ring-2 ring-red-500/30' 
                        : 'bg-gray-400 dark:bg-gray-600'
                    )} title={`Connection: ${connectionStatus}`} />
                    <span className={cn(
                      "text-xs font-medium transition-colors",
                      connectionStatus === 'connected' 
                        ? 'text-green-600 dark:text-green-400' 
                        : connectionStatus === 'failed' 
                        ? 'text-red-600 dark:text-red-400' 
                        : 'text-gray-500'
                    )}>
                      {connectionStatus === 'connected' ? 'Connected' : 
                       connectionStatus === 'failed' ? 'Failed' : 'Connecting...'}
                    </span>
                  </div>
                </div>
              )
            })()}
          </div>
          
          <div className="grid grid-cols-3 gap-2">
            <Dialog open={showModelSelector} onOpenChange={setShowModelSelector}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="hover:bg-muted/60 transition-colors">
                  <Bot className="w-4 h-4 mr-1" />
                  Model
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Select AI Model</DialogTitle>
                </DialogHeader>
                <OCRModelSelector
                  selectedModel={openRouterConfig.selectedModel}
                  onModelChange={async (modelId: string) => {
                    // Update global settings
                    const updatedConfig = { ...openRouterConfig, selectedModel: modelId }
                    try {
                      await saveOpenRouterConfig(updatedConfig)
                      setShowModelSelector(false)
                    } catch (error) {
                      console.error('Failed to save model selection:', error)
                    }
                  }}
                  onConfigChange={(config) => {
                    console.log('Config changed:', config);
                  }}
                />
              </DialogContent>
            </Dialog>
            
            <Button 
              variant="outline" 
              size="sm" 
              className="hover:bg-muted/60 transition-colors"
              onClick={() => setShowEnhancedSettings(true)}
            >
              <Settings className="w-4 h-4 mr-1" />
              Settings
            </Button>
            
            {/* Test Connection Button */}
            <Button 
              variant="outline" 
              size="sm" 
              className="hover:bg-muted/60 transition-colors"
              onClick={async () => {
                try {
                  await testConnection()
                } catch (error) {
                  console.error('Test connection failed:', error)
                }
              }}
            >
              <CheckCircle className="w-4 h-4 mr-1" />
              Test
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4 pr-6">
        {selectedImage && (
          <div>
            <h4 className="font-medium mb-2">Selected Image</h4>
            
            {/* Document Type Selector - positioned before analysis */}
            <div className="mb-3">
              <div className="flex items-center gap-2 mb-2">
                <span className="text-sm text-muted-foreground">Document Type:</span>
              </div>
              <Select 
                value={documentType} 
                onValueChange={(value) => {
                  setDocumentType(value as any)
                }}
              >
                <SelectTrigger className="w-full h-9 text-sm bg-muted/30 border-muted-foreground/20">
                  <SelectValue placeholder="Select document type" />
                </SelectTrigger>
                <SelectContent>
                  {getDocumentTypeOptions().map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <span className="text-sm">{option.icon}</span>
                        <span>{option.label}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center gap-3 p-3 bg-muted rounded-lg">
              <Image 
                src={selectedImage.thumbnailUrl} 
                alt={selectedImage.filename}
                width={48}
                height={48}
                className="w-12 h-12 rounded object-cover"
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium truncate">{selectedImage.filename}</p>
                <p className="text-xs text-muted-foreground truncate">{selectedImage.relativePath}</p>
              </div>
              {/* Only show main Analyze button when no OCR results exist */}
              {!ocrResult ? (
                <Button 
                  onClick={handleAnalyze}
                  disabled={isProcessing}
                  size="sm"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Eye className="w-4 h-4 mr-2" />
                      Analyze
                    </>
                  )}
                </Button>
              ) : (
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => onAnalyze(selectedImage.id, undefined, true)}
                  disabled={isProcessing}
                  title="Re-run OCR analysis"
                >
                  {isProcessing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                      Re-analyzing...
                    </>
                  ) : (
                    <>
                      <Eye className="w-4 h-4 mr-2" />
                      Re-analyze
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        )}

        {isProcessing && (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4" />
            <p className="text-sm text-muted-foreground">Analyzing image with AI...</p>
            <p className="text-xs text-muted-foreground mt-1">This may take up to 30 seconds</p>
          </div>
        )}

        {ocrResult && !isProcessing && (
          <div>
            <div className="space-y-3 mb-4">
              <div className="flex items-center gap-2">
                <h4 className="font-medium">OCR Results</h4>
                {ocrResult.cached && (
                  <Badge variant="secondary" className="text-xs">
                    <Save className="h-3 w-3 mr-1" />
                    Cached
                  </Badge>
                )}
              </div>
            </div>
            
            <div className="flex items-center justify-between mb-3">
              <div className="flex gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setShowRawText(!showRawText)}
                >
                  {showRawText ? 'Hide' : 'Show'} Raw Text
                </Button>
                
                {/* Saved Data Viewer Button */}
                {savedData && (savedData.hasTxtFile || savedData.hasJsonFile) && (
                  <Dialog open={showSavedDataViewer} onOpenChange={setShowSavedDataViewer}>
                    <DialogTrigger asChild>
                      <Button 
                        variant="outline" 
                        size="sm"
                        className="flex items-center gap-2"
                      >
                        <FolderOpen className="h-4 w-4" />
                        View Saved Data
                        {savedData.hasTxtFile && (
                          <Badge variant="secondary" className="text-xs ml-1">
                            TXT
                          </Badge>
                        )}
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-4xl w-[90vw] max-h-[90vh] overflow-hidden">
                      <DialogHeader>
                        <DialogTitle className="flex items-center gap-2">
                          <FileText className="h-5 w-5" />
                          Saved OCR Data
                        </DialogTitle>
                      </DialogHeader>
                      
                      <div className="space-y-4 max-h-[70vh] overflow-y-auto">
                        {/* File Path Information */}
                        <div className="space-y-3">
                          {savedData.txtPath && (
                            <div className="p-3 bg-muted/40 rounded-lg border">
                              <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-2">
                                <div className="text-sm font-medium text-muted-foreground flex-shrink-0">
                                  TXT File Path:
                                </div>
                                <div className="flex flex-col xs:flex-row items-stretch xs:items-center gap-2 min-w-0">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => copyToClipboard(savedData.txtPath)}
                                    className="flex items-center gap-2 h-8 px-3 text-xs whitespace-nowrap flex-shrink-0"
                                  >
                                    {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                                    Copy Path
                                  </Button>
                                </div>
                              </div>
                              <div className="text-sm font-mono text-foreground bg-background px-3 py-2 rounded border border-border break-all">
                                {savedData.txtPath}
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Editable TXT Content */}
                        {savedData.hasTxtFile && (
                          <div className="space-y-3">
                            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                              <h4 className="text-base font-semibold flex-shrink-0">TXT File Content (Editable)</h4>
                              <div className="flex flex-col xs:flex-row items-stretch xs:items-center gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => copyToClipboard(editableTxtContent)}
                                  className="flex items-center gap-2 h-8 px-3 text-xs whitespace-nowrap"
                                >
                                  {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                                  Copy Content
                                </Button>
                                <Button
                                  variant="default"
                                  size="sm"
                                  onClick={saveTxtContent}
                                  disabled={savingTxtContent}
                                  className="flex items-center gap-2 h-8 px-3 text-xs whitespace-nowrap"
                                >
                                  {savingTxtContent ? (
                                    <>
                                      <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white" />
                                      Saving...
                                    </>
                                  ) : (
                                    <>
                                      <Save className="h-3 w-3" />
                                      Save Changes
                                    </>
                                  )}
                                </Button>
                              </div>
                            </div>
                            <textarea
                              value={editableTxtContent}
                              onChange={(e) => setEditableTxtContent(e.target.value)}
                              className="w-full h-64 p-4 text-sm font-mono bg-background border border-border rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-ring"
                              placeholder="No TXT content available"
                            />
                          </div>
                        )}

                        {/* JSON Data Display (Read-only) */}
                        {savedData.hasJsonFile && savedData.jsonContent && (
                          <div className="space-y-3">
                            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                              <h4 className="text-base font-semibold flex-shrink-0">JSON Data (Read-only)</h4>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyToClipboard(JSON.stringify(savedData.jsonContent, null, 2))}
                                className="flex items-center gap-2 h-8 px-3 text-xs whitespace-nowrap"
                              >
                                {copied ? <Check className="h-3 w-3" /> : <Copy className="h-3 w-3" />}
                                Copy JSON
                              </Button>
                            </div>
                            <pre className="text-sm font-mono bg-muted/30 p-4 rounded-lg border border-border overflow-auto max-h-64 whitespace-pre">
                              {JSON.stringify(savedData.jsonContent, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            </div>
            
            {ocrResult.cached && ocrResult.cacheTimestamp && (
              <div className="mb-3 p-2 bg-muted/50 rounded-md border border-muted-foreground/20">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <AlertCircle className="h-4 w-4" />
                  <span>
                    Results loaded from cache (saved {new Date(ocrResult.cacheTimestamp).toLocaleString()})
                  </span>
                </div>
              </div>
            )}
            
            {ocrResult.qualityWarnings && ocrResult.qualityWarnings.length > 0 && (
              <div className="mb-3 p-3 bg-amber-50 rounded-md border border-amber-200">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-4 w-4 text-amber-600 flex-shrink-0 mt-0.5" />
                  <div className="flex-1">
                    <div className="text-sm font-medium text-amber-800 mb-1">
                      Data Quality Alert
                    </div>
                    {ocrResult.qualityWarnings.map((warning, index) => (
                      <div key={index} className="text-sm text-amber-700 mb-1 last:mb-0">
                        <span className="font-medium">{warning.field}:</span> {warning.message}
                      </div>
                    ))}
                    <div className="text-xs text-amber-600 mt-2">
                      Try re-analyzing the image for improved extraction
                    </div>
                  </div>
                </div>
              </div>
            )}

            {showRawText && (
              <div className="mb-4 p-3 bg-muted rounded-lg">
                <h5 className="text-sm font-medium mb-2">Raw OCR Text</h5>
                <pre className="text-xs text-muted-foreground whitespace-pre-wrap">
                  {ocrResult.rawText}
                </pre>
              </div>
            )}

            <OCRForm
              initialData={{...ocrResult, mode: ocrMode}}
              onSave={handleSave}
              onCancel={onCancel}
              documentType={documentType}
              isAustralianDocument={(documentType === 'auto_detect' ? countryMode === 'australian' : isAustralianDocumentType(documentType))}
              error={error}
              successMessage={successMessage}
              isSaving={isSaving}
            />
          </div>
        )}

        {/* ReadySearch Panel - Only show in Australian mode */}
        {currentMode === 'australian' && ocrResult && selectedImage && (
          <div className="mt-4">
            <ReadySearchPanel
              ocrResult={ocrResult}
              imageId={selectedImage.id}
            />
          </div>
        )}

        {!selectedImage && !ocrResult && (
          <div className="text-center py-8 text-muted-foreground">
            <AlertCircle className="h-8 w-8 mx-auto mb-2" />
            <p>Select an image to begin OCR analysis</p>
          </div>
        )}
      </CardContent>
      
      {/* Enhanced OCR Settings Dialog */}
      <EnhancedOCRSettings
        isOpen={showEnhancedSettings}
        onOpenChange={setShowEnhancedSettings}
        onSettingsChange={handleSettingsChange}
        currentModel={openRouterConfig.selectedModel}
      />
    </Card>
  )
}