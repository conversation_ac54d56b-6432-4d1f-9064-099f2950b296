@echo off
title DL Organizer - Windows Launcher

REM Check if PowerShell is available
powershell -Command "exit 0" >nul 2>&1
if %errorlevel% neq 0 (
    echo.
    echo [ERROR] PowerShell is required but not available!
    echo Please install PowerShell and try again.
    echo.
    pause
    exit /b 1
)

REM Check if launcher.ps1 exists
if not exist "launcher.ps1" (
    echo.
    echo [ERROR] launcher.ps1 not found in current directory!
    echo Please ensure you are running this from the project root.
    echo.
    pause
    exit /b 1
)

REM Launch PowerShell launcher
echo.
echo Starting DL Organizer PowerShell Launcher...
echo.
powershell -ExecutionPolicy Bypass -File "launcher.ps1"

REM Check PowerShell exit code
if %errorlevel% neq 0 (
    echo.
    echo [ERROR] PowerShell launcher failed to execute!
    echo Error code: %errorlevel%
    echo.
    echo Troubleshooting steps:
    echo  1. Ensure PowerShell execution policy allows scripts
    echo  2. Check that launcher.ps1 exists in the project directory
    echo  3. Run: Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
    echo.
)

pause
