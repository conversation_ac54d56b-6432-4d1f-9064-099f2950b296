@echo off
REM Quick Fix - Set Qwen CLI to use proper model
cd /d "C:\claude\dl-organizer"

REM Set the environment variables
set OPENAI_API_KEY=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
set OPENAI_BASE_URL=https://openrouter.ai/api/v1

REM Try to force the model via environment variable
set QWEN_MODEL=qwen/qwen3-coder

echo Testing qwen/qwen3-coder model...
qwen --model qwen/qwen3-coder --prompt "Just say 'Hello from Qwen3-Coder!' and nothing else"

echo.
echo If that worked, you can now just type: qwen
echo The model should be set to qwen/qwen3-coder
pause
