const { execSync } = require('child_process');

try {
  console.log('🔍 Running TypeScript check...');
  execSync('npx tsc --noEmit', { 
    stdio: 'inherit',
    cwd: process.cwd()
  });
  console.log('✅ TypeScript check passed!');
  
  console.log('\n🔍 Running ESLint...');
  execSync('npm run lint', { 
    stdio: 'inherit',
    cwd: process.cwd()
  });
  console.log('✅ ESLint check passed!');
  
  console.log('\n🔍 Running Next.js build...');
  execSync('npm run build', { 
    stdio: 'inherit',
    cwd: process.cwd()
  });
  console.log('✅ Build successful!');
  
} catch (error) {
  console.error('❌ Build failed with error:', error.message);
  process.exit(1);
}