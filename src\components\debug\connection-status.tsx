'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  RefreshCw, 
  Monitor, 
  Wifi,
  Settings
} from 'lucide-react'
import { testBackendConnectivity, logPortConfig } from '@/utils/port-config'
import { detectExtensions, resolveExtensionConflicts } from '@/app/browser-compatibility'

interface ConnectionStatus {
  connected: boolean
  port: number
  url: string
  responseTime: number
  status?: number
  statusText?: string
  error?: string
}

interface ExtensionInfo {
  hasMetaMask: boolean
  hasNanoDefender: boolean
  hasUSOAwDx: boolean
  hasUnstoppableDomains: boolean
  hasChromeExtensions: boolean
  hasWebExtensionPolyfill: boolean
}

export function ConnectionStatus() {
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null)
  const [extensionInfo, setExtensionInfo] = useState<ExtensionInfo | null>(null)
  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [lastTestTime, setLastTestTime] = useState<Date | null>(null)
  const [showDetails, setShowDetails] = useState(false)

  // Test backend connectivity
  const testConnection = async () => {
    setIsTestingConnection(true)
    try {
      const status = await testBackendConnectivity()
      setConnectionStatus(status)
      setLastTestTime(new Date())
      
      // Also log port configuration for debugging
      await logPortConfig()
    } catch (error) {
      console.error('Connection test failed:', error)
      setConnectionStatus({
        connected: false,
        port: 0,
        url: 'unknown',
        responseTime: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      setLastTestTime(new Date())
    } finally {
      setIsTestingConnection(false)
    }
  }

  // Detect browser extensions
  const checkExtensions = () => {
    try {
      const extensions = detectExtensions()
      // Ensure all required properties are present
      const extensionInfo: ExtensionInfo = {
        hasMetaMask: extensions.hasMetaMask || false,
        hasNanoDefender: extensions.hasNanoDefender || false,
        hasUSOAwDx: extensions.hasUSOAwDx || false,
        hasUnstoppableDomains: extensions.hasUnstoppableDomains || false,
        hasChromeExtensions: extensions.hasChromeExtensions || false,
        hasWebExtensionPolyfill: extensions.hasWebExtensionPolyfill || false
      }
      setExtensionInfo(extensionInfo)
      console.log('🔍 Browser extensions detected:', extensionInfo)
    } catch (error) {
      console.error('Extension detection failed:', error)
      setExtensionInfo({
        hasMetaMask: false,
        hasNanoDefender: false,
        hasUSOAwDx: false,
        hasUnstoppableDomains: false,
        hasChromeExtensions: false,
        hasWebExtensionPolyfill: false
      })
    }
  }

  // Manual extension conflict resolution
  const handleExtensionConflicts = () => {
    try {
      const resolved = resolveExtensionConflicts()
      console.log(resolved ? '✅ Extension conflicts resolved' : '🔧 Extension conflicts handled')
      checkExtensions() // Refresh extension info
    } catch (error) {
      console.error('Extension conflict resolution failed:', error)
    }
  }

  // Initial checks
  useEffect(() => {
    testConnection()
    checkExtensions()
    
    // Set up periodic connection testing
    const interval = setInterval(() => {
      if (!isTestingConnection) {
        testConnection()
      }
    }, 30000) // Test every 30 seconds

    return () => clearInterval(interval)
  }, [])

  const getStatusIcon = (status: ConnectionStatus | null) => {
    if (!status) return <Monitor className="h-4 w-4" />
    if (status.connected) return <CheckCircle className="h-4 w-4" />
    return <XCircle className="h-4 w-4" />
  }

  const extensionCount = extensionInfo ? 
    Object.values(extensionInfo).filter(Boolean).length : 0

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Wifi className="h-5 w-5" />
            Connection Status
          </CardTitle>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={testConnection}
              disabled={isTestingConnection}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${isTestingConnection ? 'animate-spin' : ''}`} />
              Test
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDetails(!showDetails)}
            >
              <Settings className="h-4 w-4 mr-1" />
              {showDetails ? 'Hide' : 'Details'}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Backend Connection Status */}
        <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
          <div className="flex items-center gap-3">
            {getStatusIcon(connectionStatus)}
            <div>
              <p className="font-medium">Backend Connection</p>
              <p className="text-sm text-muted-foreground">
                {connectionStatus?.url || 'Unknown'} 
                {connectionStatus?.responseTime ? ` (${connectionStatus.responseTime}ms)` : ''}
              </p>
            </div>
          </div>
          <Badge 
            variant={connectionStatus?.connected ? "default" : "destructive"}
            className={`
              ${connectionStatus?.connected 
                ? 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-100 dark:border-green-800' 
                : 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-100 dark:border-red-800'
              }
            `}
          >
            {connectionStatus?.connected ? 'Connected' : 'Disconnected'}
          </Badge>
        </div>

        {/* Browser Extensions */}
        <div className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
          <div className="flex items-center gap-3">
            <Monitor className="h-4 w-4" />
            <div>
              <p className="font-medium">Browser Extensions</p>
              <p className="text-sm text-muted-foreground">
                {extensionCount} extension{extensionCount !== 1 ? 's' : ''} detected
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Badge variant="outline">
              {extensionCount} detected
            </Badge>
            {extensionCount > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleExtensionConflicts}
              >
                Fix Conflicts
              </Button>
            )}
          </div>
        </div>

        {/* Error Display */}
        {connectionStatus?.error && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <strong>Connection Error:</strong> {connectionStatus.error}
            </AlertDescription>
          </Alert>
        )}

        {/* Detailed Information */}
        {showDetails && (
          <div className="space-y-3">
            <div className="border-t pt-3">
              <h4 className="font-medium mb-2">Backend Details</h4>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>Port: {connectionStatus?.port || 'Unknown'}</div>
                <div>Status: {connectionStatus?.status || 'Unknown'}</div>
                <div>Response Time: {connectionStatus?.responseTime || 0}ms</div>
                <div>Last Test: {lastTestTime?.toLocaleTimeString() || 'Never'}</div>
              </div>
            </div>

            {extensionInfo && (
              <div className="border-t pt-3">
                <h4 className="font-medium mb-2">Extension Details</h4>
                <div className="grid grid-cols-2 gap-1 text-sm">
                  <div className={extensionInfo.hasMetaMask ? 'text-orange-600' : 'text-muted-foreground'}>
                    MetaMask: {extensionInfo.hasMetaMask ? 'Detected' : 'Not found'}
                  </div>
                  <div className={extensionInfo.hasNanoDefender ? 'text-blue-600' : 'text-muted-foreground'}>
                    Nano Defender: {extensionInfo.hasNanoDefender ? 'Detected' : 'Not found'}
                  </div>
                  <div className={extensionInfo.hasUSOAwDx ? 'text-purple-600' : 'text-muted-foreground'}>
                    USO-aw-dx: {extensionInfo.hasUSOAwDx ? 'Detected' : 'Not found'}
                  </div>
                  <div className={extensionInfo.hasUnstoppableDomains ? 'text-indigo-600' : 'text-muted-foreground'}>
                    Unstoppable: {extensionInfo.hasUnstoppableDomains ? 'Detected' : 'Not found'}
                  </div>
                  <div className={extensionInfo.hasChromeExtensions ? 'text-green-600' : 'text-muted-foreground'}>
                    Chrome API: {extensionInfo.hasChromeExtensions ? 'Available' : 'Not available'}
                  </div>
                  <div className={extensionInfo.hasWebExtensionPolyfill ? 'text-yellow-600' : 'text-muted-foreground'}>
                    WebExt Polyfill: {extensionInfo.hasWebExtensionPolyfill ? 'Detected' : 'Not found'}
                  </div>
                </div>
              </div>
            )}

            <div className="border-t pt-3 text-xs text-muted-foreground">
              <p>
                Connection tests run automatically every 30 seconds. 
                Extension conflicts are handled automatically on page load.
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}