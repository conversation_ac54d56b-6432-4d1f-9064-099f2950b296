// Debug script to check port configuration
const config = require('./scripts/port-config.js');

console.log('=== Debug Port Configuration ===');
console.log('Environment Variables:');
console.log('  FRONTEND_PORT:', process.env.FRONTEND_PORT);
console.log('  BACKEND_PORT:', process.env.BACKEND_PORT);
console.log('  NGROK_PORT:', process.env.NGROK_PORT);
console.log('');
console.log('Port Config Values:');
console.log('  Frontend:', config.getFrontendPort());
console.log('  Backend:', config.getBackendPort());
console.log('  Backend URL:', config.getBackendUrl());
console.log('');
console.log('DEFAULT_PORTS:', config.DEFAULT_PORTS);
console.log('PORTS:', config.PORTS);