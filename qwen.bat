@echo off
REM Qwen Launcher - Quick Start
REM Easy access to Qwen with proper model configuration

cd /d "C:\claude\dl-organizer"

if "%1"=="help" goto :help
if "%1"=="--help" goto :help
if "%1"=="-h" goto :help
if "%1"=="models" goto :models
if "%1"=="settings" goto :settings

echo.
echo Qwen Launcher - Quick Start
echo ============================
echo.

REM Default to interactive mode with qwen/qwen3-coder
if "%1"=="" (
    echo Starting Qwen with model: qwen/qwen3-coder
    echo.
    powershell -ExecutionPolicy Bypass -File "qwen-launcher.ps1" -Model "qwen/qwen3-coder" -Interactive
    goto :end
)

REM Pass all arguments to PowerShell launcher
powershell -ExecutionPolicy Bypass -File "qwen-launcher.ps1" %*
goto :end

:help
echo.
echo Qwen Launcher - Quick Commands
echo ================================
echo.
echo   qwen                    - Start interactive mode with qwen/qwen3-coder
echo   qwen models             - Show available models
echo   qwen settings           - Open settings menu
echo   qwen help               - Show this help
echo.
echo   Advanced PowerShell options:
echo   qwen-launcher.ps1 -Model "qwen/qwen3-coder" -Prompt "Hello"
echo   qwen-launcher.ps1 -ListModels
echo   qwen-launcher.ps1 -Settings
echo.
goto :end

:models
powershell -ExecutionPolicy Bypass -File "qwen-launcher.ps1" -ListModels
goto :end

:settings
powershell -ExecutionPolicy Bypass -File "qwen-launcher.ps1" -Settings
goto :end

:end
