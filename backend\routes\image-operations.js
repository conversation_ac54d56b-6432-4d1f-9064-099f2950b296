const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');
const { promisify } = require('util');
const sharp = require('sharp');
const logger = require('../utils/logger');
const SecurityMiddleware = require('../middleware/security');
const ErrorHandler = require('../middleware/error-handler');
const ImageProcessor = require('../utils/image-processor');

// Initialize image processor
const imageProcessor = new ImageProcessor();

// Thumbnail generation endpoint
router.get('/:encodedPath/thumbnail', ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { encodedPath } = req.params;
    
    // Decode image path from base64
    const imagePath = Buffer.from(encodedPath, 'base64').toString();
    const validatedPath = SecurityMiddleware.validatePath(imagePath);
    
    // Verify file exists
    const stats = await fs.stat(validatedPath);
    if (!stats.isFile()) {
      return res.status(404).json({ success: false, error: 'Image file not found' });
    }
    
    // Generate thumbnail
    const result = await imageProcessor.generateThumbnail(validatedPath);
    
    if (result && result.path) {
      // Send the thumbnail file
      res.setHeader('Content-Type', 'image/jpeg');
      res.setHeader('Cache-Control', 'public, max-age=3600');
      const thumbnailBuffer = await fs.readFile(result.path);
      res.send(thumbnailBuffer);
    } else {
      res.status(500).json({ success: false, error: 'Failed to generate thumbnail' });
    }
    
  } catch (error) {
    logger.error('Error generating thumbnail:', error);
    res.status(500).json({ success: false, error: 'Failed to generate thumbnail' });
  }
}));

// Preview generation endpoint
router.get('/:encodedPath/preview', ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { encodedPath } = req.params;
    
    // Decode image path from base64
    const imagePath = Buffer.from(encodedPath, 'base64').toString();
    const validatedPath = SecurityMiddleware.validatePath(imagePath);
    
    // Verify file exists
    const stats = await fs.stat(validatedPath);
    if (!stats.isFile()) {
      return res.status(404).json({ success: false, error: 'Image file not found' });
    }
    
    // Generate preview
    const result = await imageProcessor.generatePreview(validatedPath);
    
    if (result && result.path) {
      // Send the preview file
      res.setHeader('Content-Type', 'image/jpeg');
      res.setHeader('Cache-Control', 'public, max-age=3600');
      const previewBuffer = await fs.readFile(result.path);
      res.send(previewBuffer);
    } else {
      res.status(500).json({ success: false, error: 'Failed to generate preview' });
    }
    
  } catch (error) {
    logger.error('Error generating preview:', error);
    res.status(500).json({ success: false, error: 'Failed to generate preview' });
  }
}));

// Image rotation endpoint
router.post('/:encodedPath/rotate', SecurityMiddleware.validateRequestBody(['degrees']), ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { encodedPath } = req.params;
    const { degrees } = req.body;
    
    // Decode image path from base64
    const imagePath = Buffer.from(encodedPath, 'base64').toString();
    const validatedPath = SecurityMiddleware.validatePath(imagePath);
    
    // Verify file exists
    const stats = await fs.stat(validatedPath);
    if (!stats.isFile()) {
      return res.status(404).json({ success: false, error: 'Image file not found' });
    }
    
    // Validate degrees parameter
    if (typeof degrees !== 'number' || degrees % 90 !== 0) {
      return res.status(400).json({ success: false, error: 'Degrees must be a multiple of 90' });
    }
    
    // Get database connection
    const Database = require('../config/database');
    const db = Database.getInstance();
    
    // Get current rotation from database
    const currentRotationQuery = `SELECT rotation FROM images WHERE path = ?`;
    const currentRotationResult = await new Promise((resolve, reject) => {
      db.get(currentRotationQuery, [validatedPath], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });
    
    const currentRotation = currentRotationResult ? currentRotationResult.rotation : 0;
    const newRotation = (currentRotation + degrees) % 360;
    
    // Normalize negative rotations to positive (e.g., -90 becomes 270)
    const normalizedRotation = newRotation < 0 ? newRotation + 360 : newRotation;
    
    // Rotate the image
    const result = await imageProcessor.rotateImage(validatedPath, degrees);
    
    if (result && result.success) {
      // Update database with new cumulative rotation
      const updateRotationQuery = `UPDATE images SET rotation = ? WHERE path = ?`;
      await new Promise((resolve, reject) => {
        db.run(updateRotationQuery, [normalizedRotation, validatedPath], function(err) {
          if (err) {
            logger.warn('Failed to update rotation in database:', err);
            // Don't fail the request if DB update fails, but log it
          }
          resolve();
        });
      });
      
      // Regenerate thumbnail & preview (force) and gather fresh metadata
      const thumbnail = await imageProcessor.generateThumbnail(validatedPath, { forceRegenerate: true });
      const preview = await imageProcessor.generatePreview(validatedPath, { forceRegenerate: true });
      const updatedStats = await fs.stat(validatedPath);
      const imageId = Buffer.from(validatedPath).toString('base64');

      res.json({ 
        success: true, 
        message: 'Image rotated successfully', 
        image: {
          id: imageId,
          path: validatedPath,
          thumbnailUrl: thumbnail.url,
          previewUrl: preview.url,
          lastModified: updatedStats.mtime,
          rotation: normalizedRotation // Return cumulative rotation
        }
      });
    } else {
      res.status(500).json({ success: false, error: 'Failed to rotate image' });
    }
    
  } catch (error) {
    logger.error('Error rotating image:', error);
    res.status(500).json({ success: false, error: 'Failed to rotate image' });
  }
}));

// Single image rename endpoint
router.post('/rename', SecurityMiddleware.validateRequestBody(['imageId', 'newName']), ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { imageId, newName } = req.body;
    
    // Decode image path from base64 ID
    const imagePath = Buffer.from(imageId, 'base64').toString();
    const validatedPath = SecurityMiddleware.validatePath(imagePath);
    
    // Verify file exists
    const stats = await fs.stat(validatedPath);
    if (!stats.isFile()) {
      return res.status(404).json({ success: false, error: 'Image file not found' });
    }
    
    // Sanitize new filename
    const sanitizedName = SecurityMiddleware.sanitizeText(newName);
    if (!sanitizedName || sanitizedName.trim() === '') {
      return res.status(400).json({ success: false, error: 'Invalid filename' });
    }
    
    // Get directory and create new path
    const directory = path.dirname(validatedPath);
    const extension = path.extname(validatedPath);
    const nameWithoutExt = sanitizedName.replace(/\.[^/.]+$/, ''); // Remove extension if provided
    const newPath = path.join(directory, nameWithoutExt + extension);
    
    // Check if new filename already exists
    try {
      await fs.stat(newPath);
      return res.status(400).json({ success: false, error: 'A file with this name already exists' });
    } catch (error) {
      // File doesn't exist, which is what we want
    }
    
    // Rename the file
    await fs.rename(validatedPath, newPath);
    
    // Generate new image ID for the renamed file
    const newImageId = Buffer.from(newPath).toString('base64');
    
    logger.info('Image renamed successfully', { 
      originalPath: validatedPath, 
      newPath: newPath,
      originalId: imageId,
      newId: newImageId
    });
    
    res.json({ 
      success: true, 
      message: 'Image renamed successfully',
      newImageId: newImageId,
      newPath: newPath,
      newFilename: path.basename(newPath)
    });
  } catch (error) {
    logger.error('Error renaming image', { error: error.message, imageId: req.body.imageId });
    throw error;
  }
}));

// Batch image rename endpoint
router.post('/batch-rename', SecurityMiddleware.validateRequestBody(['renamingPairs']), ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { renamingPairs } = req.body;
    
    if (!Array.isArray(renamingPairs) || renamingPairs.length === 0) {
      return res.status(400).json({ success: false, error: 'renamingPairs must be a non-empty array' });
    }
    
    const results = [];
    const errors = [];
    
    // Validate all operations first
    const validatedOperations = [];
    for (const pair of renamingPairs) {
      try {
        const { imageId, newName } = pair;
        
        if (!imageId || !newName) {
          errors.push({ imageId, error: 'Missing imageId or newName' });
          continue;
        }
        
        // Decode image path from base64 ID
        const imagePath = Buffer.from(imageId, 'base64').toString();
        const validatedPath = SecurityMiddleware.validatePath(imagePath);
        
        // Verify file exists
        const stats = await fs.stat(validatedPath);
        if (!stats.isFile()) {
          errors.push({ imageId, error: 'Image file not found' });
          continue;
        }
        
        // Sanitize new filename
        const sanitizedName = SecurityMiddleware.sanitizeText(newName);
        if (!sanitizedName || sanitizedName.trim() === '') {
          errors.push({ imageId, error: 'Invalid filename' });
          continue;
        }
        
        // Get directory and create new path
        const directory = path.dirname(validatedPath);
        const extension = path.extname(validatedPath);
        const nameWithoutExt = sanitizedName.replace(/\.[^/.]+$/, ''); // Remove extension if provided
        const newPath = path.join(directory, nameWithoutExt + extension);
        
        // Check if new filename already exists
        try {
          await fs.stat(newPath);
          errors.push({ imageId, error: `A file with name '${path.basename(newPath)}' already exists` });
          continue;
        } catch (error) {
          // File doesn't exist, which is what we want
        }
        
        validatedOperations.push({
          imageId,
          originalPath: validatedPath,
          newPath,
          newName: sanitizedName
        });
        
      } catch (error) {
        errors.push({ imageId: pair.imageId, error: error.message });
      }
    }
    
    // Execute all validated rename operations
    for (const operation of validatedOperations) {
      try {
        await fs.rename(operation.originalPath, operation.newPath);
        
        const newImageId = Buffer.from(operation.newPath).toString('base64');
        
        results.push({
          originalImageId: operation.imageId,
          newImageId: newImageId,
          originalPath: operation.originalPath,
          newPath: operation.newPath,
          newFilename: path.basename(operation.newPath),
          success: true
        });
        
        logger.info('Image renamed in batch operation', { 
          originalPath: operation.originalPath, 
          newPath: operation.newPath,
          originalId: operation.imageId,
          newId: newImageId
        });
        
      } catch (error) {
        errors.push({ 
          imageId: operation.imageId, 
          error: `Failed to rename: ${error.message}` 
        });
        
        logger.error('Error in batch rename operation', { 
          error: error.message, 
          imageId: operation.imageId,
          originalPath: operation.originalPath,
          newPath: operation.newPath
        });
      }
    }
    
    const totalAttempted = renamingPairs.length;
    const successCount = results.length;
    const errorCount = errors.length;
    
    logger.info('Batch rename operation completed', {
      totalAttempted,
      successCount,
      errorCount
    });
    
    res.json({
      success: errorCount === 0,
      message: `Batch rename completed: ${successCount} successful, ${errorCount} errors`,
      results,
      errors,
      stats: {
        totalAttempted,
        successCount,
        errorCount
      }
    });
    
  } catch (error) {
    logger.error('Error in batch rename operation', { error: error.message });
    throw error;
  }
}));

// Move image to different folder endpoint
router.post('/move', SecurityMiddleware.validateRequestBody(['imageId', 'targetFolder']), ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { imageId, targetFolder, newName } = req.body;
    
    // Decode image path from base64 ID
    const imagePath = Buffer.from(imageId, 'base64').toString();
    const validatedPath = SecurityMiddleware.validatePath(imagePath);
    const validatedTargetFolder = SecurityMiddleware.validatePath(targetFolder);
    
    // Verify source file exists
    const stats = await fs.stat(validatedPath);
    if (!stats.isFile()) {
      return res.status(404).json({ success: false, error: 'Image file not found' });
    }
    
    // Verify target directory exists or create it
    try {
      const targetStats = await fs.stat(validatedTargetFolder);
      if (!targetStats.isDirectory()) {
        return res.status(400).json({ success: false, error: 'Target path is not a directory' });
      }
    } catch (error) {
      // Directory doesn't exist, create it
      await fs.mkdir(validatedTargetFolder, { recursive: true });
    }
    
    // Determine target filename
    const originalFilename = path.basename(validatedPath);
    const targetFilename = newName ? SecurityMiddleware.sanitizeText(newName) : originalFilename;
    const targetPath = path.join(validatedTargetFolder, targetFilename);
    
    // Check if target file already exists
    try {
      await fs.stat(targetPath);
      return res.status(400).json({ success: false, error: 'A file with this name already exists in target directory' });
    } catch (error) {
      // File doesn't exist, which is what we want
    }
    
    // Move the file
    await fs.rename(validatedPath, targetPath);
    
    const newImageId = Buffer.from(targetPath).toString('base64');
    
    logger.info('Image moved successfully', { 
      originalPath: validatedPath, 
      targetPath: targetPath,
      originalId: imageId,
      newId: newImageId
    });
    
    res.json({ 
      success: true, 
      message: 'Image moved successfully',
      newImageId: newImageId,
      newPath: targetPath,
      newFilename: path.basename(targetPath)
    });
    
  } catch (error) {
    logger.error('Error moving image', { error: error.message, imageId: req.body.imageId });
    throw error;
  }
}));

// Batch move images endpoint
router.post('/batch-move', SecurityMiddleware.validateRequestBody(['operations']), ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { operations } = req.body;
    
    if (!Array.isArray(operations) || operations.length === 0) {
      return res.status(400).json({ success: false, error: 'operations must be a non-empty array' });
    }
    
    const results = [];
    const errors = [];
    
    // Track created directories to avoid repeated creation attempts
    const createdDirectories = new Set();
    
    for (const operation of operations) {
      try {
        const { imageId, targetFolder, newName } = operation;
        
        if (!imageId || !targetFolder) {
          errors.push({ imageId, error: 'Missing imageId or targetFolder' });
          continue;
        }
        
        // Decode image path from base64 ID
        const imagePath = Buffer.from(imageId, 'base64').toString();
        const validatedPath = SecurityMiddleware.validatePath(imagePath);
        const validatedTargetFolder = SecurityMiddleware.validatePath(targetFolder);
        
        // Verify source file exists
        const stats = await fs.stat(validatedPath);
        if (!stats.isFile()) {
          errors.push({ imageId, error: 'Image file not found' });
          continue;
        }
        
        // Ensure target directory exists
        if (!createdDirectories.has(validatedTargetFolder)) {
          try {
            const targetStats = await fs.stat(validatedTargetFolder);
            if (!targetStats.isDirectory()) {
              errors.push({ imageId, error: 'Target path is not a directory' });
              continue;
            }
          } catch (error) {
            // Directory doesn't exist, create it
            await fs.mkdir(validatedTargetFolder, { recursive: true });
          }
          createdDirectories.add(validatedTargetFolder);
        }
        
        // Determine target filename
        const originalFilename = path.basename(validatedPath);
        const targetFilename = newName ? SecurityMiddleware.sanitizeText(newName) : originalFilename;
        const targetPath = path.join(validatedTargetFolder, targetFilename);
        
        // Check if target file already exists
        try {
          await fs.stat(targetPath);
          errors.push({ imageId, error: `File '${targetFilename}' already exists in target directory` });
          continue;
        } catch (error) {
          // File doesn't exist, which is what we want
        }
        
        // Move the file
        await fs.rename(validatedPath, targetPath);
        
        const newImageId = Buffer.from(targetPath).toString('base64');
        
        results.push({
          originalImageId: imageId,
          newImageId: newImageId,
          originalPath: validatedPath,
          targetPath: targetPath,
          targetFolder: validatedTargetFolder,
          newFilename: path.basename(targetPath),
          success: true
        });
        
        logger.info('Image moved in batch operation', { 
          originalPath: validatedPath, 
          targetPath: targetPath,
          originalId: imageId,
          newId: newImageId
        });
        
      } catch (error) {
        errors.push({ 
          imageId: operation.imageId, 
          error: `Failed to move: ${error.message}` 
        });
        
        logger.error('Error in batch move operation', { 
          error: error.message, 
          imageId: operation.imageId
        });
      }
    }
    
    const totalAttempted = operations.length;
    const successCount = results.length;
    const errorCount = errors.length;
    
    logger.info('Batch move operation completed', {
      totalAttempted,
      successCount,
      errorCount
    });
    
    res.json({
      success: errorCount === 0,
      message: `Batch move completed: ${successCount} successful, ${errorCount} errors`,
      results,
      errors,
      stats: {
        totalAttempted,
        successCount,
        errorCount
      }
    });
    
  } catch (error) {
    logger.error('Error in batch move operation', { error: error.message });
    throw error;
  }
}));

// Cache checking endpoint
router.get('/:encodedPath/cache', ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { encodedPath } = req.params;
    
    // Decode image path from base64
    const imagePath = Buffer.from(encodedPath, 'base64').toString();
    const validatedPath = SecurityMiddleware.validatePath(imagePath);
    
    // Check for OCR cache files
    const directory = path.dirname(validatedPath);
    const basename = path.basename(validatedPath, path.extname(validatedPath));
    
    // Check for various cache file patterns
    const cacheFiles = [
      `${basename}_ocr_results.json`,
      `${basename}_ocr_results.txt`,
      `${basename}.json`,
      `${basename}.txt`
    ];
    
    const availableVariations = [];
    let exists = false;
    
    for (const cacheFile of cacheFiles) {
      const cacheFilePath = path.join(directory, cacheFile);
      try {
        const stats = await fs.stat(cacheFilePath);
        if (stats.isFile()) {
          exists = true;
          availableVariations.push({
            filename: cacheFile,
            path: cacheFilePath,
            lastModified: stats.mtime,
            size: stats.size
          });
        }
      } catch (error) {
        // File doesn't exist, continue
      }
    }
    
    res.json({
      success: true,
      exists,
      availableVariations,
      imagePath: validatedPath
    });
    
  } catch (error) {
    logger.error('Error checking cache:', error);
    res.status(500).json({ success: false, error: 'Failed to check cache' });
  }
}));

module.exports = router;