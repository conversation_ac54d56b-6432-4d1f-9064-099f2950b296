# ReadySearch Project - Git Ignore

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test
.env.local
.env.production

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# VS Code
.vscode/

# IDE
.idea/

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/
launcher.log

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
*.egg-info/
.pytest_cache/
.coverage
htmlcov/

# Browser automation
downloads/
.playwright/
screenshots/
test-results/

# Application specific
readysearch_results/
readysearch_results.json
search_results.*
*.results
emergency_results/
ultra_verbose_results/

# File Management  
backups/
archive/data/*.json
archive/logs/*.log
archive/screenshots/*.png

# Temporary files
*.tmp
*.temp
temp/

# Build outputs
build/
dist/

# Security
*.key
*.pem
secrets/
