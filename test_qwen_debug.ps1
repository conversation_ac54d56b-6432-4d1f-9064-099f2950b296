#!/usr/bin/env pwsh
# Test Qwen CLI with proper OpenRouter configuration

Set-Location "C:\claude\dl-organizer"

# Set environment variables
$env:OPENAI_API_KEY = "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"
$env:OPENAI_BASE_URL = "https://openrouter.ai/api/v1"

Write-Host "🔧 Environment Configuration:" -ForegroundColor Yellow
Write-Host "API Key: $($env:OPENAI_API_KEY.Substring(0,20))..." -ForegroundColor Cyan
Write-Host "Base URL: $env:OPENAI_BASE_URL" -ForegroundColor Cyan
Write-Host ""

Write-Host "🧪 Testing Qwen CLI with debug output..." -ForegroundColor Yellow

# Test with debug to see what's happening
Write-Host "[TEST 1] qwen/qwen3-coder:free with debug:" -ForegroundColor Magenta
try {
    & qwen --debug --model "qwen/qwen3-coder:free" --prompt "Just say 'Hello from Qwen3-Coder Free!' and nothing else"
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}

Write-Host "`n[TEST 2] qwen/qwen3-coder (paid) with debug:" -ForegroundColor Magenta  
try {
    & qwen --debug --model "qwen/qwen3-coder" --prompt "Just say 'Hello from Qwen3-Coder Paid!' and nothing else"
} catch {
    Write-Host "Error: $_" -ForegroundColor Red
}

Write-Host "`n✅ Test complete!" -ForegroundColor Green
