#!/usr/bin/env node

/**
 * Smart Development Starter
 * 
 * Handles the sequential execution of port sync and development server start
 * in a cross-platform way to avoid Windows && operator issues.
 */

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Smart Development Server...');
console.log('📋 Step 1: Synchronizing ports...');

// First, run port synchronization
const portSync = spawn('node', ['scripts/port-sync-manager.js', 'sync'], {
    cwd: path.resolve(__dirname, '..'),
    stdio: 'inherit',
    shell: true
});

portSync.on('close', (code) => {
    if (code === 0) {
        console.log('✅ Port synchronization completed successfully');
        console.log('📋 Step 2: Starting development servers...');
        
        // Then start the development server
        const devServer = spawn('npm', ['run', 'dev'], {
            cwd: path.resolve(__dirname, '..'),
            stdio: 'inherit',
            shell: true
        });
        
        devServer.on('close', (devCode) => {
            console.log(`🏁 Development server exited with code ${devCode}`);
            process.exit(devCode);
        });
        
        devServer.on('error', (error) => {
            console.error('❌ Failed to start development server:', error);
            process.exit(1);
        });
        
    } else {
        console.warn('⚠️ Port synchronization had issues (code:', code, '), continuing anyway...');
        console.log('📋 Step 2: Starting development servers...');
        
        // Continue with dev server even if port sync had issues
        const devServer = spawn('npm', ['run', 'dev'], {
            cwd: path.resolve(__dirname, '..'),
            stdio: 'inherit',
            shell: true
        });
        
        devServer.on('close', (devCode) => {
            console.log(`🏁 Development server exited with code ${devCode}`);
            process.exit(devCode);
        });
        
        devServer.on('error', (error) => {
            console.error('❌ Failed to start development server:', error);
            process.exit(1);
        });
    }
});

portSync.on('error', (error) => {
    console.error('❌ Failed to run port synchronization:', error);
    console.log('📋 Fallback: Starting development servers without port sync...');
    
    // Fallback to direct dev server start
    const devServer = spawn('npm', ['run', 'dev'], {
        cwd: path.resolve(__dirname, '..'),
        stdio: 'inherit',
        shell: true
    });
    
    devServer.on('close', (devCode) => {
        console.log(`🏁 Development server exited with code ${devCode}`);
        process.exit(devCode);
    });
    
    devServer.on('error', (fallbackError) => {
        console.error('❌ Failed to start development server (fallback):', fallbackError);
        process.exit(1);
    });
});

// Handle Ctrl+C gracefully
process.on('SIGINT', () => {
    console.log('\n🛑 Received SIGINT, shutting down gracefully...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
    process.exit(0);
});