#!/usr/bin/env node
/**
 * VALIDATED STARTUP SCRIPT
 * 
 * This script ensures consistent port configuration before starting servers.
 * Run this instead of 'npm run dev' to avoid port conflicts.
 */

const { execSync, spawn } = require('child_process');
const { PORTS, isPortAvailable, getEnvironmentString } = require('./port-config.js');

async function validateAndStart() {
  console.log('🔍 Validating port configuration...');
  
  // Check if ports are available
  const frontendAvailable = await isPortAvailable(PORTS.frontend);
  const backendAvailable = await isPortAvailable(PORTS.backend);
  
  if (!frontendAvailable) {
    console.log(`⚠️  Frontend port ${PORTS.frontend} is in use. Checking for existing process...`);
    try {
      execSync(`taskkill /F /PID $(netstat -ano | findstr :${PORTS.frontend} | awk '{print $5}')`, { stdio: 'ignore' });
      console.log(`✅ Freed port ${PORTS.frontend}`);
    } catch (e) {
      console.log(`ℹ️  Port ${PORTS.frontend} conflict handled`);
    }
  }
  
  if (!backendAvailable) {
    console.log(`⚠️  Backend port ${PORTS.backend} is in use. Checking for existing process...`);
    try {
      execSync(`taskkill /F /PID $(netstat -ano | findstr :${PORTS.backend} | awk '{print $5}')`, { stdio: 'ignore' });
      console.log(`✅ Freed port ${PORTS.backend}`);
    } catch (e) {
      console.log(`ℹ️  Port ${PORTS.backend} conflict handled`);
    }
  }
  
  console.log('🚀 Starting DL Organizer with validated ports...');
  console.log(`   Frontend: http://localhost:${PORTS.frontend}`);
  console.log(`   Backend:  http://localhost:${PORTS.backend}`);
  
  // Set environment variables for child processes
  const env = {
    ...process.env,
    FRONTEND_PORT: PORTS.frontend.toString(),
    BACKEND_PORT: PORTS.backend.toString(),
    NGROK_PORT: PORTS.ngrok.toString(),
    PORT: PORTS.frontend.toString()
  };
  
  // Start both servers with validated environment
  const frontend = spawn('npm', ['run', 'dev:frontend'], { 
    stdio: 'inherit',
    env,
    shell: true
  });
  
  const backend = spawn('npm', ['run', 'dev:backend'], { 
    stdio: 'inherit',
    env,
    shell: true
  });
  
  // Handle cleanup
  process.on('SIGINT', () => {
    console.log('\\n🛑 Shutting down servers...');
    frontend.kill('SIGINT');
    backend.kill('SIGINT');
    process.exit(0);
  });
  
  frontend.on('exit', (code) => {
    console.log(`Frontend exited with code ${code}`);
  });
  
  backend.on('exit', (code) => {
    console.log(`Backend exited with code ${code}`);
  });
}

// Run validation and startup
validateAndStart().catch(console.error);