const EventEmitter = require('events');
const fs = require('fs').promises;
const path = require('path');
const cache = require('../utils/ocr-cache');
const { detectLicenseSide } = require('../utils/detect-license-side');
const { buildAnalysisManifest } = require('../utils/build-manifest');
const FileLogger = require('../utils/file-logger');

class SmartAnalyzer extends EventEmitter {
  constructor(ocrService) {
    super();
    this.ocrService = ocrService;
    this.jobs = new Map();
    this.isProcessing = false;
    this.maxConcurrentJobs = 2; // Lower than OCR batch processing
    this.activeJobs = 0;
    this.queue = [];
    this.logger = new FileLogger('smart-analyzer-debug.log');
    this.lastProcessingTime = null;
    this.processingTimeout = 5 * 60 * 1000; // 5 minutes timeout
    
    // Setup periodic health check to recover from stuck processing
    this.setupHealthCheck();
    
    this.logger.log('🔧 Smart Analyzer: Initialized with OCR service', { 
      ocrServiceExists: !!ocrService,
      maxConcurrentJobs: this.maxConcurrentJobs 
    });
  }

  async createAnalysisJob(options) {
    const job = {
      id: this.generateJobId(),
      status: 'pending',
      progress: 0,
      startTime: null,
      endTime: null,
      totalImages: options.images.length,
      processedImages: 0,
      manifest: null,
      error: null,
      images: options.images,
      folderPath: options.folderPath,
      forceRefresh: options.forceRefresh || false,
      stats: {
        sideCounts: { front: 0, back: 0, selfie: 0, unknown: 0 },
        filenameClusters: new Map(),
        sizeBuckets: new Map(),
        resBuckets: new Map(),
        imageIds: [],
        imageSides: new Map()
      }
    };

    this.logger.log(`📝 Smart Analyzer: Created job ${job.id} with ${job.totalImages} images`, {
      jobId: job.id,
      totalImages: job.totalImages,
      folderPath: options.folderPath,
      firstFewImages: options.images.slice(0, 3).map(img => ({ filename: img.filename, path: img.path }))
    });
    
    this.jobs.set(job.id, job);
    this.queue.push(job.id);
    
    this.logger.log(`📋 Smart Analyzer: Added job to queue`, {
      jobId: job.id,
      queueLength: this.queue.length,
      isProcessing: this.isProcessing,
      activeJobs: this.activeJobs
    });
    
    // Start processing if not already running
    if (!this.isProcessing) {
      this.logger.log(`🚀 Smart Analyzer: Starting queue processing for job ${job.id}`);
      this.processQueue();
    } else {
      // Check if processing might be stuck
      const timeSinceLastActivity = this.lastProcessingTime ? Date.now() - this.lastProcessingTime : 0;
      if (timeSinceLastActivity > 60000) { // 1 minute stuck check
        this.logger.log(`⚠️ Smart Analyzer: Processing may be stuck, forcing health check for job ${job.id}`, {
          timeSinceLastActivity,
          activeJobs: this.activeJobs,
          queueLength: this.queue.length
        });
        this.checkProcessingHealth();
      } else {
        this.logger.log(`⏳ Smart Analyzer: Queue processing already running, job ${job.id} will be processed when ready`);
      }
    }

    return job;
  }

  generateJobId() {
    return `sfa_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  setupHealthCheck() {
    // Check every 30 seconds for stuck processing
    setInterval(() => {
      this.checkProcessingHealth();
    }, 30000);
  }

  checkProcessingHealth() {
    if (this.isProcessing && this.lastProcessingTime) {
      const timeSinceLastActivity = Date.now() - this.lastProcessingTime;
      
      // If processing has been stuck for more than the timeout
      if (timeSinceLastActivity > this.processingTimeout) {
        this.logger.log('🚨 Smart Analyzer: Processing appears stuck, forcing recovery', {
          timeSinceLastActivity,
          processingTimeout: this.processingTimeout,
          activeJobs: this.activeJobs,
          queueLength: this.queue.length,
          isProcessing: this.isProcessing
        });
        
        // Force reset processing state
        this.isProcessing = false;
        this.activeJobs = 0;
        this.lastProcessingTime = null;
        
        // Mark any processing jobs as failed
        for (const [jobId, job] of this.jobs.entries()) {
          if (job.status === 'processing') {
            job.status = 'failed';
            job.error = 'Processing timeout - job was stuck';
            job.endTime = new Date().toISOString();
            
            this.emit('progress', {
              jobId: job.id,
              done: true,
              status: 'failed',
              error: job.error
            });
          }
        }
        
        // Restart processing if there are queued jobs
        if (this.queue.length > 0) {
          this.logger.log('🔄 Smart Analyzer: Restarting processing after recovery');
          this.processQueue();
        }
      }
    }
  }

  async processQueue() {
    this.logger.log(`🔄 Smart Analyzer: processQueue called`, {
      isProcessing: this.isProcessing,
      queueLength: this.queue.length,
      activeJobs: this.activeJobs,
      maxConcurrentJobs: this.maxConcurrentJobs
    });
    
    if (this.isProcessing || this.queue.length === 0) {
      this.logger.log(`⏸️ Smart Analyzer: Queue processing skipped`, {
        reason: this.isProcessing ? 'already running' : 'empty queue',
        isProcessing: this.isProcessing,
        queueLength: this.queue.length
      });
      return;
    }
    
    this.isProcessing = true;
    this.lastProcessingTime = Date.now(); // Track when processing started
    this.logger.log(`🚀 Smart Analyzer: Starting queue processing`);
    
    while (this.queue.length > 0 && this.activeJobs < this.maxConcurrentJobs) {
      const jobId = this.queue.shift();
      const job = this.jobs.get(jobId);
      
      this.logger.log(`📋 Smart Analyzer: Processing job from queue`, {
        jobId,
        jobExists: !!job,
        jobStatus: job?.status,
        queueLength: this.queue.length,
        activeJobs: this.activeJobs
      });
      
      if (job && job.status === 'pending') {
        this.logger.log(`▶️ Smart Analyzer: Starting async processing for job ${jobId}`);
        this.activeJobs++;
        // Don't await here - let it run asynchronously
        this.processAnalysisJob(job).catch(error => {
          this.logger.error(`❌ Smart Analyzer: Job processing error for ${jobId}`, error);
          this.activeJobs--;
          this.processQueue();
        });
      } else {
        this.logger.log(`⚠️ Smart Analyzer: Job not processable`, {
          jobId,
          jobExists: !!job,
          jobStatus: job?.status,
          reason: !job ? 'job not found' : 'job not pending'
        });
      }
    }
    
    // Check if we need to stop processing
    if (this.activeJobs === 0 && this.queue.length === 0) {
      this.logger.log(`🛑 Smart Analyzer: Queue processing complete - no active jobs or queued jobs`);
      this.isProcessing = false;
      this.lastProcessingTime = null; // Clear processing time when done
    }
  }

  async processAnalysisJob(job) {
    try {
      job.status = 'processing';
      job.startTime = new Date().toISOString();
      
      this.logger.log(`🔍 Smart Analyzer: Starting analysis job`, {
        jobId: job.id,
        totalImages: job.totalImages,
        folderPath: job.folderPath
      });
      
      for (let i = 0; i < job.images.length; i++) {
        const image = job.images[i];
        
        this.logger.log(`📷 Smart Analyzer: Processing image ${i + 1}/${job.totalImages}`, {
          jobId: job.id,
          imageIndex: i + 1,
          filename: image.filename,
          imagePath: image.path
        });
        
        try {
          // Check if job was cancelled
          if (job.status === 'cancelled') {
            this.logger.log(`⏹️ Smart Analyzer: Job ${job.id} was cancelled`);
            break;
          }
          
          // Analyze image
          await this.analyzeImage(image, job);
          
          job.processedImages++;
          job.progress = Math.round((job.processedImages / job.totalImages) * 100);
          
          // Emit progress update
          const progressData = {
            jobId: job.id,
            status: 'processing',
            progress: job.progress,
            processedImages: job.processedImages,
            totalImages: job.totalImages,
            currentImage: {
              id: image.id,
              filename: image.filename || path.basename(image.path)
            }
          };
          
          this.logger.log(`📊 Smart Analyzer: Emitting progress for job ${job.id}`, {
            progress: job.progress,
            processedImages: job.processedImages,
            totalImages: job.totalImages,
            currentFilename: image.filename
          });
          
          this.emit('progress', progressData);
          
          // Small delay to prevent overwhelming the system (reduced for faster processing)
          await this.sleep(10);
          
        } catch (error) {
          console.error(`❌ Smart Analyzer: Failed to analyze image ${image.id}:`, error);
          // Continue processing other images
        }
      }
      
      // Generate final manifest
      if (job.status === 'processing') {
        job.manifest = buildAnalysisManifest(job.stats);
        job.status = 'completed';
        job.endTime = new Date().toISOString();
        
        console.log(`✅ Smart Analyzer: Job ${job.id} completed successfully`);
        console.log(`📊 Smart Analyzer: Manifest summary:`, {
          totalImages: job.totalImages,
          sideCounts: job.stats.sideCounts,
          filenameClusters: job.stats.filenameClusters.size,
          sizeBuckets: job.stats.sizeBuckets.size,
          resBuckets: job.stats.resBuckets.size
        });
        
        // Send completion event
        this.emit('progress', {
          jobId: job.id,
          done: true,
          status: 'completed',
          manifest: job.manifest,
          processingTime: Date.now() - new Date(job.startTime).getTime()
        });
      }
      
    } catch (error) {
      job.status = 'failed';
      job.endTime = new Date().toISOString();
      job.error = error.message;
      
      console.error(`💥 Smart Analyzer: Job ${job.id} failed:`, error);
      
      this.emit('progress', {
        jobId: job.id,
        done: true,
        status: 'failed',
        error: error.message
      });
    } finally {
      this.activeJobs--;
      this.processQueue(); // Continue processing remaining jobs
    }
  }

  async analyzeImage(image, job) {
    const imageId = image.id || image.path;
    const filename = image.filename || path.basename(image.path);
    
    // Add to image IDs list
    job.stats.imageIds.push(imageId);
    
    // Smart image classification and grouping - NOW USES AI!
    const classification = await this.classifyImageWithAI(image, filename);
    
    // Group similar images for deduplication
    this.groupSimilarImages(image, classification, job.stats);
    
    // Analyze filename patterns for insights
    this.analyzeFilename(image, job.stats);
    
    // Analyze file size and resolution
    this.analyzeFileSize(image, job.stats);
    if (image.width && image.height) {
      this.analyzeResolution(image, job.stats);
    }

    // Update side counts
    job.stats.sideCounts[classification.side] = (job.stats.sideCounts[classification.side] || 0) + 1;

    // Record side and classification for this image
    job.stats.imageSides.set(imageId, classification.side);
    
    // Store full classification data
    if (!job.stats.imageClassifications) {
      job.stats.imageClassifications = new Map();
    }
    job.stats.imageClassifications.set(imageId, classification);
  }

  async classifyImageWithAI(image, filename) {
    // First try AI-based side detection using actual image content
    let aiSide = 'unknown';
    
    try {
      // Read image file for AI analysis
      const imagePath = image.path || image.id;
      
      if (imagePath && this.ocrService) {
        const imageBuffer = await fs.readFile(imagePath);
        
        // Add timeout to prevent hanging
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('AI detection timeout after 30 seconds')), 30000);
        });
        
        const detectionPromise = detectLicenseSide(imageBuffer, this.ocrService);
        
        aiSide = await Promise.race([detectionPromise, timeoutPromise]);
        console.log(`🤖 Smart Analyzer: AI detected "${aiSide}" for ${filename}`);
      } else {
        console.warn(`⚠️ Smart Analyzer: Missing imagePath or ocrService for ${filename}`);
      }
    } catch (error) {
      console.warn(`⚠️ Smart Analyzer: AI detection failed for ${filename}, using fallback:`, error.message);
    }
    
    // Fallback to filename-based detection if AI failed
    const fallbackClassification = this.classifyImage(image, filename);
    
    // Use AI result if it's not 'unknown', otherwise use filename fallback
    const finalSide = aiSide !== 'unknown' ? aiSide : fallbackClassification.side;
    
    return {
      ...fallbackClassification,
      side: finalSide,
      aiDetected: aiSide !== 'unknown',
      aiSide: aiSide,
      fallbackSide: fallbackClassification.side
    };
  }

  classifyImage(image, filename) {
    const lowerFilename = filename.toLowerCase();
    const baseName = path.parse(filename).name.toLowerCase();
    
    // Detect image side/type
    let side = 'unknown';
    if (lowerFilename.includes('front') || lowerFilename.includes('face')) {
      side = 'front';
    } else if (lowerFilename.includes('back') || lowerFilename.includes('rear')) {
      side = 'back';
    } else if (lowerFilename.includes('selfie') || lowerFilename.includes('self')) {
      side = 'selfie';
    }
    
    // Detect if it's an expanded/zoomed version
    const isExpanded = lowerFilename.includes('expand') || 
                      lowerFilename.includes('zoom') || 
                      lowerFilename.includes('large') ||
                      lowerFilename.includes('_e') ||
                      lowerFilename.includes('-e');
    
    // Extract base identifier (remove expanded/duplicate markers)
    let baseId = baseName
      .replace(/[-_](expand|expanded|zoom|large|e|copy|duplicate).*$/g, '')
      .replace(/[-_]\d+$/g, '') // Remove trailing numbers
      .replace(/[-_](front|back|selfie|self)$/g, ''); // Remove side indicators
    
    // Quality scoring (prefer higher resolution, non-expanded versions)
    let qualityScore = 0;
    if (image.width && image.height) {
      qualityScore += Math.log(image.width * image.height) / 100; // Resolution score
    }
    if (!isExpanded) {
      qualityScore += 10; // Prefer originals over expanded
    }
    if (image.fileSize) {
      qualityScore += Math.log(image.fileSize) / 1000; // File size score
    }
    
    return {
      side,
      isExpanded,
      baseId,
      qualityScore,
      filename,
      imageId: image.id || image.path
    };
  }

  groupSimilarImages(image, classification, stats) {
    if (!stats.imageGroups) {
      stats.imageGroups = new Map();
    }
    
    // Create group key based on base ID and side
    const groupKey = `${classification.baseId}_${classification.side}`;
    
    if (!stats.imageGroups.has(groupKey)) {
      stats.imageGroups.set(groupKey, {
        baseId: classification.baseId,
        side: classification.side,
        images: [],
        selectedImage: null,
        duplicateCount: 0
      });
    }
    
    const group = stats.imageGroups.get(groupKey);
    group.images.push({
      imageId: classification.imageId,
      filename: classification.filename,
      qualityScore: classification.qualityScore,
      isExpanded: classification.isExpanded,
      width: image.width,
      height: image.height,
      fileSize: image.fileSize
    });
    
    // Update selected image if this one has higher quality score
    if (!group.selectedImage || classification.qualityScore > group.selectedImage.qualityScore) {
      group.selectedImage = {
        imageId: classification.imageId,
        filename: classification.filename,
        qualityScore: classification.qualityScore,
        isExpanded: classification.isExpanded,
        width: image.width,
        height: image.height,
        fileSize: image.fileSize
      };
    }
    
    // Count duplicates (more than 1 image in group)
    group.duplicateCount = Math.max(0, group.images.length - 1);
  }

  analyzeFilename(image, stats) {
    const filename = image.filename || path.basename(image.path);
    const baseName = path.parse(filename).name.toLowerCase();
    
    // Extract tokens from filename
    const tokens = baseName.split(/[-_.\s]+/).filter(token => 
      token.length > 1 && !/^\d+$/.test(token) // Ignore single chars and pure numbers
    );
    
    // Count token occurrences
    tokens.forEach(token => {
      const count = stats.filenameClusters.get(token) || 0;
      stats.filenameClusters.set(token, count + 1);
    });
  }

  analyzeFileSize(image, stats) {
    const size = image.fileSize || image.size || 0;
    
    let bucket;
    if (size < 100 * 1024) bucket = '< 100KB';
    else if (size < 300 * 1024) bucket = '100KB - 300KB';
    else if (size < 500 * 1024) bucket = '300KB - 500KB';
    else if (size < 1024 * 1024) bucket = '500KB - 1MB';
    else if (size < 2 * 1024 * 1024) bucket = '1MB - 2MB';
    else if (size < 5 * 1024 * 1024) bucket = '2MB - 5MB';
    else bucket = '> 5MB';
    
    const count = stats.sizeBuckets.get(bucket) || 0;
    stats.sizeBuckets.set(bucket, count + 1);
  }

  analyzeResolution(image, stats) {
    const width = image.width || 0;
    const height = image.height || 0;
    
    if (width === 0 || height === 0) return;
    
    // Create resolution bucket
    let bucket;
    const megapixels = (width * height) / (1024 * 1024);
    
    if (megapixels < 1) bucket = '< 1MP';
    else if (megapixels < 2) bucket = '1-2MP';
    else if (megapixels < 5) bucket = '2-5MP';
    else if (megapixels < 8) bucket = '5-8MP';
    else if (megapixels < 12) bucket = '8-12MP';
    else bucket = '> 12MP';
    
    const count = stats.resBuckets.get(bucket) || 0;
    stats.resBuckets.set(bucket, count + 1);
  }

  async cancelJob(jobId) {
    const job = this.jobs.get(jobId);
    if (!job) {
      throw new Error('Job not found');
    }
    
    if (job.status === 'processing' || job.status === 'pending') {
      job.status = 'cancelled';
      job.endTime = new Date().toISOString();
      
      this.emit('progress', {
        jobId: job.id,
        done: true,
        status: 'cancelled'
      });
      
      console.log(`⏹️ Smart Analyzer: Job ${jobId} cancelled`);
    }
    
    return job;
  }

  getJob(jobId) {
    return this.jobs.get(jobId);
  }

  getAllJobs() {
    return Array.from(this.jobs.values()).sort((a, b) => 
      new Date(b.startTime || 0).getTime() - new Date(a.startTime || 0).getTime()
    );
  }

  deleteJob(jobId) {
    const job = this.jobs.get(jobId);
    if (job && job.status !== 'processing') {
      this.jobs.delete(jobId);
      return true;
    }
    return false;
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

module.exports = { SmartAnalyzer };