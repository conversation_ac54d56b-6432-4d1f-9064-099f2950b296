import { test, expect } from '@playwright/test';
import { ensureTestProject, deleteTestProject, createSampleImages } from './test-utils';

test.describe('Enhanced Image Grid - Folder Navigation E2E Tests', () => {
  const testProjectName = 'E2E Test - Folder Navigation';
  const testProjectPath = 'C:\\claude\\dl-organizer\\test-samples\\e2e-folder-nav-test';

  test.beforeAll(async () => {
    // Set up test project with sample images
    await ensureTestProject(testProjectName, testProjectPath);
    await createSampleImages(testProjectPath, 8);
    
    // Create nested folder structure for testing
    const fs = require('fs/promises');
    const path = require('path');
    
    try {
      // Create subfolder structure
      const subFolder1 = path.join(testProjectPath, 'subfolder-1');
      const subFolder2 = path.join(testProjectPath, 'subfolder-2');
      const nestedFolder = path.join(subFolder1, 'nested-folder');
      
      await fs.mkdir(subFolder1, { recursive: true });
      await fs.mkdir(subFolder2, { recursive: true });
      await fs.mkdir(nestedFolder, { recursive: true });
      
      // Copy some images to subfolders
      await createSampleImages(subFolder1, 3);
      await createSampleImages(subFolder2, 4);
      await createSampleImages(nestedFolder, 2);
      
      console.log('Test folder structure created successfully');
    } catch (error) {
      console.log('Test folder structure already exists or creation failed:', error);
    }
  });

  test.afterAll(async () => {
    // Clean up test project
    await deleteTestProject(testProjectPath);
  });

  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3030');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
  });

  test('should create test project and navigate to enhanced image grid', async ({ page }) => {
    console.log('Creating test project for folder navigation...');
    
    // Look for existing project or create new one
    const existingProjects = page.locator('[data-testid="project-card"]');
    const projectCount = await existingProjects.count();
    
    let projectOpened = false;
    
    // Check if our test project already exists
    if (projectCount > 0) {
      for (let i = 0; i < projectCount; i++) {
        const projectCard = existingProjects.nth(i);
        const projectTitle = await projectCard.locator('h3, .project-title, [data-testid="project-title"]').textContent();
        
        if (projectTitle?.includes('E2E Test') || projectTitle?.includes('Folder Navigation')) {
          console.log('Found existing test project, opening it...');
          await projectCard.locator('button:has-text("Open")').click();
          await page.waitForTimeout(3000);
          projectOpened = true;
          break;
        }
      }
    }
    
    // Create new project if none found
    if (!projectOpened) {
      const newProjectButton = page.locator('button:has-text("New Project"), button:has-text("Create Project")');
      if (await newProjectButton.isVisible()) {
        console.log('Creating new test project...');
        await newProjectButton.click();
        await page.waitForTimeout(1000);
        
        // Fill project details
        const nameInput = page.locator('input[placeholder*="name"], input[name="name"]').first();
        if (await nameInput.isVisible()) {
          await nameInput.fill(testProjectName);
        }
        
        const pathInput = page.locator('input[placeholder*="path"], input[placeholder*="folder"], input[name="path"]').first();
        if (await pathInput.isVisible()) {
          await pathInput.fill(testProjectPath);
        }
        
        const createButton = page.locator('button:has-text("Create"), button:has-text("Save")');
        if (await createButton.isVisible()) {
          await createButton.click();
          await page.waitForTimeout(5000);
          projectOpened = true;
        }
      }
    }
    
    expect(projectOpened).toBeTruthy();
    console.log('✓ Test project opened successfully');
  });

  test('should display folder tree and navigate to folders', async ({ page }) => {
    console.log('Testing folder tree display and navigation...');
    
    // First ensure we have a project open
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(3000);
    }
    
    // Look for folder tree component
    const folderTree = page.locator('[data-testid="folder-tree"], .folder-tree, [class*="folder-tree"]').first();
    let folderTreeVisible = await folderTree.isVisible();
    
    if (!folderTreeVisible) {
      // Alternative selectors for folder navigation
      const folderNavigation = page.locator('[class*="folder"], [class*="navigation"], .sidebar').first();
      folderTreeVisible = await folderNavigation.isVisible();
    }
    
    console.log(`Folder tree visibility: ${folderTreeVisible}`);
    
    // Look for folder items
    const folderItems = page.locator('[data-testid="folder-item"], .folder-item, [class*="folder-node"]');
    const folderCount = await folderItems.count();
    console.log(`Found ${folderCount} folder items`);
    
    if (folderCount > 0) {
      // Test clicking on first folder
      const firstFolder = folderItems.first();
      const folderName = await firstFolder.locator('span, .folder-name').first().textContent();
      console.log(`Clicking on folder: ${folderName}`);
      
      await firstFolder.click();
      await page.waitForTimeout(2000);
      
      console.log('✓ Folder clicked successfully');
    }
    
    expect(folderTreeVisible || folderCount > 0).toBeTruthy();
  });

  test('should load and display images when folder is selected', async ({ page }) => {
    console.log('Testing image loading when folder is selected...');
    
    // Navigate to project
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(3000);
    }
    
    // Find and click on a folder with images
    const foldersWithImages = page.locator('[class*="folder"]:has([class*="image-count"]), [data-testid="folder-item"]:has(.image-icon)');
    let folderClicked = false;
    
    // Try multiple approaches to find folders
    const allFolders = page.locator('[data-testid="folder-item"], .folder-item, [class*="folder-node"], button:has([class*="folder"])');
    const totalFolders = await allFolders.count();
    
    console.log(`Found ${totalFolders} total folders to test`);
    
    for (let i = 0; i < Math.min(totalFolders, 5); i++) {
      const folder = allFolders.nth(i);
      const folderText = await folder.textContent();
      
      if (folderText && (folderText.includes('image') || folderText.includes('test') || folderText.includes('subfolder'))) {
        console.log(`Clicking on folder: ${folderText.slice(0, 50)}...`);
        
        try {
          await folder.click();
          await page.waitForTimeout(2000);
          folderClicked = true;
          break;
        } catch (error) {
          console.log(`Failed to click folder ${i}, trying next...`);
          continue;
        }
      }
    }
    
    if (!folderClicked && totalFolders > 0) {
      // Just click the first folder as fallback
      await allFolders.first().click();
      await page.waitForTimeout(2000);
      folderClicked = true;
      console.log('✓ Clicked first available folder as fallback');
    }
    
    // Now check for enhanced image grid
    const imageGrid = page.locator('[data-testid="enhanced-image-grid"], [class*="image-grid"], [class*="grid"]').first();
    const imageGridVisible = await imageGrid.isVisible();
    
    // Look for individual image items
    const imageItems = page.locator('[data-testid="image-item"], .image-item, [class*="image-card"], img');
    const imageCount = await imageItems.count();
    
    console.log(`Enhanced Image Grid visible: ${imageGridVisible}`);
    console.log(`Found ${imageCount} image items`);
    
    // Check for loading states
    const loadingIndicators = page.locator('[data-testid="loading"], .loading, [class*="loading"], [class*="spinner"]');
    const loadingCount = await loadingIndicators.count();
    
    if (loadingCount > 0) {
      console.log('Found loading indicators, waiting for images to load...');
      await page.waitForTimeout(3000);
    }
    
    // Verify image grid is functional
    if (imageCount > 0) {
      // Test clicking on first image
      const firstImage = imageItems.first();
      
      try {
        await firstImage.click();
        await page.waitForTimeout(1000);
        console.log('✓ Successfully clicked on first image');
      } catch (error) {
        console.log('Could not click first image, but images are displayed');
      }
    }
    
    expect(folderClicked).toBeTruthy();
    console.log('✓ Folder selection and image loading test completed');
  });

  test('should test enhanced image grid pagination and controls', async ({ page }) => {
    console.log('Testing enhanced image grid pagination and controls...');
    
    // Navigate to project with images
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(3000);
    }
    
    // Look for pagination controls
    const paginationControls = page.locator('[class*="pagination"], button:has-text("Next"), button:has-text("Previous")');
    const paginationVisible = await paginationControls.count() > 0;
    
    console.log(`Pagination controls found: ${paginationVisible}`);
    
    // Look for filter controls
    const filterButton = page.locator('button:has-text("Filter"), button:has-text("Filters")');
    const filterVisible = await filterButton.isVisible();
    
    if (filterVisible) {
      console.log('Found filter button, testing filter panel...');
      await filterButton.click();
      await page.waitForTimeout(1000);
      
      // Look for filter options
      const filterPanel = page.locator('[class*="filter"], [data-testid="filter-panel"]');
      const filterPanelVisible = await filterPanel.isVisible();
      console.log(`Filter panel visible: ${filterPanelVisible}`);
      
      // Close filter panel
      if (filterPanelVisible) {
        await filterButton.click();
        await page.waitForTimeout(500);
      }
    }
    
    // Look for search functionality
    const searchInput = page.locator('input[placeholder*="search"], input[type="search"]');
    const searchVisible = await searchInput.isVisible();
    
    if (searchVisible) {
      console.log('Testing search functionality...');
      await searchInput.fill('test');
      await page.waitForTimeout(1000);
      await searchInput.clear();
      await page.waitForTimeout(500);
      console.log('✓ Search functionality tested');
    }
    
    // Test view mode controls if available
    const viewModeButtons = page.locator('button:has-text("Grid"), button:has-text("List")');
    const viewModeCount = await viewModeButtons.count();
    
    if (viewModeCount > 0) {
      console.log('Testing view mode controls...');
      await viewModeButtons.first().click();
      await page.waitForTimeout(1000);
      console.log('✓ View mode controls tested');
    }
    
    console.log('✓ Enhanced image grid controls test completed');
  });

  test('should test folder expansion and collapse functionality', async ({ page }) => {
    console.log('Testing folder expansion and collapse...');
    
    // Navigate to project
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(3000);
    }
    
    // Look for expandable folders (folders with chevron icons)
    const expandButtons = page.locator(
      'button:has(svg[class*="chevron"]), ' +
      'button[title*="expand"], ' +
      '[data-testid="expand-button"], ' +
      'button:has(.chevron-right), ' +
      'button:has(.chevron-down)'
    );
    
    const expandableCount = await expandButtons.count();
    console.log(`Found ${expandableCount} expandable folders`);
    
    if (expandableCount > 0) {
      // Test expanding first folder
      const firstExpandButton = expandButtons.first();
      
      console.log('Testing folder expansion...');
      await firstExpandButton.click();
      await page.waitForTimeout(1000);
      
      // Look for expanded content (child folders)
      const childFolders = page.locator('[class*="child"], [data-level="1"], [style*="padding-left"]');
      const childCount = await childFolders.count();
      
      console.log(`Found ${childCount} child folders after expansion`);
      
      // Test collapsing the folder
      await firstExpandButton.click();
      await page.waitForTimeout(1000);
      
      console.log('✓ Folder expansion and collapse tested successfully');
    }
    
    // Test folder selection workflow
    const allFolders = page.locator('[data-testid="folder-item"], .folder-item, [class*="folder-node"]');
    const totalFolders = await allFolders.count();
    
    if (totalFolders > 0) {
      console.log('Testing folder selection workflow...');
      
      // Select multiple folders to test the workflow
      for (let i = 0; i < Math.min(totalFolders, 3); i++) {
        const folder = allFolders.nth(i);
        
        try {
          await folder.click();
          await page.waitForTimeout(1500);
          
          // Check if images loaded
          const imageItems = page.locator('img, [class*="image"]');
          const imageCount = await imageItems.count();
          
          console.log(`Folder ${i + 1} selected - ${imageCount} images found`);
        } catch (error) {
          console.log(`Could not select folder ${i + 1}, continuing...`);
        }
      }
      
      console.log('✓ Folder selection workflow tested');
    }
    
    expect(expandableCount > 0 || totalFolders > 0).toBeTruthy();
  });

  test('should test enhanced image grid responsiveness and performance', async ({ page }) => {
    console.log('Testing enhanced image grid responsiveness and performance...');
    
    // Navigate to project
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(3000);
    }
    
    // Measure initial load time
    const startTime = Date.now();
    
    // Select a folder with images
    const foldersWithImages = page.locator('[data-testid="folder-item"], .folder-item');
    const folderCount = await foldersWithImages.count();
    
    if (folderCount > 0) {
      await foldersWithImages.first().click();
      await page.waitForTimeout(2000);
      
      // Wait for images to load
      const images = page.locator('img');
      const imageCount = await images.count();
      
      if (imageCount > 0) {
        // Wait for first image to be loaded
        await images.first().waitFor({ state: 'attached' });
        
        const loadTime = Date.now() - startTime;
        console.log(`Image grid loaded ${imageCount} images in ${loadTime}ms`);
        
        // Test should complete within reasonable time (10 seconds)
        expect(loadTime).toBeLessThan(10000);
      }
    }
    
    // Test grid responsiveness by checking if it adapts to different container sizes
    const imageGrid = page.locator('[class*="grid"], [data-testid="image-grid"]').first();
    const gridVisible = await imageGrid.isVisible();
    
    if (gridVisible) {
      // Check if grid has responsive classes or styles
      const gridClasses = await imageGrid.getAttribute('class');
      const gridStyles = await imageGrid.getAttribute('style');
      
      console.log(`Grid responsive features detected: ${!!(gridClasses || gridStyles)}`);
    }
    
    // Test virtual scrolling if implemented
    const virtualizedGrid = page.locator('[class*="virtual"], [class*="windowed"]');
    const virtualizedCount = await virtualizedGrid.count();
    
    if (virtualizedCount > 0) {
      console.log('✓ Virtual scrolling detected for performance optimization');
    }
    
    console.log('✓ Enhanced image grid performance test completed');
  });

  test('should verify complete folder navigation workflow', async ({ page }) => {
    console.log('Testing complete folder navigation workflow...');
    
    let workflowSteps = 0;
    const totalSteps = 6;
    
    // Step 1: Navigate to project
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(3000);
      workflowSteps++;
      console.log('✓ Step 1: Project opened');
    }
    
    // Step 2: Folder tree is visible
    const folderElements = page.locator('[data-testid="folder-item"], .folder-item, [class*="folder"]');
    const folderElementCount = await folderElements.count();
    
    if (folderElementCount > 0) {
      workflowSteps++;
      console.log(`✓ Step 2: Folder tree visible with ${folderElementCount} folders`);
    }
    
    // Step 3: Select a folder
    if (folderElementCount > 0) {
      await folderElements.first().click();
      await page.waitForTimeout(2000);
      workflowSteps++;
      console.log('✓ Step 3: Folder selected');
    }
    
    // Step 4: Images load in enhanced grid
    const images = page.locator('img, [class*="image-item"], [data-testid="image-item"]');
    const imageCount = await images.count();
    
    if (imageCount > 0) {
      workflowSteps++;
      console.log(`✓ Step 4: ${imageCount} images loaded in enhanced grid`);
    } else {
      // Check for empty state message
      const emptyState = page.locator(':text("No images"), :text("empty")');
      const emptyStateVisible = await emptyState.isVisible();
      
      if (emptyStateVisible) {
        workflowSteps++;
        console.log('✓ Step 4: Empty state properly displayed');
      }
    }
    
    // Step 5: Image interaction works
    if (imageCount > 0) {
      try {
        await images.first().click();
        await page.waitForTimeout(1000);
        workflowSteps++;
        console.log('✓ Step 5: Image interaction functional');
      } catch (error) {
        console.log('Step 5: Image interaction could not be tested');
      }
    } else {
      workflowSteps++; // Skip this step if no images
    }
    
    // Step 6: Navigation controls work
    const navControls = page.locator(
      'button:has-text("Filter"), button:has-text("Search"), ' +
      'input[placeholder*="search"], [class*="pagination"]'
    );
    const navControlCount = await navControls.count();
    
    if (navControlCount > 0) {
      workflowSteps++;
      console.log(`✓ Step 6: ${navControlCount} navigation controls available`);
    }
    
    console.log(`Workflow completed: ${workflowSteps}/${totalSteps} steps successful`);
    
    // At least 4 out of 6 steps should work for a successful test
    expect(workflowSteps).toBeGreaterThanOrEqual(4);
    
    console.log('✓ Complete folder navigation workflow test passed');
  });
});