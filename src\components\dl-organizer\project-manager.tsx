"use client"

import { useState, useEffect } from 'react'
import { Plus, Settings, Download, Upload, FolderOpen, Trash2 } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { cn } from '@/lib/utils'
import { Project, ProjectSettings, DEFAULT_PROJECT_SETTINGS } from '@/types'
import { DLOrganizerStorageUtils } from '@/lib/storage-utils'

interface ProjectManagerProps {
  projects: Project[]
  currentProject: Project | null
  onProjectSelect: (project: Project) => void
  onProjectCreate: (name: string, rootPath: string) => void
  onProjectDelete: (projectId: string) => void
  onProjectUpdate: (projectId: string, updates: Partial<Project>) => void
  onBackupExport: () => void
  onBackupImport: (data: string) => void
  className?: string
}

interface CreateProjectDialogProps {
  onCreate: (name: string, rootPath: string) => void
}

function CreateProjectDialog({ onCreate }: CreateProjectDialogProps) {
  const [name, setName] = useState('')
  const [rootPath, setRootPath] = useState('')
  const [isOpen, setIsOpen] = useState(false)

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (name.trim() && rootPath.trim()) {
      onCreate(name.trim(), rootPath.trim())
      setName('')
      setRootPath('')
      setIsOpen(false)
    }
  }

  const handleFolderSelect = async () => {
    try {
      // Use the File System Access API for folder selection
      if ('showDirectoryPicker' in window) {
        const dirHandle = await (window as any).showDirectoryPicker()
        setRootPath(dirHandle.name)
      } else {
        // Fallback for browsers that don't support the API
        const input = document.createElement('input')
        input.type = 'file'
        input.webkitdirectory = true
        input.multiple = true
        input.onchange = (e) => {
          const files = (e.target as HTMLInputElement).files
          if (files && files.length > 0) {
            const path = files[0].webkitRelativePath.split('/')[0]
            setRootPath(path)
          }
        }
        input.click()
      }
    } catch (error) {
      console.error('Error selecting folder:', error)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button>
          <Plus className="w-4 h-4 mr-2" />
          New Project
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Create New Project</DialogTitle>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Project Name</label>
            <Input
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="My Driver's License Project"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-1">Root Folder Path</label>
            <div className="flex gap-2">
              <Input
                value={rootPath}
                onChange={(e) => setRootPath(e.target.value)}
                placeholder="C:\Images\DL_Scans"
                required
              />
              <Button 
                type="button" 
                variant="outline"
                onClick={handleFolderSelect}
              >
                <FolderOpen className="w-4 h-4" />
              </Button>
            </div>
          </div>

          <div className="flex gap-2 pt-4">
            <Button type="submit" className="flex-1">Create Project</Button>
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}

interface ProjectCardProps {
  project: Project
  isSelected: boolean
  onSelect: (project: Project) => void
  onDelete: (projectId: string) => void
  onUpdate: (projectId: string, updates: Partial<Project>) => void
}

function ProjectCard({ project, isSelected, onSelect, onDelete, onUpdate }: ProjectCardProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editName, setEditName] = useState(project.name)
  const [editDescription, setEditDescription] = useState(project.description || '')

  const handleSave = () => {
    onUpdate(project.id, {
      name: editName,
      description: editDescription
    })
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditName(project.name)
    setEditDescription(project.description || '')
    setIsEditing(false)
  }

  return (
    <Card className={cn(
      "cursor-pointer transition-all duration-200 hover:shadow-md",
      isSelected && "ring-2 ring-primary"
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1" onClick={() => onSelect(project)}>
            {isEditing ? (
              <Input
                value={editName}
                onChange={(e) => setEditName(e.target.value)}
                className="mb-2"
                autoFocus
              />
            ) : (
              <CardTitle className="text-lg">{project.name}</CardTitle>
            )}
            
            {isEditing ? (
              <Input
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
                placeholder="Project description..."
              />
            ) : (
              <p className="text-sm text-muted-foreground mt-1">
                {project.description || 'No description'}
              </p>
            )}
          </div>
          
          <div className="flex gap-1">
            {isEditing ? (
              <>
                <Button size="sm" variant="ghost" onClick={handleSave}>
                  Save
                </Button>
                <Button size="sm" variant="ghost" onClick={handleCancel}>
                  Cancel
                </Button>
              </>
            ) : (
              <>
                <Button 
                  size="sm" 
                  variant="ghost" 
                  onClick={() => setIsEditing(true)}
                >
                  <Settings className="w-4 h-4" />
                </Button>
                <Button 
                  size="sm" 
                  variant="ghost" 
                  onClick={() => onDelete(project.id)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0" onClick={() => onSelect(project)}>
        <div className="space-y-2">
          <div className="text-sm">
            <span className="font-medium">Root Path:</span>
            <span className="text-muted-foreground ml-1">{project.rootPath}</span>
          </div>
          
          <div className="text-sm">
            <span className="font-medium">Created:</span>
            <span className="text-muted-foreground ml-1">
              {new Date(project.createdAt).toLocaleDateString()}
            </span>
          </div>
          
          <div className="text-sm">
            <span className="font-medium">AI Provider:</span>
            <span className="text-muted-foreground ml-1">
              {project.settings?.defaultAIProvider || DEFAULT_PROJECT_SETTINGS.defaultAIProvider}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default function ProjectManager({
  projects,
  currentProject,
  onProjectSelect,
  onProjectCreate,
  onProjectDelete,
  onProjectUpdate,
  onBackupExport,
  onBackupImport,
  className
}: ProjectManagerProps) {
  const [storageInfo, setStorageInfo] = useState<any>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [showBackupDialog, setShowBackupDialog] = useState(false)

  useEffect(() => {
    const loadStorageInfo = async () => {
      const info = await DLOrganizerStorageUtils.getStorageInfo()
      setStorageInfo(info)
    }
    loadStorageInfo()
  }, [projects])

  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    project.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    project.rootPath.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const handleBackupImport = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const data = e.target?.result as string
        onBackupImport(data)
      }
      reader.readAsText(file)
    }
  }

  return (
    <div className={cn("space-y-6", className)}>
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Projects</h2>
          <p className="text-muted-foreground">
            Manage your driver&apos;s license processing projects
          </p>
        </div>
        
        <div className="flex gap-2">
          <CreateProjectDialog onCreate={onProjectCreate} />
          
          <Dialog open={showBackupDialog} onOpenChange={setShowBackupDialog}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Download className="w-4 h-4 mr-2" />
                Backup
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Backup & Restore</DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <div className="flex gap-2">
                  <Button onClick={onBackupExport} className="flex-1">
                    <Download className="w-4 h-4 mr-2" />
                    Export Backup
                  </Button>
                  <Button variant="outline" className="flex-1">
                    <Upload className="w-4 h-4 mr-2" />
                    Import Backup
                    <input
                      type="file"
                      accept=".json"
                      onChange={handleBackupImport}
                      className="absolute inset-0 opacity-0 cursor-pointer"
                    />
                  </Button>
                </div>
                
                {storageInfo && (
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Total Projects:</span>
                      <span>{storageInfo.totalProjects}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Folders:</span>
                      <span>{storageInfo.totalFolders}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Images:</span>
                      <span>{storageInfo.totalImages}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Storage Used:</span>
                      <span>{(storageInfo.totalStorageUsed / 1024).toFixed(1)} KB</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Last Backup:</span>
                      <span>{storageInfo.lastBackup}</span>
                    </div>
                  </div>
                )}
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <div className="space-y-4">
        <Input
          placeholder="Search projects..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full"
        />

        {filteredProjects.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-muted-foreground">
              {projects.length === 0 ? (
                <div>
                  <p className="text-lg mb-2">No projects yet</p>
                  <p>Create your first project to get started</p>
                </div>
              ) : (
                <p>No projects match your search</p>
              )}
            </div>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {filteredProjects.map((project) => (
              <ProjectCard
                key={project.id}
                project={project}
                isSelected={currentProject?.id === project.id}
                onSelect={onProjectSelect}
                onDelete={onProjectDelete}
                onUpdate={onProjectUpdate}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}