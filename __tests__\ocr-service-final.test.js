/**
 * Comprehensive Jest tests for OCR service error handling and API fallback logic
 * 
 * Tests cover:
 * 1. API error handling - network, auth, rate limit, server errors
 * 2. Fallback logic - primary model failure recovery
 * 3. Retry mechanisms - exponential backoff and failure handling
 * 4. Rate limiting and cost tracking
 * 5. Model availability and configuration validation
 * 6. Response parsing edge cases
 * 7. Provider-specific error scenarios
 */

// Mock all external dependencies before importing the service
jest.mock('openai', () => jest.fn());
jest.mock('sharp');
jest.mock('../backend/services/cost-tracker');
jest.mock('../backend/services/file-manager');

// Mock console methods to suppress output during tests
const mockConsole = {
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn()
};

global.console = mockConsole;

describe('OCR Service Error Handling and API Fallback Logic', () => {
  let OCRService;
  let ocrService;
  let mockOpenAIClient;
  let mockSharp;

  beforeAll(() => {
    // Setup mocks before requiring the service
    const OpenAI = require('openai');
    mockSharp = require('sharp');
    
    // Mock OpenAI client
    mockOpenAIClient = {
      chat: {
        completions: {
          create: jest.fn()
        }
      }
    };
    
    OpenAI.mockImplementation(() => mockOpenAIClient);
    
    // Mock Sharp with proper chaining
    mockSharp.mockImplementation(() => ({
      metadata: jest.fn().mockResolvedValue({ width: 1024, height: 768, format: 'jpeg' }),
      resize: jest.fn().mockReturnThis(),
      jpeg: jest.fn().mockReturnThis(),
      normalize: jest.fn().mockReturnThis(),
      sharpen: jest.fn().mockReturnThis(),
      gamma: jest.fn().mockReturnThis(),
      toBuffer: jest.fn().mockResolvedValue(Buffer.from('processed-image'))
    }));

    // Add static method support for Sharp test image creation
    mockSharp.mockImplementation((options) => {
      if (options && options.create) {
        return {
          jpeg: jest.fn().mockReturnThis(),
          toBuffer: jest.fn().mockResolvedValue(Buffer.from('test-image'))
        };
      }
      return {
        metadata: jest.fn().mockResolvedValue({ width: 1024, height: 768, format: 'jpeg' }),
        resize: jest.fn().mockReturnThis(),
        jpeg: jest.fn().mockReturnThis(),
        normalize: jest.fn().mockReturnThis(),
        sharpen: jest.fn().mockReturnThis(),
        gamma: jest.fn().mockReturnThis(),
        toBuffer: jest.fn().mockResolvedValue(Buffer.from('processed-image'))
      };
    });

    // Now require the service
    OCRService = require('../backend/services/ocr-service');
  });

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create fresh service instance
    ocrService = new OCRService();
    
    // Mock cost tracker with all required methods
    ocrService.costTracker = {
      initialize: jest.fn().mockResolvedValue(),
      checkLimits: jest.fn().mockReturnValue({ allowed: true }),
      checkRateLimit: jest.fn().mockReturnValue({ allowed: true }),
      data: {
        transactions: [],
        statistics: { totalSpent: 0 }
      }
    };

    // Configure test provider
    ocrService.configureProvider('openrouter', { apiKey: 'test-api-key' });
  });

  describe('API Error Handling', () => {
    test('should handle network timeout with proper error message', async () => {
      const timeoutError = new Error('Network request timeout');
      timeoutError.code = 'ETIMEDOUT';
      
      mockOpenAIClient.chat.completions.create.mockRejectedValue(timeoutError);
      
      // Mock sleep to speed up test
      jest.spyOn(ocrService, 'sleep').mockResolvedValue();

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Network request timeout');
      expect(result.retryCount).toBe(3);
    });

    test('should handle API authentication errors', async () => {
      const authError = new Error('Invalid API key');
      authError.status = 401;
      
      mockOpenAIClient.chat.completions.create.mockRejectedValue(authError);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid API key');
    });

    test('should handle rate limiting errors', async () => {
      const rateLimitError = new Error('Rate limit exceeded');
      rateLimitError.status = 429;
      
      mockOpenAIClient.chat.completions.create.mockRejectedValue(rateLimitError);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Rate limit exceeded');
    });

    test('should handle server errors', async () => {
      const serverError = new Error('Internal server error');
      serverError.status = 500;
      
      mockOpenAIClient.chat.completions.create.mockRejectedValue(serverError);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Internal server error');
    });
  });

  describe('Fallback Logic', () => {
    test('should use fallback model when primary fails', async () => {
      // First call fails, second succeeds
      mockOpenAIClient.chat.completions.create
        .mockRejectedValueOnce(new Error('Primary model failed'))
        .mockResolvedValueOnce({
          choices: [{ message: { content: JSON.stringify({
            firstName: 'John',
            lastName: 'Doe',
            licenseNumber: '********',
            confidence: 0.9
          }) } }],
          usage: { prompt_tokens: 100, completion_tokens: 50, total_tokens: 150 }
        });

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(true);
      expect(result.result.firstName).toBe('John');
      expect(result.note).toMatch(/fallback/);
    });

    test('should not use fallback for user-specified models', async () => {
      mockOpenAIClient.chat.completions.create.mockRejectedValue(
        new Error('User specified model failed')
      );

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('User specified model failed');
      expect(result.note).toBeUndefined();
    });
  });

  describe('Retry Mechanism', () => {
    test('should retry on transient errors with increasing delay', async () => {
      const transientError = new Error('Connection reset');
      transientError.code = 'ECONNRESET';
      
      mockOpenAIClient.chat.completions.create
        .mockRejectedValueOnce(transientError)
        .mockRejectedValueOnce(transientError)
        .mockResolvedValueOnce({
          choices: [{ message: { content: JSON.stringify({
            firstName: 'Success',
            lastName: 'AfterRetry',
            confidence: 0.9
          }) } }],
          usage: { prompt_tokens: 100, completion_tokens: 50, total_tokens: 150 }
        });

      jest.spyOn(ocrService, 'sleep').mockResolvedValue();

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(2);
      expect(ocrService.sleep).toHaveBeenCalledWith(1000); // First retry delay
      expect(ocrService.sleep).toHaveBeenCalledWith(2000); // Second retry delay
    });
  });

  describe('Rate Limiting and Cost Control', () => {
    test('should block requests when rate limited', async () => {
      jest.spyOn(ocrService, 'isRateLimited').mockReturnValue(true);

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(false);
      expect(result.error).toContain('Rate limit exceeded');
    });

    test('should block requests when cost limit exceeded', async () => {
      jest.spyOn(ocrService, 'isCostLimitExceeded').mockReturnValue(true);

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(false);
      expect(result.error).toContain('cost limit exceeded');
    });
  });

  describe('Model Configuration and Availability', () => {
    test('should handle invalid model selection', async () => {
      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'nonexistent-model'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('not available');
      expect(result.error).toContain('Available models:');
    });

    test('should handle unconfigured provider', async () => {
      const provider = ocrService.providers.get('openrouter');
      provider.isConfigured = false;

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('API configuration');
    });

    test('should validate provider configuration', () => {
      expect(() => {
        ocrService.configureProvider('invalid-provider', { apiKey: 'test' });
      }).toThrow('Provider invalid-provider not found');
    });
  });

  describe('Response Parsing', () => {
    test('should handle malformed JSON responses', async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: '{"firstName": "John", invalid json' } }],
        usage: { prompt_tokens: 100, completion_tokens: 50, total_tokens: 150 }
      });

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(true);
      expect(result.result.parseError).toBeDefined();
      expect(result.result.firstName).toBe(''); // Should fallback to empty
    });

    test('should extract JSON from response with extra text', async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: 'Here is the data: {"firstName": "Jane", "lastName": "Smith"} End of response' } }],
        usage: { prompt_tokens: 100, completion_tokens: 50, total_tokens: 150 }
      });

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(true);
      expect(result.result.firstName).toBe('Jane');
      expect(result.result.lastName).toBe('Smith');
    });
  });

  describe('Successful Processing', () => {
    test('should successfully process valid responses', async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: JSON.stringify({
          firstName: 'Alice',
          lastName: 'Johnson',
          licenseNumber: '********',
          state: 'TX',
          confidence: 0.95
        }) } }],
        usage: { prompt_tokens: 150, completion_tokens: 75, total_tokens: 225 }
      });

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(true);
      expect(result.result.firstName).toBe('Alice');
      expect(result.result.lastName).toBe('Johnson');
      expect(result.result.licenseNumber).toBe('********');
      expect(result.cost).toBeGreaterThan(0);
      expect(result.processingTime).toBeGreaterThan(0);
    });

    test('should track costs on successful processing', async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: JSON.stringify({
          firstName: 'Bob',
          lastName: 'Wilson',
          confidence: 0.8
        }) } }],
        usage: { prompt_tokens: 100, completion_tokens: 50, total_tokens: 150 }
      });

      const updateCostSpy = jest.spyOn(ocrService, 'updateCostTracking');

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(true);
      expect(updateCostSpy).toHaveBeenCalledWith(
        expect.any(String), // model ID
        expect.any(Number), // cost
        true // success
      );
    });
  });

  describe('Image Processing Edge Cases', () => {
    test('should handle image preprocessing errors', async () => {
      // Mock Sharp to throw an error
      mockSharp.mockImplementation(() => {
        throw new Error('Invalid image format');
      });

      const result = await ocrService.processImage(Buffer.from('invalid-data'));

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid image format');
    });

    test('should handle unsupported image formats by converting', async () => {
      const mockSharpChain = {
        metadata: jest.fn().mockResolvedValue({ width: 1024, height: 768, format: 'tiff' }),
        resize: jest.fn().mockReturnThis(),
        jpeg: jest.fn().mockReturnThis(),
        normalize: jest.fn().mockReturnThis(),
        sharpen: jest.fn().mockReturnThis(),
        gamma: jest.fn().mockReturnThis(),
        toBuffer: jest.fn().mockResolvedValue(Buffer.from('converted-image'))
      };

      mockSharp.mockImplementation(() => mockSharpChain);

      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: JSON.stringify({
          firstName: 'Converted',
          lastName: 'Image',
          confidence: 0.8
        }) } }],
        usage: { prompt_tokens: 100, completion_tokens: 50, total_tokens: 150 }
      });

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(true);
      expect(result.result.firstName).toBe('Converted');
      expect(mockSharpChain.jpeg).toHaveBeenCalled(); // Should convert to JPEG
    });
  });

  describe('Provider-Specific Error Handling', () => {
    test('should handle OpenRouter service errors', async () => {
      const openRouterError = new Error('OpenRouter service unavailable');
      openRouterError.response = { status: 503 };
      
      mockOpenAIClient.chat.completions.create.mockRejectedValue(openRouterError);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('OpenRouter service unavailable');
    });

    test('should handle OpenAI quota errors', async () => {
      const quotaError = new Error('Quota exceeded');
      quotaError.type = 'insufficient_quota';
      
      mockOpenAIClient.chat.completions.create.mockRejectedValue(quotaError);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'gpt-4o-mini'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Quota exceeded');
    });
  });

  describe('Concurrent Request Handling', () => {
    test('should handle multiple concurrent requests without interference', async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: JSON.stringify({
          firstName: 'Concurrent',
          lastName: 'Test',
          confidence: 0.9
        }) } }],
        usage: { prompt_tokens: 100, completion_tokens: 50, total_tokens: 150 }
      });

      const requests = Array(3).fill(null).map(() => 
        ocrService.processImage(Buffer.from('test-image'))
      );

      const results = await Promise.all(requests);

      results.forEach(result => {
        expect(result.success).toBe(true);
        expect(result.result.firstName).toBe('Concurrent');
      });

      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledTimes(3);
    });
  });
});