import { NextRequest, NextResponse } from "next/server";
import { getBackendUrl } from "@/utils/port-config";

export async function GET(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  try {
    const { jobId } = params;

    // Get the backend URL
    const backendUrl = await getBackendUrl();

    // Create a passthrough stream for Server-Sent Events
    const response = await fetch(`${backendUrl}/api/sequential-batch/stream/${jobId}`, {
      method: "GET",
      headers: {
        "User-Agent": request.headers.get("User-Agent") || "",
        "Accept": "text/event-stream",
        "Cache-Control": "no-cache",
      },
    });

    if (!response.ok) {
      throw new Error(`Backend responded with status: ${response.status}`);
    }

    // Return the streaming response with appropriate headers
    return new NextResponse(response.body, {
      status: 200,
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control",
      },
    });
    
  } catch (error) {
    console.error("API Proxy Error (sequential-batch/stream):", error);
    
    // Return an error event stream
    const errorStream = new ReadableStream({
      start(controller) {
        const errorMessage = JSON.stringify({
          type: "error",
          error: "Failed to connect to backend stream",
          details: error instanceof Error ? error.message : "Unknown error",
        });
        
        controller.enqueue(`data: ${errorMessage}\n\n`);
        controller.close();
      },
    });

    return new NextResponse(errorStream, {
      status: 200, // Still return 200 for SSE, but with error event
      headers: {
        "Content-Type": "text/event-stream",
        "Cache-Control": "no-cache",
        "Connection": "keep-alive",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control",
      },
    });
  }
}