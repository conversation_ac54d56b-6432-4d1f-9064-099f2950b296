'use client'

import React, { useState } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  ArrowLeft,
  CheckCircle, 
  Copy, 
  Eye, 
  FileText, 
  FolderOpen, 
  X 
} from 'lucide-react'

interface SavedFiles {
  json?: string
  txt?: string
}

interface OCRSaveSuccessDialogProps {
  isOpen: boolean
  onClose: () => void
  savedFiles: SavedFiles
  imageName: string
}

export function OCRSaveSuccessDialog({ 
  isOpen, 
  onClose, 
  savedFiles, 
  imageName 
}: OCRSaveSuccessDialogProps) {
  const [copiedFile, setCopiedFile] = useState<string | null>(null)
  const [fileContent, setFileContent] = useState<{type: string, content: string} | null>(null)
  const [loadingContent, setLoadingContent] = useState(false)

  const copyToClipboard = async (filePath: string, fileType: string) => {
    try {
      await navigator.clipboard.writeText(filePath)
      setCopiedFile(fileType)
      setTimeout(() => setCopiedFile(null), 2000)
    } catch (err) {
      console.error('Failed to copy to clipboard:', err)
    }
  }

  const viewFile = async (filePath: string, fileType: string) => {
    setLoadingContent(true)
    try {
      const response = await fetch('/api/filesystem/read-file', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ filePath })
      })
      
      if (response.ok) {
        const data = await response.json()
        setFileContent({ type: fileType, content: data.content })
      } else {
        console.error('Failed to read file')
      }
    } catch (err) {
      console.error('Error reading file:', err)
    } finally {
      setLoadingContent(false)
    }
  }

  const getFileName = (filePath: string) => {
    return filePath.split(/[\\/]/).pop() || filePath
  }

  const formatFileContent = (content: string, type: string) => {
    if (type === 'json') {
      try {
        const parsed = JSON.parse(content)
        return JSON.stringify(parsed, null, 2)
      } catch {
        return content
      }
    }
    return content
  }

  const closeFileViewer = () => {
    setFileContent(null)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader className="pb-6 border-b border-border">
          <DialogTitle className="flex items-center gap-3 text-xl">
            <div className="p-2 bg-green-100 dark:bg-green-900/50 rounded-full">
              <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <div className="text-xl font-semibold text-green-800 dark:text-green-200">OCR Data Saved Successfully</div>
              <div className="text-sm font-normal text-muted-foreground mt-1">Operation completed successfully</div>
            </div>
          </DialogTitle>
          <DialogDescription className="text-base text-foreground mt-4">
            OCR results for <strong className="text-green-700 dark:text-green-300">{imageName}</strong> have been saved to the following files:
          </DialogDescription>
        </DialogHeader>

        {fileContent ? (
          // File Content Viewer
          <div className="space-y-6">
            <div className="flex items-center justify-between pb-2 border-b">
              <div className="flex items-center gap-3">
                <FileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                <div>
                  <span className="font-semibold text-lg">{fileContent.type.toUpperCase()} File Content</span>
                  <p className="text-xs text-muted-foreground mt-1">Viewing saved OCR data</p>
                </div>
              </div>
              <Button
                onClick={closeFileViewer}
                variant="ghost"
                size="sm"
                className="hover:bg-red-50 hover:text-red-600 dark:hover:bg-red-900/20"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            <Card className="border-blue-200 dark:border-blue-800">
              <CardContent className="p-0">
                <div className="bg-muted/30 px-4 py-2 border-b flex items-center gap-2">
                  <Badge variant="outline" className="text-xs bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-200">
                    {fileContent.type.toUpperCase()}
                  </Badge>
                  <span className="text-xs text-muted-foreground">File content preview</span>
                </div>
                <div className="p-4">
                  <pre className="text-sm whitespace-pre-wrap bg-background border rounded-md p-4 overflow-auto max-h-96 font-mono text-foreground">
                    {formatFileContent(fileContent.content, fileContent.type)}
                  </pre>
                </div>
              </CardContent>
            </Card>

            <div className="flex gap-3">
              <Button
                onClick={closeFileViewer}
                variant="outline"
                className="hover:bg-gray-50 dark:hover:bg-gray-800"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to File List
              </Button>
            </div>
          </div>
        ) : (
          // File List View
          <div className="space-y-6 pt-2">
            {Object.entries(savedFiles).map(([fileType, filePath]) => {
              if (!filePath) return null
              
              return (
                <Card key={fileType} className="border-l-4 border-l-green-500 bg-card hover:bg-accent/20 transition-colors">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between gap-4">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-3">
                          <FileText className="h-4 w-4 text-green-600 dark:text-green-400" />
                          <Badge variant="secondary" className="uppercase text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200">
                            {fileType}
                          </Badge>
                        </div>
                        
                        <div className="font-semibold text-sm mb-2 text-foreground">
                          {getFileName(filePath)}
                        </div>
                        
                        <div className="text-xs text-muted-foreground font-mono break-all bg-muted/50 p-2 rounded border">
                          {filePath}
                        </div>
                      </div>

                      <div className="flex flex-col gap-2 shrink-0">
                        <Button
                          onClick={() => copyToClipboard(filePath, fileType)}
                          variant="outline"
                          size="sm"
                          className="h-8 hover:bg-green-50 hover:border-green-300 dark:hover:bg-green-900/20"
                        >
                          {copiedFile === fileType ? (
                            <>
                              <CheckCircle className="h-3 w-3 mr-1 text-green-600" />
                              <span className="text-green-600">Copied</span>
                            </>
                          ) : (
                            <>
                              <Copy className="h-3 w-3 mr-1" />
                              Copy Path
                            </>
                          )}
                        </Button>
                        
                        <Button
                          onClick={() => viewFile(filePath, fileType)}
                          variant="outline"
                          size="sm"
                          className="h-8 hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-900/20"
                          disabled={loadingContent}
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          {loadingContent ? 'Loading...' : 'View File'}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}

            <Separator />

            <Alert className="border-green-500/20 bg-green-500/10 text-green-800 dark:text-green-200">
              <CheckCircle className="h-4 w-4 text-green-600 dark:text-green-400" />
              <AlertDescription className="text-green-800 dark:text-green-200">
                Files have been saved successfully. You can copy the file paths to your clipboard or view the content directly.
              </AlertDescription>
            </Alert>

            <div className="flex justify-end gap-3 pt-2">
              <Button
                onClick={onClose}
                variant="default"
                className="bg-green-600 hover:bg-green-700 text-white px-6"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Done
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  )
}

export default OCRSaveSuccessDialog