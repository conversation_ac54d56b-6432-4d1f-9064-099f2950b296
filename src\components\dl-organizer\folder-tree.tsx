"use client"

import { useState, useEffect } from 'react'
import { ChevronRight, ChevronDown, Folder, FolderOpen, Image as ImageIcon, FileText, Tag, Edit3, Check, X } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { cn } from '@/lib/utils'
import { FolderNode } from '@/types'

interface FolderTreeProps {
  rootPath: string
  folders: FolderNode[]
  onFolderSelect: (folder: FolderNode) => Promise<void>
  onFolderExpand: (folderId: string) => void
  onFolderTag: (folderId: string, tags: string[]) => void
  onFolderRename?: (folderId: string, newName: string) => void
  selectedFolderId?: string
  className?: string
  // New props for batch selection
  enableBatchSelection?: boolean
  selectedFolderIds?: string[]
  onBatchSelect?: (folderIds: string[]) => void
}

interface FolderNodeProps {
  folder: FolderNode
  level: number
  onSelect: (folder: FolderNode) => Promise<void>
  onExpand: (folderId: string) => void
  onTag: (folderId: string, tags: string[]) => void
  onRename?: (folderId: string, newName: string) => void
  isSelected: boolean
  selectedFolderId?: string
  // New props for batch selection
  enableBatchSelection?: boolean
  selectedFolderIds?: string[]
  onBatchToggle?: (folderId: string, selected: boolean) => void
}

function FolderNodeComponent({ 
  folder, 
  level, 
  onSelect, 
  onExpand, 
  onTag, 
  onRename,
  isSelected,
  selectedFolderId,
  enableBatchSelection,
  selectedFolderIds,
  onBatchToggle
}: FolderNodeProps) {
  const [isEditingTags, setIsEditingTags] = useState(false)
  const [isEditingName, setIsEditingName] = useState(false)
  const [tagInput, setTagInput] = useState(folder.tags.join(', '))
  const [nameInput, setNameInput] = useState(folder.name)

  const handleExpand = (e: React.MouseEvent) => {
    e.stopPropagation()
    onExpand(folder.id)
  }

  const handleSelect = () => {
    onSelect(folder)
  }

  const isBatchSelected = selectedFolderIds?.includes(folder.id) || false

  const handleBatchToggle = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (onBatchToggle) {
      onBatchToggle(folder.id, !isBatchSelected)
    }
  }

  const handleTagSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    const newTags = tagInput
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)
    
    onTag(folder.id, newTags)
    setIsEditingTags(false)
  }

  const handleTagClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    setIsEditingTags(true)
  }

  const handleTagCancel = () => {
    setTagInput(folder.tags.join(', '))
    setIsEditingTags(false)
  }

  const handleRenameSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (nameInput.trim() && nameInput.trim() !== folder.name && onRename) {
      onRename(folder.id, nameInput.trim())
      setIsEditingName(false)
    }
  }

  const handleRenameClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    setIsEditingName(true)
  }

  const handleRenameCancel = () => {
    setNameInput(folder.name)
    setIsEditingName(false)
  }

  const hasChildren = folder.children.length > 0
  const paddingLeft = level * 20 + 8

  return (
    <div className="select-none">
      <div
        className={cn(
          "flex items-center py-2 px-2 rounded-md cursor-pointer hover:bg-muted/50 transition-colors group min-w-0",
          isSelected && "bg-muted"
        )}
        style={{ paddingLeft }}
        onClick={handleSelect}
      >
        {hasChildren && (
          <Button
            variant="ghost"
            size="sm"
            className="p-0 h-4 w-4 mr-2 hover:bg-muted/80 focus:bg-muted/80"
            onClick={handleExpand}
          >
            {folder.isExpanded ? (
              <ChevronDown className="h-3 w-3 text-muted-foreground" />
            ) : (
              <ChevronRight className="h-3 w-3 text-muted-foreground" />
            )}
          </Button>
        )}
        
        {!hasChildren && <div className="w-6 mr-2" />}
        
        {enableBatchSelection && (
          <input
            type="checkbox"
            checked={isBatchSelected || false}
            onChange={(e) => {
              e.stopPropagation()
              if (onBatchToggle) {
                onBatchToggle(folder.id, e.target.checked)
              }
            }}
            onClick={(e) => e.stopPropagation()}
            className="mr-2 h-4 w-4 accent-blue-600 bg-background border-border rounded focus:ring-ring focus:ring-2"
          />
        )}
        
        <div className="flex items-center gap-2 flex-1 min-w-0">
          {folder.isExpanded ? (
            <FolderOpen className="h-4 w-4 text-blue-500 flex-shrink-0" />
          ) : (
            <Folder className="h-4 w-4 text-blue-500 flex-shrink-0" />
          )}
          
          {isEditingName ? (
            <form onSubmit={handleRenameSubmit} className="flex items-center gap-1">
              <Input
                value={nameInput}
                onChange={(e) => setNameInput(e.target.value)}
                className="h-6 text-sm"
                autoFocus
                onBlur={handleRenameCancel}
                onClick={(e) => e.stopPropagation()}
              />
              <Button
                type="submit"
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={(e) => e.stopPropagation()}
              >
                <Check className="h-3 w-3" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={(e) => {
                  e.stopPropagation()
                  handleRenameCancel()
                }}
              >
                <X className="h-3 w-3" />
              </Button>
            </form>
          ) : (
            <span className="text-sm font-medium truncate">{folder.name}</span>
          )}
          
          <div className="flex items-center gap-1 ml-auto flex-shrink-0 max-w-[150px]">
            {folder.imageCount > 0 && (
              <div className="flex items-center gap-1 text-xs text-muted-foreground">
                <ImageIcon className="h-3 w-3" />
                <span>{folder.imageCount}</span>
              </div>
            )}
            
            {folder.hasTextFile && (
              <FileText className="h-3 w-3 text-green-500" />
            )}
            
            {folder.tags.length > 0 && (
              <div className="flex items-center gap-1">
                <Tag className="h-3 w-3 text-purple-500" />
                <div className="flex gap-1">
                  {folder.tags.map((tag, index) => (
                    <span
                      key={index}
                      className="text-xs bg-purple-100 text-purple-700 px-1 py-0.5 rounded"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
            
            {onRename && (
              <Button
                variant="ghost"
                size="sm"
                className="p-0 h-4 w-4 opacity-0 group-hover:opacity-100 hover:bg-muted/80 flex-shrink-0"
                onClick={handleRenameClick}
                title="Rename folder"
              >
                <Edit3 className="h-3 w-3" />
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              className="p-0 h-4 w-4 opacity-0 group-hover:opacity-100 hover:bg-muted/80 flex-shrink-0"
              onClick={handleTagClick}
              title="Edit tags"
            >
              <Tag className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>

      {isEditingTags && (
        <div className="ml-8 mt-2 mb-2">
          <form onSubmit={handleTagSubmit} className="flex gap-2">
            <Input
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              placeholder="Enter tags separated by commas"
              className="text-xs"
              autoFocus
            />
            <Button type="submit" size="sm" variant="outline">
              Save
            </Button>
            <Button type="button" size="sm" variant="ghost" onClick={handleTagCancel}>
              Cancel
            </Button>
          </form>
        </div>
      )}

      {folder.isExpanded && folder.children.map((child) => (
        <FolderNodeComponent
          key={child.id}
          folder={child}
          level={level + 1}
          onSelect={onSelect}
          onExpand={onExpand}
          onTag={onTag}
          onRename={onRename}
          isSelected={child.id === selectedFolderId}
          selectedFolderId={selectedFolderId}
          enableBatchSelection={enableBatchSelection}
          selectedFolderIds={selectedFolderIds}
          onBatchToggle={onBatchToggle}
        />
      ))}
    </div>
  )
}

export default function FolderTree({
  rootPath,
  folders,
  onFolderSelect,
  onFolderExpand,
  onFolderTag,
  onFolderRename,
  selectedFolderId,
  className,
  enableBatchSelection,
  selectedFolderIds,
  onBatchSelect
}: FolderTreeProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [filteredFolders, setFilteredFolders] = useState(folders)

  const handleBatchToggle = (folderId: string, selected: boolean) => {
    if (!onBatchSelect || !selectedFolderIds) return
    
    const newSelectedIds = selected
      ? [...selectedFolderIds, folderId]
      : selectedFolderIds.filter(id => id !== folderId)
    
    onBatchSelect(newSelectedIds)
  }

  const handleSelectAll = () => {
    if (!onBatchSelect || !selectedFolderIds) return
    
    const getAllFolderIds = (folders: FolderNode[]): string[] => {
      return folders.reduce((acc, folder) => {
        return [...acc, folder.id, ...getAllFolderIds(folder.children)]
      }, [] as string[])
    }
    
    const allFolderIds = getAllFolderIds(filteredFolders)
    const areAllSelected = allFolderIds.every(id => selectedFolderIds.includes(id))
    
    if (areAllSelected) {
      onBatchSelect([])
    } else {
      onBatchSelect(allFolderIds)
    }
  }

  useEffect(() => {
    if (!searchTerm) {
      setFilteredFolders(folders)
      return
    }

    const filterFolders = (folders: FolderNode[]): FolderNode[] => {
      return folders.filter(folder => {
        const matchesSearch = folder.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             folder.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
        
        const hasMatchingChildren = folder.children.some(child => 
          filterFolders([child]).length > 0
        )

        return matchesSearch || hasMatchingChildren
      }).map(folder => ({
        ...folder,
        children: filterFolders(folder.children)
      }))
    }

    setFilteredFolders(filterFolders(folders))
  }, [searchTerm, folders])

  if (folders.length === 0) {
    return (
      <Card className={cn("h-64", className)}>
        <CardContent className="flex items-center justify-center h-full text-muted-foreground">
          <div className="text-center">
            <Folder className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p>No folders found</p>
            <p className="text-sm">Select a root directory to begin</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={cn("h-full", className)}>
      <CardContent className="p-4">
        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-semibold">Folders</h3>
            {enableBatchSelection && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleSelectAll}
                className="text-xs"
              >
                {selectedFolderIds && selectedFolderIds.length > 0 ? 'Clear All' : 'Select All'}
              </Button>
            )}
          </div>
          <div className="text-sm text-muted-foreground mb-2">
            Root: {rootPath}
            {enableBatchSelection && selectedFolderIds && selectedFolderIds.length > 0 && (
              <span className="ml-2 text-blue-600">
                ({selectedFolderIds.length} selected)
              </span>
            )}
          </div>
          <Input
            placeholder="Search folders or tags..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full"
          />
        </div>

        <div className="overflow-y-auto overflow-x-hidden h-full max-h-[600px] min-h-[500px]">
          {filteredFolders.map((folder) => (
            <FolderNodeComponent
              key={folder.id}
              folder={folder}
              level={0}
              onSelect={onFolderSelect}
              onExpand={onFolderExpand}
              onTag={onFolderTag}
              onRename={onFolderRename}
              isSelected={selectedFolderId === folder.id}
              selectedFolderId={selectedFolderId}
              enableBatchSelection={enableBatchSelection}
              selectedFolderIds={selectedFolderIds}
              onBatchToggle={handleBatchToggle}
            />
          ))}
        </div>

        {filteredFolders.length === 0 && searchTerm && (
          <div className="text-center text-muted-foreground py-8">
            <p>No folders match your search</p>
            <Button 
              variant="ghost" 
              size="sm"
              className="mt-2"
              onClick={() => setSearchTerm('')}
            >
              Clear search
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  )
}