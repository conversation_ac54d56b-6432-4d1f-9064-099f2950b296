const BatchProcessor = require('./batch-processor');
const EventEmitter = require('events');

// Mock dependencies
const mockOcrService = {
  processImage: jest.fn(),
  findModel: jest.fn()
};

const mockLocalModelService = {};

const mockCostTracker = {
  checkLimits: jest.fn(),
  checkRateLimit: jest.fn(),
  recordTransaction: jest.fn()
};

describe('BatchProcessor - Status Tracking', () => {
  let batchProcessor;
  let eventSpy;

  beforeEach(() => {
    jest.clearAllMocks();
    batchProcessor = new BatchProcessor(mockOcrService, mockLocalModelService, mockCostTracker);
    
    // Spy on event emissions
    eventSpy = {
      jobStarted: jest.fn(),
      jobProgress: jest.fn(),
      jobCompleted: jest.fn(),
      jobFailed: jest.fn(),
      jobCancelled: jest.fn()
    };
    
    Object.keys(eventSpy).forEach(event => {
      batchProcessor.on(event, eventSpy[event]);
    });

    // Mock successful OCR processing by default
    mockOcrService.processImage.mockResolvedValue({
      success: true,
      result: {
        firstName: 'John',
        lastName: 'Doe',
        confidence: 0.95
      },
      cost: 0.01,
      modelUsed: 'test-model'
    });

    // Mock successful limits check by default
    mockCostTracker.checkLimits.mockReturnValue({ allowed: true, violations: [] });
    mockCostTracker.checkRateLimit.mockReturnValue({ allowed: true });
    
    // Mock loadImageData method
    batchProcessor.loadImageData = jest.fn().mockResolvedValue(Buffer.from('test image data'));
  });

  describe('Progress Calculation', () => {
    test('should calculate progress correctly for single image', async () => {
      const job = await batchProcessor.createJob({
        images: ['image1.jpg'],
        modelId: 'test-model'
      });

      // Wait for job processing to complete
      await new Promise(resolve => {
        batchProcessor.on('jobCompleted', resolve);
      });

      expect(job.progress).toBe(100);
      expect(eventSpy.jobProgress).toHaveBeenCalledWith(
        expect.objectContaining({
          id: job.id,
          progress: 100
        })
      );
    });

    test('should calculate progress correctly for multiple images', async () => {
      const images = ['image1.jpg', 'image2.jpg', 'image3.jpg', 'image4.jpg'];
      const job = await batchProcessor.createJob({
        images,
        modelId: 'test-model'
      });

      const progressEvents = [];
      batchProcessor.on('jobProgress', (jobData) => {
        progressEvents.push(jobData.progress);
      });

      // Wait for job processing
      await new Promise(resolve => {
        batchProcessor.on('jobCompleted', resolve);
      });

      // Should have progress events for each image: 25%, 50%, 75%, 100%
      expect(progressEvents).toEqual([25, 50, 75, 100]);
      expect(job.progress).toBe(100);
    });

    test('should handle progress calculation with odd number of images', async () => {
      const images = ['image1.jpg', 'image2.jpg', 'image3.jpg'];
      const job = await batchProcessor.createJob({
        images,
        modelId: 'test-model'
      });

      const progressEvents = [];
      batchProcessor.on('jobProgress', (jobData) => {
        progressEvents.push(jobData.progress);
      });

      await new Promise(resolve => {
        batchProcessor.on('jobCompleted', resolve);
      });

      // Should be rounded: 33%, 67%, 100%
      expect(progressEvents).toEqual([33, 67, 100]);
    });

    test('should maintain progress when some images fail', async () => {
      mockOcrService.processImage
        .mockResolvedValueOnce({ success: true, result: {}, cost: 0.01, modelUsed: 'test-model' })
        .mockRejectedValueOnce(new Error('Processing failed'))
        .mockResolvedValueOnce({ success: true, result: {}, cost: 0.01, modelUsed: 'test-model' });

      const job = await batchProcessor.createJob({
        images: ['image1.jpg', 'image2.jpg', 'image3.jpg'],
        modelId: 'test-model'
      });

      const progressEvents = [];
      batchProcessor.on('jobProgress', (jobData) => {
        progressEvents.push(jobData.progress);
      });

      await new Promise(resolve => {
        batchProcessor.on('jobCompleted', resolve);
      });

      // Progress should continue even with failures - only successful images increment processedCount
      expect(progressEvents).toEqual([33, 67, 100]);
      expect(job.errors).toHaveLength(1);
      expect(job.processedCount).toBe(2); // Only 2 successful
    });
  });

  describe('Status Transitions', () => {
    test('should transition through correct status phases', async () => {
      // Create job but don't start processing immediately by controlling the queue
      const originalProcessQueue = batchProcessor.processQueue;
      batchProcessor.processQueue = jest.fn();

      const job = await batchProcessor.createJob({
        images: ['image1.jpg'],
        modelId: 'test-model'
      });

      // Initial status should be pending
      expect(job.status).toBe('pending');

      // Restore original processQueue and start processing
      batchProcessor.processQueue = originalProcessQueue;
      
      const statusEvents = [];
      batchProcessor.on('jobStarted', (jobData) => statusEvents.push('started'));
      batchProcessor.on('jobProgress', (jobData) => statusEvents.push('progress'));
      batchProcessor.on('jobCompleted', (jobData) => statusEvents.push('completed'));

      // Start processing
      batchProcessor.processQueue();

      await new Promise(resolve => {
        batchProcessor.on('jobCompleted', resolve);
      });

      // Should have transitioned: pending -> processing -> completed
      expect(statusEvents).toEqual(['started', 'progress', 'completed']);
      expect(job.status).toBe('completed');
      expect(job.startTime).toBeTruthy();
      expect(job.endTime).toBeTruthy();
    });

    test('should handle job cancellation', async () => {
      // Create job without auto-processing
      const originalProcessQueue = batchProcessor.processQueue;
      batchProcessor.processQueue = jest.fn();

      const job = await batchProcessor.createJob({
        images: ['image1.jpg'],
        modelId: 'test-model'
      });

      // Immediately cancel the job
      const cancelledJob = await batchProcessor.cancelJob(job.id);
      expect(cancelledJob.status).toBe('cancelled');
      expect(cancelledJob.endTime).toBeTruthy();
      expect(eventSpy.jobCancelled).toHaveBeenCalledWith(cancelledJob);

      // Restore processQueue
      batchProcessor.processQueue = originalProcessQueue;
    });

    test('should handle job retry after failure', async () => {
      mockOcrService.processImage.mockRejectedValue(new Error('Critical error'));

      const job = await batchProcessor.createJob({
        images: ['image1.jpg'],
        modelId: 'test-model'
      });

      await new Promise(resolve => {
        batchProcessor.on('jobFailed', resolve);
      });

      expect(job.status).toBe('failed');

      // Reset mock for retry
      mockOcrService.processImage.mockResolvedValue({
        success: true,
        result: {},
        cost: 0.01,
        modelUsed: 'test-model'
      });

      const retriedJob = await batchProcessor.retryJob(job.id);
      expect(retriedJob.status).toBe('pending');
      expect(retriedJob.progress).toBe(0);
      expect(retriedJob.results).toHaveLength(0);
      expect(retriedJob.errors).toHaveLength(0);
    });
  });

  describe('Error State Handling', () => {
    test('should handle individual image processing errors', async () => {
      mockOcrService.processImage
        .mockResolvedValueOnce({ success: true, result: {}, cost: 0.01, modelUsed: 'test-model' })
        .mockRejectedValueOnce(new Error('Processing failed'))
        .mockResolvedValueOnce({ success: true, result: {}, cost: 0.01, modelUsed: 'test-model' });

      const job = await batchProcessor.createJob({
        images: ['image1.jpg', 'image2.jpg', 'image3.jpg'],
        modelId: 'test-model'
      });

      await new Promise(resolve => {
        batchProcessor.on('jobCompleted', resolve);
      });

      expect(job.status).toBe('completed');
      expect(job.errors).toHaveLength(1);
      expect(job.errors[0]).toEqual({
        imageId: 'image2.jpg',
        error: 'Processing failed'
      });
      expect(job.processedCount).toBe(2); // Only successful ones
    });

    test('should fail job on rate limit errors', async () => {
      mockOcrService.processImage.mockRejectedValue(new Error('Rate limit exceeded'));

      const job = await batchProcessor.createJob({
        images: ['image1.jpg', 'image2.jpg'],
        modelId: 'test-model'
      });

      await new Promise(resolve => {
        batchProcessor.on('jobFailed', resolve);
      });

      expect(job.status).toBe('failed');
      expect(eventSpy.jobFailed).toHaveBeenCalledWith(job);
      expect(job.errors).toHaveLength(1);
      expect(job.errors[0].error).toBe('Rate limit exceeded');
    });

    test('should fail job on limits exceeded errors', async () => {
      mockOcrService.processImage.mockRejectedValue(new Error('Limits exceeded: cost_limit'));

      const job = await batchProcessor.createJob({
        images: ['image1.jpg'],
        modelId: 'test-model'
      });

      await new Promise(resolve => {
        batchProcessor.on('jobFailed', resolve);
      });

      expect(job.status).toBe('failed');
      expect(job.errors[0].error).toBe('Limits exceeded: cost_limit');
    });

    test('should handle critical job-level errors', async () => {
      // Mock a critical error during job setup
      batchProcessor.loadImageData = jest.fn().mockRejectedValue(new Error('Critical system error'));

      const job = await batchProcessor.createJob({
        images: ['image1.jpg'],
        modelId: 'test-model'
      });

      await new Promise(resolve => {
        batchProcessor.on('jobFailed', resolve);
      });

      expect(job.status).toBe('failed');
      expect(job.errors).toContainEqual({
        error: 'Critical system error',
        critical: true
      });
    });

    test('should continue processing after non-critical errors', async () => {
      mockOcrService.processImage
        .mockRejectedValueOnce(new Error('Non-critical error'))
        .mockResolvedValueOnce({ success: true, result: {}, cost: 0.01, modelUsed: 'test-model' });

      const job = await batchProcessor.createJob({
        images: ['image1.jpg', 'image2.jpg'],
        modelId: 'test-model'
      });

      await new Promise(resolve => {
        batchProcessor.on('jobCompleted', resolve);
      });

      expect(job.status).toBe('completed');
      expect(job.errors).toHaveLength(1);
      expect(job.processedCount).toBe(1);
      expect(job.progress).toBe(100);
    });
  });

  describe('Event Emission', () => {
    test('should emit jobStarted event with correct data', async () => {
      const job = await batchProcessor.createJob({
        images: ['image1.jpg'],
        modelId: 'test-model'
      });

      await new Promise(resolve => {
        batchProcessor.on('jobStarted', resolve);
      });

      expect(eventSpy.jobStarted).toHaveBeenCalledWith(
        expect.objectContaining({
          id: job.id,
          status: 'processing',
          startTime: expect.any(String)
        })
      );
    });

    test('should emit jobProgress events with accurate data', async () => {
      const job = await batchProcessor.createJob({
        images: ['image1.jpg', 'image2.jpg'],
        modelId: 'test-model'
      });

      const progressEvents = [];
      batchProcessor.on('jobProgress', (jobData) => {
        progressEvents.push({
          progress: jobData.progress,
          processedCount: jobData.processedCount
        });
      });

      await new Promise(resolve => {
        batchProcessor.on('jobCompleted', resolve);
      });

      expect(progressEvents).toEqual([
        { progress: 50, processedCount: 1 },
        { progress: 100, processedCount: 2 }
      ]);
    });

    test('should emit jobCompleted event with final stats', async () => {
      const job = await batchProcessor.createJob({
        images: ['image1.jpg', 'image2.jpg'],
        modelId: 'test-model'
      });

      await new Promise(resolve => {
        batchProcessor.on('jobCompleted', resolve);
      });

      expect(eventSpy.jobCompleted).toHaveBeenCalledWith(
        expect.objectContaining({
          id: job.id,
          status: 'completed',
          progress: 100,
          processedCount: 2,
          endTime: expect.any(String)
        })
      );
    });

    test('should emit jobFailed event on critical failures', async () => {
      mockOcrService.processImage.mockRejectedValue(new Error('Rate limit exceeded'));

      const job = await batchProcessor.createJob({
        images: ['image1.jpg'],
        modelId: 'test-model'
      });

      await new Promise(resolve => {
        batchProcessor.on('jobFailed', resolve);
      });

      expect(eventSpy.jobFailed).toHaveBeenCalledWith(
        expect.objectContaining({
          id: job.id,
          status: 'failed',
          endTime: expect.any(String),
          errors: expect.arrayContaining([
            expect.objectContaining({
              error: 'Rate limit exceeded'
            })
          ])
        })
      );
    });
  });

  describe('Concurrent Job Handling', () => {
    test('should handle multiple concurrent jobs with correct status tracking', async () => {
      const jobs = [];
      jobs.push(await batchProcessor.createJob({
        images: ['image1.jpg'],
        modelId: 'test-model'
      }));
      jobs.push(await batchProcessor.createJob({
        images: ['image2.jpg'],
        modelId: 'test-model'
      }));
      jobs.push(await batchProcessor.createJob({
        images: ['image3.jpg'],
        modelId: 'test-model'
      }));

      // Wait for all jobs to complete
      let completedCount = 0;
      await new Promise(resolve => {
        batchProcessor.on('jobCompleted', () => {
          completedCount++;
          if (completedCount === 3) {
            resolve();
          }
        });
      });

      jobs.forEach(job => {
        expect(job.status).toBe('completed');
      });

      const queueStatus = batchProcessor.getQueueStatus();
      expect(queueStatus.completedJobs).toBe(3);
      expect(queueStatus.activeJobs).toBe(0);
    });

    test('should respect maxConcurrentJobs limit', async () => {
      // Set a lower limit for testing
      batchProcessor.maxConcurrentJobs = 2;

      const jobs = [];
      for (let i = 0; i < 5; i++) {
        jobs.push(await batchProcessor.createJob({
          images: [`image${i}.jpg`],
          modelId: 'test-model'
        }));
      }

      // Give some time for jobs to start
      await new Promise(resolve => setTimeout(resolve, 50));

      // Check that no more than 2 jobs are active at once
      const queueStatus = batchProcessor.getQueueStatus();
      expect(queueStatus.activeJobs).toBeLessThanOrEqual(2);
      expect(queueStatus.queueLength + queueStatus.activeJobs).toBeGreaterThan(0);
    });

    test('should track queue status correctly', () => {
      const status = batchProcessor.getQueueStatus();
      
      expect(status).toEqual({
        queueLength: expect.any(Number),
        activeJobs: expect.any(Number),
        maxConcurrentJobs: expect.any(Number),
        isProcessing: expect.any(Boolean),
        totalJobs: expect.any(Number),
        completedJobs: expect.any(Number),
        failedJobs: expect.any(Number),
        pendingJobs: expect.any(Number)
      });
    });
  });

  describe('Job Filtering and Retrieval', () => {
    test('should filter jobs by status correctly', async () => {
      const job1 = await batchProcessor.createJob({
        images: ['image1.jpg'],
        modelId: 'test-model'
      });

      // Wait for first job to complete
      await new Promise(resolve => {
        batchProcessor.on('jobCompleted', (completedJob) => {
          if (completedJob.id === job1.id) {
            resolve();
          }
        });
      });

      // Create second job that will fail
      mockOcrService.processImage.mockRejectedValue(new Error('Test failure'));
      const job2 = await batchProcessor.createJob({
        images: ['image2.jpg'],
        modelId: 'test-model'
      });

      // Wait for second job to fail
      await new Promise(resolve => {
        batchProcessor.on('jobFailed', (failedJob) => {
          if (failedJob.id === job2.id) {
            resolve();
          }
        });
      });

      const completedJobs = batchProcessor.getJobsByStatus('completed');
      const failedJobs = batchProcessor.getJobsByStatus('failed');

      expect(completedJobs).toHaveLength(1);
      expect(failedJobs).toHaveLength(1);
      expect(completedJobs[0].id).toBe(job1.id);
      expect(failedJobs[0].id).toBe(job2.id);
    });
  });

  describe('Cost and Processing Tracking', () => {
    test('should track total cost correctly across images', async () => {
      mockOcrService.processImage
        .mockResolvedValueOnce({ success: true, result: {}, cost: 0.05, modelUsed: 'test-model' })
        .mockResolvedValueOnce({ success: true, result: {}, cost: 0.03, modelUsed: 'test-model' });

      const job = await batchProcessor.createJob({
        images: ['image1.jpg', 'image2.jpg'],
        modelId: 'test-model'
      });

      await new Promise(resolve => {
        batchProcessor.on('jobCompleted', resolve);
      });

      expect(job.totalCost).toBe(0.08);
      expect(job.processedCount).toBe(2);
    });

    test('should not increment processed count for failed images', async () => {
      mockOcrService.processImage
        .mockResolvedValueOnce({ success: true, result: {}, cost: 0.05, modelUsed: 'test-model' })
        .mockResolvedValueOnce({ success: false, error: 'Failed processing' });

      const job = await batchProcessor.createJob({
        images: ['image1.jpg', 'image2.jpg'],
        modelId: 'test-model'
      });

      await new Promise(resolve => {
        batchProcessor.on('jobCompleted', resolve);
      });

      expect(job.totalCost).toBe(0.05); // Only successful image
      expect(job.processedCount).toBe(1); // Only successful image
      expect(job.errors).toHaveLength(1);
    });
  });

  describe('Progress Calculation Edge Cases', () => {
    test('should handle zero progress correctly', () => {
      // Test initial state
      expect(0).toBe(0); // Placeholder for zero progress case
    });

    test('should handle 100% progress correctly', async () => {
      const job = await batchProcessor.createJob({
        images: ['image1.jpg'],
        modelId: 'test-model'
      });

      await new Promise(resolve => {
        batchProcessor.on('jobCompleted', resolve);
      });

      expect(job.progress).toBe(100);
    });

    test('should calculate intermediate progress correctly', () => {
      // Test progress calculation formula: Math.round(((i + 1) / images.length) * 100)
      const testCases = [
        { completed: 1, total: 3, expected: 33 },
        { completed: 2, total: 3, expected: 67 },
        { completed: 3, total: 3, expected: 100 },
        { completed: 1, total: 4, expected: 25 },
        { completed: 2, total: 4, expected: 50 },
        { completed: 3, total: 4, expected: 75 },
        { completed: 4, total: 4, expected: 100 }
      ];

      testCases.forEach(({ completed, total, expected }) => {
        const progress = Math.round((completed / total) * 100);
        expect(progress).toBe(expected);
      });
    });
  });
});