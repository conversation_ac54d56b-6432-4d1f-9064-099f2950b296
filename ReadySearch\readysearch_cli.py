#!/usr/bin/env python3
"""
ReadySearch Unified CLI v4.0 - All-in-One Production Ready Tool
Combines all previous CLI versions into a single, intelligent interface

Replaces:
- enhanced_cli_final.py
- enhanced_cli_with_chunking.py  
- enhanced_cli.py
- optimized_batch_cli.py
- production_cli.py

Usage:
    python readysearch_cli.py "<PERSON>"                    # Single search
    python readysearch_cli.py "<PERSON>,1990"               # With birth year
    python readysearch_cli.py "<PERSON>;Jane,1985;Bob"            # Batch search
    python readysearch_cli.py --interactive                   # Interactive mode
    python readysearch_cli.py --mode=json "<PERSON>"        # JSON output
"""

import asyncio
import sys
import json
import csv
import time
import argparse
import os
import gc
import traceback
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union
from dataclasses import dataclass, asdict

# Optional dependencies with graceful fallbacks
try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.progress import Progress, SpinnerColumn, TextC<PERSON>umn, Bar<PERSON><PERSON>umn, TimeElapsedColumn
    from rich.prompt import Prompt, Confirm
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    from playwright.async_api import async_playwright, Browser, BrowserContext
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

# Import core ReadySearch functionality
sys.path.append(str(Path(__file__).parent))
from config import Config
from readysearch_automation.input_loader import SearchRecord
from readysearch_automation.advanced_name_matcher import AdvancedNameMatcher, MatchType


@dataclass
class SearchResult:
    """Unified search result structure"""
    name: str
    status: str
    search_duration: float
    matches_found: int
    exact_matches: int
    partial_matches: int
    match_category: str
    match_reasoning: str
    detailed_results: List[Dict[str, Any]]
    timestamp: str
    birth_year: Optional[int] = None
    error: Optional[str] = None
    chunk_id: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class ChunkingConfig:
    """Configuration for intelligent chunking"""
    max_chunk_size: int = 15
    min_chunk_size: int = 5
    enable_optimization: bool = True
    memory_threshold: float = 80.0
    pause_between_chunks: float = 2.0


class ReadySearchCLI:
    """Unified ReadySearch CLI with all features"""
    
    def __init__(self, enable_rich: bool = True):
        self.config = Config.get_config()
        self.matcher = AdvancedNameMatcher()
        self.session_results: List[SearchResult] = []
        self.chunking_config = ChunkingConfig()
        
        # Initialize console
        if RICH_AVAILABLE and enable_rich:
            self.console = Console()
            self.rich_enabled = True
        else:
            self.console = None
            self.rich_enabled = False
            
        # Performance tracking
        self.stats = {
            'total_searches': 0,
            'successful_searches': 0,
            'total_duration': 0.0,
            'average_duration': 0.0
        }
    
    def print(self, message: str, style: str = None):
        """Unified print method with optional Rich styling"""
        if self.rich_enabled and style:
            self.console.print(message, style=style)
        elif self.rich_enabled:
            self.console.print(message)
        else:
            print(message)
    
    def print_panel(self, content: str, title: str = None):
        """Print content in a panel if Rich is available"""
        if self.rich_enabled:
            self.console.print(Panel(content, title=title))
        else:
            if title:
                print(f"\n=== {title} ===")
            print(content)
            print("=" * (len(title) + 8) if title else "")
    
    async def search_person(self, search_record: SearchRecord) -> SearchResult:
        """Core search functionality - unified from production_cli.py"""
        start_time = time.time()
        
        self.print(f"🎯 Searching for: {search_record.name}")
        if search_record.birth_year:
            self.print(f"📅 Birth year: {search_record.birth_year} (searching {search_record.birth_year-2} to {search_record.birth_year+2})")
        
        async with async_playwright() as p:
            try:
                # Launch browser with optimized settings
                self.print("🚀 Launching browser...")
                browser = await p.chromium.launch(
                    headless=True,
                    args=['--no-sandbox', '--disable-dev-shm-usage', '--disable-gpu']
                )
                context = await browser.new_context()
                page = await context.new_page()
                
                # Navigate to ReadySearch
                self.print("🌐 Navigating to ReadySearch...")
                await page.goto("https://readysearch.com.au/products?person", timeout=15000, wait_until="networkidle")
                self.print("✅ Page loaded")
                
                # Find and fill search input
                self.print("🔍 Finding search input...")
                name_input = await page.wait_for_selector('input[name="search"]', timeout=5000)
                self.print("✅ Found name input field")
                
                # Enter name
                self.print(f"⌨️ Entering name: {search_record.name}")
                await name_input.click()
                await name_input.fill(search_record.name)
                self.print("✅ Name entered")
                
                # Set birth year range if provided
                if search_record.birth_year:
                    start_year = search_record.birth_year - 2
                    end_year = search_record.birth_year + 2
                    
                    self.print(f"📅 Setting birth year range: {start_year} to {end_year}")
                    
                    # Start year
                    start_select = await page.wait_for_selector('select[name="yobs"]', timeout=3000)
                    await start_select.select_option(str(start_year))
                    self.print(f"✅ Start year set to {start_year}")
                    
                    # End year  
                    end_select = await page.wait_for_selector('select[name="yobe"]', timeout=3000)
                    await end_select.select_option(str(end_year))
                    self.print(f"✅ End year set to {end_year}")
                
                # Submit search
                self.print("🚀 Submitting search...")
                submit_button = await page.wait_for_selector('.sch_but', timeout=3000)
                await submit_button.click()
                self.print("✅ Search submitted")
                
                # Handle popup if it appears
                try:
                    await page.wait_for_selector('text="ONE PERSON MAY HAVE MULTIPLE RECORDS"', timeout=3000)
                    self.print("ℹ️ Popup detected, dismissing...")
                    dismiss_button = await page.wait_for_selector('text="Dismiss"', timeout=2000)
                    await dismiss_button.click()
                    self.print("✅ Popup dismissed")
                except:
                    self.print("ℹ️ No popup appeared")
                
                # Wait for results page to load - using working approach from production_cli.py
                self.print("⏳ Waiting for results...")
                await page.wait_for_load_state('networkidle', timeout=30000)
                self.print("✅ Results page loaded")
                
                # Check for no results with enhanced detection
                page_content = await page.content()
                page_text = await page.text_content('body') if await page.query_selector('body') else ""
                
                # More comprehensive no-results detection
                no_results_indicators = [
                    "Sorry, no results",
                    "no results found",
                    "no matches found",
                    "0 results",
                    "Your search returned no results"
                ]
                
                has_no_results = any(indicator.lower() in page_content.lower() or 
                                   indicator.lower() in page_text.lower() 
                                   for indicator in no_results_indicators)
                
                if has_no_results:
                    duration = time.time() - start_time
                    self.print("❌ No results found")
                    
                    return SearchResult(
                        name=search_record.name,
                        status="No Match",
                        search_duration=duration,
                        matches_found=0,
                        exact_matches=0,
                        partial_matches=0,
                        match_category="NO MATCH",
                        match_reasoning="No results found in database",
                        detailed_results=[],
                        timestamp=datetime.now().isoformat(),
                        birth_year=search_record.birth_year
                    )
                
                # Extract results using working approach from production_cli.py
                self.print("📊 Extracting results...")
                
                # Extract results from table rows - same as working production_cli.py
                result_rows = await page.query_selector_all('tr')
                self.print(f"📋 Found {len(result_rows)} table rows")
                
                detailed_results = []
                matches_found = 0
                
                for i, row in enumerate(result_rows):
                    try:
                        # Extract text from row
                        row_text = await row.inner_text()
                        
                        # Print first few rows for debugging
                        if i < 15:
                            self.print(f"   🔍 Row {i+1}: {row_text[:100]}...")
                        
                        # Skip obviously irrelevant rows
                        if not row_text.strip():
                            continue
                        
                        # Look for ReadySearch result patterns
                        row_text_clean = row_text.strip()
                        
                        # Look for "Date of Birth:" pattern which indicates a result
                        if "Date of Birth:" in row_text_clean:
                            self.print(f"   🎯 Found result row {i+1}: {row_text_clean}")
                            
                            # Extract name and date from the pattern
                            if "|" in row_text_clean:
                                # Split by pipe separators
                                parts = row_text_clean.split("|")
                                
                                for part in parts:
                                    part = part.strip()
                                    if "Date of Birth:" in part:
                                        # Find the name (should be in a previous part or same part)
                                        name_part = ""
                                        date_part = part
                                        
                                        # Look for name in previous parts
                                        for prev_part in parts:
                                            prev_part = prev_part.strip()
                                            if prev_part and "Date of Birth:" not in prev_part and len(prev_part) > 2:
                                                # Check if this looks like a name
                                                if prev_part.replace(' ', '').replace('-', '').replace('.', '').isalpha():
                                                    name_part = prev_part
                                                    break
                                        
                                        # Extract date and location from "Date of Birth: XX/XX/XXXX LOCATION"
                                        date_match = ""
                                        location_match = ""
                                        if "Date of Birth:" in date_part:
                                            date_text = date_part.split("Date of Birth:")[1].strip()
                                            if date_text:
                                                # Split date and location
                                                parts = date_text.split()
                                                if parts:
                                                    date_match = parts[0]  # First part is the date
                                                    if len(parts) > 1:
                                                        location_match = ' '.join(parts[1:])  # Rest is location
                                        
                                        if name_part:
                                            self.print(f"      📝 Clean extraction: Name='{name_part}', Date='{date_match}', Location='{location_match}'")
                                            
                                            # Use STRICT advanced matcher to enforce last name exact matching
                                            exact_first_name = getattr(search_record, 'exact_matching', False)
                                            match_result = self.matcher.match_names_strict(search_record.name, name_part, exact_first_name)
                                            
                                            if match_result.match_type != MatchType.NOT_MATCHED:
                                                matches_found += 1
                                                detailed_results.append({
                                                    'matched_name': name_part,
                                                    'date_of_birth': date_match,
                                                    'location': location_match,
                                                    'match_type': match_result.get_display_category(),
                                                    'match_reasoning': match_result.reasoning,
                                                    'confidence': match_result.confidence
                                                })
                                                
                                                self.print(f"      ✅ MATCH {matches_found}: {name_part} ({match_result.get_display_category()}) - {match_result.reasoning}")
                                            else:
                                                self.print(f"      ❌ No match: {name_part} - {match_result.reasoning}")
                                        break
                            else:
                                # Alternative pattern without pipes - handle line-separated data
                                # Try to extract name and date from the line
                                if "Date of Birth:" in row_text_clean:
                                    # Look for lines like: "ANDRO CUTUK\nDate of Birth: 12/06/1975\tSYDNEY NSW"
                                    lines = row_text_clean.split('\n')
                                    
                                    for i, line in enumerate(lines):
                                        if "Date of Birth:" in line:
                                            # The name should be in the previous line or beginning of this line
                                            name_candidates = []
                                            
                                            # Check previous line
                                            if i > 0:
                                                prev_line = lines[i-1].strip()
                                                if prev_line and len(prev_line) > 2 and prev_line.replace(' ', '').replace('-', '').replace('.', '').isalpha():
                                                    name_candidates.append(prev_line)
                                            
                                            # Check if name is at the beginning of current line
                                            if line.split("Date of Birth:")[0].strip():
                                                candidate = line.split("Date of Birth:")[0].strip()
                                                if len(candidate) > 2 and candidate.replace(' ', '').replace('-', '').replace('.', '').isalpha():
                                                    name_candidates.append(candidate)
                                            
                                            # Extract date and location
                                            date_part = ""
                                            location_part = ""
                                            if "Date of Birth:" in line:
                                                birth_text = line.split("Date of Birth:")[1].strip()
                                                if birth_text:
                                                    parts = birth_text.split()
                                                    if parts:
                                                        date_part = parts[0]  # First part is the date
                                                        if len(parts) > 1:
                                                            location_part = ' '.join(parts[1:])  # Rest is location
                                            
                                            # Process each name candidate
                                            for potential_name in name_candidates:
                                                if potential_name and len(potential_name) <= 50:  # Reasonable name length
                                                    self.print(f"      📝 Clean extraction: Name='{potential_name}', Date='{date_part}', Location='{location_part}'")
                                                    
                                                    # Use STRICT matching with user preference
                                                    exact_first_name = getattr(search_record, 'exact_matching', False)
                                                    match_result = self.matcher.match_names_strict(search_record.name, potential_name, exact_first_name)
                                                    
                                                    if match_result.match_type != MatchType.NOT_MATCHED:
                                                        matches_found += 1
                                                        detailed_results.append({
                                                            'matched_name': potential_name,
                                                            'date_of_birth': date_part,
                                                            'location': location_part,
                                                            'match_type': match_result.get_display_category(),
                                                            'match_reasoning': match_result.reasoning,
                                                            'confidence': match_result.confidence
                                                        })
                                                        
                                                        self.print(f"      ✅ MATCH {matches_found}: {potential_name} ({match_result.get_display_category()})")
                                                    break  # Only take first valid match per row
                    
                    except Exception as e:
                        # Skip rows that can't be processed
                        self.print(f"      ⚠️ Error processing row {i+1}: {e}")
                        continue
                
                # Categorize results
                exact_matches = len([r for r in detailed_results if 'EXACT' in r['match_type']])
                partial_matches = len([r for r in detailed_results if 'PARTIAL' in r['match_type']])
                
                duration = time.time() - start_time
                
                # Determine overall match category
                if exact_matches > 0:
                    match_category = "EXACT MATCH"
                    status = "Match"
                    reasoning = f"Found {exact_matches} exact matches"
                elif partial_matches > 0:
                    match_category = "PARTIAL MATCH"
                    status = "Match"
                    reasoning = f"Found {partial_matches} partial matches"
                elif matches_found > 0:
                    match_category = "PARTIAL MATCH"
                    status = "Match"
                    reasoning = f"Found {matches_found} matches"
                else:
                    match_category = "NOT MATCHED"
                    status = "No Match"
                    reasoning = "No meaningful matches found"
                
                self.print(f"✅ Search completed in {duration:.2f}s - {match_category}")
                
                await browser.close()
                
                return SearchResult(
                    name=search_record.name,
                    status=status,
                    search_duration=duration,
                    matches_found=matches_found,
                    exact_matches=exact_matches,
                    partial_matches=partial_matches,
                    match_category=match_category,
                    match_reasoning=reasoning,
                    detailed_results=detailed_results,
                    timestamp=datetime.now().isoformat(),
                    birth_year=search_record.birth_year
                )
                
            except Exception as e:
                duration = time.time() - start_time
                error_msg = f"Search failed: {str(e)}"
                self.print(f"❌ {error_msg}", style="red")
                
                try:
                    await browser.close()
                except:
                    pass
                
                return SearchResult(
                    name=search_record.name,
                    status="Error",
                    search_duration=duration,
                    matches_found=0,
                    exact_matches=0,
                    partial_matches=0,
                    match_category="ERROR",
                    match_reasoning="Search failed due to technical error",
                    detailed_results=[],
                    timestamp=datetime.now().isoformat(),
                    birth_year=search_record.birth_year,
                    error=error_msg
                )
    
    def parse_names_input(self, names_input: str) -> List[SearchRecord]:
        """Parse various input formats into SearchRecord objects"""
        if not names_input or not names_input.strip():
            return []
        
        # Split by semicolon for multiple names
        name_parts = [part.strip() for part in names_input.split(';') if part.strip()]
        records = []
        
        for part in name_parts:
            if ',' in part:
                # Name with birth year: "John Smith,1990"
                name, year_str = part.rsplit(',', 1)
                name = name.strip()
                try:
                    birth_year = int(year_str.strip())
                    records.append(SearchRecord(name=name, birth_year=birth_year))
                except ValueError:
                    # Invalid year, treat as part of name
                    records.append(SearchRecord(name=part))
            else:
                # Name only
                records.append(SearchRecord(name=part))
        
        return records
    
    def should_use_chunking(self, records: List[SearchRecord]) -> bool:
        """Determine if chunking should be used based on batch size"""
        return len(records) > 10  # Use chunking for batches > 10
    
    def get_memory_usage(self) -> float:
        """Get current memory usage percentage"""
        if PSUTIL_AVAILABLE:
            return psutil.virtual_memory().percent
        return 0.0
    
    def create_chunks(self, records: List[SearchRecord]) -> List[List[SearchRecord]]:
        """Split records into optimized chunks"""
        if not self.should_use_chunking(records):
            return [records]
        
        chunk_size = self.chunking_config.max_chunk_size
        
        # Adjust chunk size based on memory usage
        memory_usage = self.get_memory_usage()
        if memory_usage > self.chunking_config.memory_threshold:
            chunk_size = max(self.chunking_config.min_chunk_size, chunk_size // 2)
            self.print(f"⚠️ High memory usage ({memory_usage:.1f}%), reducing chunk size to {chunk_size}")
        
        chunks = []
        for i in range(0, len(records), chunk_size):
            chunk = records[i:i + chunk_size]
            chunks.append(chunk)
        
        return chunks
    
    async def perform_batch_search(self, records: List[SearchRecord]) -> List[SearchResult]:
        """Perform batch search with intelligent chunking"""
        if not records:
            return []
        
        # Single search optimization
        if len(records) == 1:
            result = await self.search_person(records[0])
            self.session_results.append(result)
            self.update_stats(result)
            return [result]
        
        all_results = []
        
        if self.should_use_chunking(records):
            # Use chunking for large batches
            chunks = self.create_chunks(records)
            
            self.print_panel(
                f"🚀 Intelligent Chunking Activated\n"
                f"• Total searches: {len(records)}\n"
                f"• Chunks: {len(chunks)}\n"
                f"• Average chunk size: {len(records) / len(chunks):.1f}\n"
                f"• Memory usage: {self.get_memory_usage():.1f}%",
                "Batch Processing"
            )
            
            for chunk_idx, chunk in enumerate(chunks, 1):
                self.print(f"\n📦 Processing chunk {chunk_idx}/{len(chunks)} ({len(chunk)} searches)")
                
                # Process chunk
                chunk_results = []
                for search_record in chunk:
                    result = await self.search_person(search_record)
                    result.chunk_id = chunk_idx
                    chunk_results.append(result)
                    self.session_results.append(result)
                    self.update_stats(result)
                
                all_results.extend(chunk_results)
                
                # Pause between chunks for stability
                if chunk_idx < len(chunks):
                    self.print(f"⏸️ Pausing {self.chunking_config.pause_between_chunks}s between chunks...")
                    await asyncio.sleep(self.chunking_config.pause_between_chunks)
                    
                    # Force garbage collection
                    gc.collect()
        else:
            # Process small batches sequentially
            self.print(f"📋 Processing {len(records)} searches sequentially...")
            for search_record in records:
                result = await self.search_person(search_record)
                all_results.append(result)
                self.session_results.append(result)
                self.update_stats(result)
        
        return all_results
    
    def update_stats(self, result: SearchResult):
        """Update performance statistics"""
        self.stats['total_searches'] += 1
        if result.status != "Error":
            self.stats['successful_searches'] += 1
        self.stats['total_duration'] += result.search_duration
        self.stats['average_duration'] = self.stats['total_duration'] / self.stats['total_searches']
    
    def display_results(self, results: List[SearchResult], format_type: str = "console"):
        """Display results in various formats"""
        if format_type == "json":
            # JSON output for DL Organizer integration
            export_data = {
                "export_info": {
                    "timestamp": datetime.now().isoformat(),
                    "total_results": len(results),
                    "tool_version": "ReadySearch Unified CLI v4.0"
                },
                "results": [result.to_dict() for result in results]
            }
            print(json.dumps(export_data, indent=2))
            return
        
        # Console output
        if not results:
            self.print("No results to display.", style="yellow")
            return
        
        if self.rich_enabled:
            # Rich table output
            table = Table(title="Search Results")
            table.add_column("Name", style="cyan")
            table.add_column("Status", style="green")
            table.add_column("Duration", style="yellow")
            table.add_column("Matches", style="blue")
            table.add_column("Category", style="magenta")
            
            for result in results:
                table.add_row(
                    result.name,
                    result.status,
                    f"{result.search_duration:.2f}s",
                    str(result.matches_found),
                    result.match_category
                )
            
            self.console.print(table)
        else:
            # Basic console output
            print("\n" + "="*60)
            print("SEARCH RESULTS")
            print("="*60)
            for i, result in enumerate(results, 1):
                print(f"{i}. {result.name}")
                print(f"   Status: {result.status}")
                print(f"   Duration: {result.search_duration:.2f}s")
                print(f"   Matches: {result.matches_found}")
                print(f"   Category: {result.match_category}")
                print("-" * 40)
    
    def export_results(self, results: List[SearchResult], format_type: str, filename: str = None):
        """Export results to file"""
        if not results:
            self.print("No results to export.", style="yellow")
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if format_type.lower() == "json":
            filename = filename or f"readysearch_results_{timestamp}.json"
            export_data = {
                "export_info": {
                    "timestamp": datetime.now().isoformat(),
                    "total_results": len(results),
                    "tool_version": "ReadySearch Unified CLI v4.0"
                },
                "results": [result.to_dict() for result in results]
            }
            
            with open(filename, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            self.print(f"✅ Results exported to {filename}")
        
        elif format_type.lower() == "csv":
            filename = filename or f"readysearch_results_{timestamp}.csv"
            
            with open(filename, 'w', newline='') as f:
                writer = csv.writer(f)
                # Header
                writer.writerow([
                    "Name", "Status", "Search Duration (s)", "Matches Found", 
                    "Exact Matches", "Partial Matches", "Match Category", "Birth Year"
                ])
                
                # Data
                for result in results:
                    writer.writerow([
                        result.name, result.status, result.search_duration,
                        result.matches_found, result.exact_matches, result.partial_matches,
                        result.match_category, result.birth_year or ""
                    ])
            
            self.print(f"✅ Results exported to {filename}")
        
        elif format_type.lower() == "txt":
            filename = filename or f"readysearch_results_{timestamp}.txt"
            
            with open(filename, 'w') as f:
                f.write("ReadySearch Results Report\n")
                f.write("=" * 50 + "\n")
                f.write(f"Generated: {datetime.now().isoformat()}\n")
                f.write(f"Total Results: {len(results)}\n\n")
                
                for i, result in enumerate(results, 1):
                    f.write(f"{i}. {result.name}\n")
                    f.write(f"   Status: {result.status}\n")
                    f.write(f"   Duration: {result.search_duration:.2f}s\n")
                    f.write(f"   Matches: {result.matches_found}\n")
                    f.write(f"   Category: {result.match_category}\n")
                    if result.birth_year:
                        f.write(f"   Birth Year: {result.birth_year}\n")
                    f.write("-" * 40 + "\n")
            
            self.print(f"✅ Results exported to {filename}")
    
    async def interactive_mode(self):
        """Interactive CLI mode"""
        self.print_panel(
            "🎯 ReadySearch Interactive Mode\n"
            "Enter names to search, or type 'help' for commands",
            "Welcome"
        )
        
        while True:
            try:
                if self.rich_enabled:
                    user_input = Prompt.ask("ReadySearch")
                else:
                    user_input = input("ReadySearch> ").strip()
                
                if not user_input:
                    continue
                
                command = user_input.lower()
                
                if command in ['exit', 'quit', 'q']:
                    self.print("👋 Goodbye!")
                    break
                elif command == 'help':
                    self.print_help()
                elif command == 'stats':
                    self.display_stats()
                elif command == 'results':
                    self.display_results(self.session_results)
                elif command.startswith('export '):
                    parts = command.split()
                    if len(parts) >= 2:
                        format_type = parts[1]
                        self.export_results(self.session_results, format_type)
                    else:
                        self.print("Usage: export <format>  (formats: json, csv, txt)")
                else:
                    # Treat as search query
                    records = self.parse_names_input(user_input)
                    if records:
                        results = await self.perform_batch_search(records)
                        self.display_results(results)
                    else:
                        self.print("❌ Invalid input format")
                        
            except KeyboardInterrupt:
                self.print("\n👋 Goodbye!")
                break
            except Exception as e:
                self.print(f"❌ Error: {str(e)}", style="red")
    
    def print_help(self):
        """Display help information"""
        help_text = """
Commands:
  <names>     Search for names (e.g., "John Smith" or "John,1990;Jane,1985")
  results     Display current session results
  stats       Show performance statistics
  export <fmt> Export results (formats: json, csv, txt)
  help        Show this help
  exit        Exit the program

Input Formats:
  John Smith              Single name
  John Smith,1990         Name with birth year
  John;Jane,1985;Bob      Multiple names (semicolon separated)
        """
        self.print_panel(help_text.strip(), "Help")
    
    def display_stats(self):
        """Display performance statistics"""
        stats_text = f"""
Total Searches: {self.stats['total_searches']}
Successful: {self.stats['successful_searches']}
Average Duration: {self.stats['average_duration']:.2f}s
Total Duration: {self.stats['total_duration']:.2f}s
Success Rate: {(self.stats['successful_searches'] / max(1, self.stats['total_searches']) * 100):.1f}%
        """
        self.print_panel(stats_text.strip(), "Statistics")


async def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="ReadySearch Unified CLI v4.0 - All-in-One Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python readysearch_cli.py "John Smith"                    # Single search
  python readysearch_cli.py "John Smith,1990"               # With birth year
  python readysearch_cli.py "John;Jane,1985;Bob"            # Batch search
  python readysearch_cli.py --interactive                   # Interactive mode
  python readysearch_cli.py --mode=json "John Smith"        # JSON output
        """
    )
    
    parser.add_argument(
        'names',
        nargs='?',
        help='Names to search for. Use semicolon (;) to separate multiple names. Use comma (,) to add birth year.'
    )
    parser.add_argument(
        '--interactive', '-i',
        action='store_true',
        help='Start interactive mode'
    )
    parser.add_argument(
        '--mode', '-m',
        choices=['console', 'json'],
        default='console',
        help='Output mode (default: console)'
    )
    parser.add_argument(
        '--batch',
        action='store_true',
        help='Force batch mode (non-interactive)'
    )
    parser.add_argument(
        '--no-rich',
        action='store_true',
        help='Disable Rich formatting'
    )
    parser.add_argument(
        '--export',
        choices=['json', 'csv', 'txt'],
        help='Export results to file'
    )
    
    args = parser.parse_args()
    
    # Initialize CLI
    cli = ReadySearchCLI(enable_rich=not args.no_rich)
    
    if args.interactive:
        # Interactive mode
        await cli.interactive_mode()
    elif args.names:
        # Direct search mode
        records = cli.parse_names_input(args.names)
        if records:
            results = await cli.perform_batch_search(records)
            cli.display_results(results, format_type=args.mode)
            
            # Export if requested
            if args.export:
                cli.export_results(results, args.export)
        else:
            cli.print("❌ Invalid input format")
            return 1
    else:
        # Show help if no arguments
        parser.print_help()
        return 1
    
    return 0


if __name__ == "__main__":
    if not PLAYWRIGHT_AVAILABLE:
        print("❌ Playwright is required. Install with: pip install playwright")
        print("   Then run: playwright install chromium")
        sys.exit(1)
    
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Fatal error: {str(e)}")
        traceback.print_exc()
        sys.exit(1)