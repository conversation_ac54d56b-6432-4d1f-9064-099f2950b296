// Core types for DL Organizer application

export interface Project {
  id: string
  name: string
  description?: string
  rootPath: string
  createdAt: Date
  updatedAt: Date
  settings: ProjectSettings
}

export interface ProjectSettings {
  defaultAIProvider: string
  autoSaveInterval: number
  thumbnailSize: number
  enableAutoBackup: boolean
  ocrOutputFormat: string
}

export interface FolderNode {
  id: string
  name: string
  path: string
  children: FolderNode[]
  imageCount: number
  hasTextFile: boolean
  isExpanded: boolean
  tags: string[]
  parentId?: string
  metadata: Record<string, any>
}

export interface ImageFile {
  id: string
  filename: string
  path: string
  relativePath: string
  thumbnailUrl: string
  previewUrl: string
  rotation: number
  lastModified: Date
  fileSize: number
  width: number
  height: number
  folderId: string
}

export interface OCRResult {
  id: string
  imageId: string
  folderId: string
  firstName: string
  middleName?: string // Optional middle name field
  lastName: string
  dateOfBirth: string // Format: YYYY-MM-DD
  address: string
  licenseNumber: string
  cardNumber?: string // Card number for Australian licenses (state-dependent)
  expirationDate: string // Format: YYYY-MM-DD
  issueDate: string // Format: YYYY-MM-DD
  state: string // Two-letter state code
  confidence: number
  rawText: string
  processedAt: Date
  mode: 'us' | 'australian' // OCR processing mode
  cardSide?: 'front' | 'back' // For Australian multi-side cards
  cached?: boolean // Whether this result was loaded from cache
  cacheTimestamp?: string | null // When the result was cached
  // Australian-specific fields
  surname?: string // Surname field for Australian format
  givenNames?: string // Given names (first + middle names combined) for Australian format
  // ReadySearch integration
  hasReadySearchCache?: boolean // Whether this image has ReadySearch results cached
  // Quality validation
  qualityWarnings?: QualityWarning[] // Data quality validation warnings
}

export interface QualityWarning {
  field: string
  message: string
  severity: 'info' | 'warning' | 'error'
}

export interface OpenAIConfig {
  model: 'gpt-4-vision-preview' | 'gpt-4o' | string
  maxTokens: number
  temperature: number
  isConfigured: boolean
  apiKey?: string
  ocrMode: 'us' | 'australian' // OCR processing mode
  customModel?: string // For custom OpenRouter models
}

export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface FolderScanResult {
  folders: FolderNode[]
  totalImages: number
  totalFolders: number
  lastScanned: Date
}

export interface ImageRotationRequest {
  imageId: string
  degrees: number
}

export interface OCRAnalysisRequest {
  imageId: string
  config: OpenAIConfig
}

export interface TextFileContent {
  content: string
  path: string
  lastModified: Date
  exists: boolean
}

export interface BackupData {
  projects: Project[]
  folders: FolderNode[]
  images: ImageFile[]
  ocrResults: OCRResult[]
  settings: ProjectSettings
  metadata: {
    exportDate: string
    version: string
    totalProjects: number
    totalFolders: number
    totalImages: number
  }
}

export interface StorageInfo {
  totalProjects: number
  totalFolders: number
  totalImages: number
  totalSize: number
  totalStorageUsed: number
  lastBackup: string
  backupCount: number
  isAvailable: boolean
}

// Australian driver license specific interface
export interface AustralianOCRResult extends Omit<OCRResult, 'issueDate'> {
  surname: string // Required field for Australian format
  givenNames: string // Given names (first + middle names combined)
  cardNumber?: string // Card number (state-dependent)
  mode: 'australian'
}

// Standard OCR Output Template for consistency
export const OCR_OUTPUT_TEMPLATE: Partial<OCRResult> = {
  firstName: "",
  middleName: "",
  lastName: "",
  dateOfBirth: "",
  address: "",
  licenseNumber: "",
  expirationDate: "",
  issueDate: "",
  state: "",
  mode: "us"
}

// Australian OCR Output Template
export const AUSTRALIAN_OCR_TEMPLATE: Partial<AustralianOCRResult> = {
  surname: "",
  givenNames: "",
  firstName: "", // Derived from givenNames
  middleName: "", // Derived from givenNames
  lastName: "", // Will be mapped from surname
  dateOfBirth: "",
  address: "",
  licenseNumber: "",
  cardNumber: "",
  expirationDate: "",
  state: "",
  mode: "australian"
}

// Supported image formats
export const SUPPORTED_IMAGE_FORMATS = [
  '.jpg', '.jpeg', '.png', '.gif', '.webp', '.tiff', '.bmp', '.pdf'
]

// Default project settings
export const DEFAULT_PROJECT_SETTINGS: ProjectSettings = {
  defaultAIProvider: 'openai',
  autoSaveInterval: 30000, // 30 seconds
  thumbnailSize: 150,
  enableAutoBackup: true,
  ocrOutputFormat: 'structured'
}

// Error types
export enum ErrorType {
  FILE_SYSTEM_ERROR = 'FILE_SYSTEM_ERROR',
  IMAGE_PROCESSING_ERROR = 'IMAGE_PROCESSING_ERROR',
  OCR_API_ERROR = 'OCR_API_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  PERMISSION_ERROR = 'PERMISSION_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR'
}

export interface ErrorResponse {
  success: false
  error: {
    type: ErrorType
    message: string
    code?: string
    details?: Record<string, any>
    retryable: boolean
  }
}