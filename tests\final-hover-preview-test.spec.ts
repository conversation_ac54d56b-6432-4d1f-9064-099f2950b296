import { test, expect } from '@playwright/test';

// Final test for 200% larger hover preview functionality
test.describe('Final Hover Preview Test', () => {
  test('should show 200% larger images in OCR Testing Playground', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Find the first image thumbnail
    const firstThumbnail = page.locator('img[width="48"]').first();
    await expect(firstThumbnail).toBeVisible();
    
    console.log('✅ Found thumbnail image (48x48)');
    
    // Get the parent group container
    const groupContainer = firstThumbnail.locator('..').locator('..');
    
    // Hover over the group container
    await groupContainer.hover();
    console.log('✅ Hovered over group container');
    
    // Wait for hover effect
    await page.waitForTimeout(1000);
    
    // Find the hover preview image - should be the first one visible
    const hoverPreview = page.locator('img[width="96"]').first();
    await expect(hoverPreview).toBeVisible();
    
    console.log('✅ Found hover preview image (96x96)');
    
    // Verify the hover preview dimensions
    const previewWidth = await hoverPreview.getAttribute('width');
    const previewHeight = await hoverPreview.getAttribute('height');
    
    console.log(`Preview image display dimensions: ${previewWidth}x${previewHeight}`);
    
    expect(previewWidth).toBe('96');
    expect(previewHeight).toBe('96');
    
    // Check that the preview is 200% larger than the 48x48 thumbnail
    expect(Number(previewWidth)).toBe(48 * 2);
    expect(Number(previewHeight)).toBe(48 * 2);
    
    console.log('✅ Hover preview is correctly 200% larger than thumbnail');
    
    // Test the natural dimensions
    const naturalWidth = await hoverPreview.evaluate((img: HTMLImageElement) => img.naturalWidth);
    const naturalHeight = await hoverPreview.evaluate((img: HTMLImageElement) => img.naturalHeight);
    
    console.log(`Preview natural dimensions: ${naturalWidth}x${naturalHeight}`);
    
    if (naturalWidth > 0 && naturalHeight > 0) {
      console.log('✅ Preview image loaded successfully');
    } else {
      console.log('❌ Preview image failed to load');
    }
    
    // Take screenshot with hover preview
    await page.screenshot({ path: 'final-ocr-testing-hover.png' });
    
    // Move mouse away to test hover out
    await page.mouse.move(0, 0);
    await page.waitForTimeout(1000);
    
    // Verify hover preview is hidden
    await expect(hoverPreview).toBeHidden();
    console.log('✅ Hover preview correctly hidden after hover out');
  });
  
  test('should verify all hover previews are 200% larger', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Count all preview images with correct dimensions
    const previewImages = page.locator('img[width="96"]');
    const previewCount = await previewImages.count();
    
    console.log(`Found ${previewCount} preview images with 96x96 dimensions`);
    
    // Verify all have correct dimensions
    for (let i = 0; i < Math.min(previewCount, 5); i++) {
      const preview = previewImages.nth(i);
      const width = await preview.getAttribute('width');
      const height = await preview.getAttribute('height');
      
      console.log(`Preview ${i}: ${width}x${height}`);
      
      expect(width).toBe('96');
      expect(height).toBe('96');
    }
    
    console.log('✅ All hover previews are correctly sized at 200% of thumbnail size');
    
    // Test hover functionality on multiple images
    const thumbnails = page.locator('img[width="48"]');
    const thumbnailCount = await thumbnails.count();
    
    console.log(`Found ${thumbnailCount} thumbnail images`);
    
    // Test first 3 thumbnails
    for (let i = 0; i < Math.min(thumbnailCount, 3); i++) {
      const thumbnail = thumbnails.nth(i);
      const groupContainer = thumbnail.locator('..').locator('..');
      
      console.log(`Testing hover on thumbnail ${i}...`);
      
      // Hover over the container
      await groupContainer.hover();
      await page.waitForTimeout(1000);
      
      // Check if a hover preview is visible
      const visiblePreviews = page.locator('img[width="96"]:visible');
      const visibleCount = await visiblePreviews.count();
      
      console.log(`Visible previews during hover ${i}: ${visibleCount}`);
      
      if (visibleCount > 0) {
        console.log(`✅ Hover ${i} shows preview correctly`);
      } else {
        console.log(`❌ Hover ${i} shows no preview`);
      }
      
      // Move mouse away
      await page.mouse.move(0, 0);
      await page.waitForTimeout(500);
    }
  });
  
  test('should verify hover preview styling and positioning', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Find first thumbnail and hover
    const firstThumbnail = page.locator('img[width="48"]').first();
    const groupContainer = firstThumbnail.locator('..').locator('..');
    
    await groupContainer.hover();
    await page.waitForTimeout(1000);
    
    // Check the hover preview styling
    const hoverPreview = page.locator('img[width="96"]').first();
    await expect(hoverPreview).toBeVisible();
    
    // Check the preview container styling
    const previewContainer = hoverPreview.locator('..');
    const containerClasses = await previewContainer.getAttribute('class');
    
    console.log('Preview container classes:', containerClasses);
    
    // Should have proper styling classes
    expect(containerClasses).toContain('bg-background');
    expect(containerClasses).toContain('border');
    expect(containerClasses).toContain('rounded-lg');
    expect(containerClasses).toContain('shadow-lg');
    
    // Check image styling
    const imageClasses = await hoverPreview.getAttribute('class');
    console.log('Preview image classes:', imageClasses);
    
    expect(imageClasses).toContain('rounded');
    expect(imageClasses).toContain('object-cover');
    
    // Check positioning
    const parentDiv = previewContainer.locator('..');
    const positionClasses = await parentDiv.getAttribute('class');
    console.log('Preview position classes:', positionClasses);
    
    expect(positionClasses).toContain('absolute');
    expect(positionClasses).toContain('z-50');
    expect(positionClasses).toContain('group-hover:block');
    
    console.log('✅ All hover preview styling and positioning verified');
    
    // Take final screenshot
    await page.screenshot({ path: 'final-hover-preview-styling.png' });
  });
});