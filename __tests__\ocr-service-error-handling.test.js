// Comprehensive tests for OCR service error handling and API fallback logic

// Mock dependencies before importing
jest.mock('openai', () => {
  return jest.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: jest.fn()
      }
    }
  }));
});

jest.mock('sharp', () => {
  const mockSharp = jest.fn(() => ({
    metadata: jest.fn().mockResolvedValue({ width: 1024, height: 768, format: 'jpeg' }),
    resize: jest.fn().mockReturnThis(),
    jpeg: jest.fn().mockReturnThis(),
    normalize: jest.fn().mockReturnThis(),
    sharpen: jest.fn().mockReturnThis(),
    gamma: jest.fn().mockReturnThis(),
    toBuffer: jest.fn().mockResolvedValue(Buffer.from('mock-image-data'))
  }));
  
  // Add the static method for creating test images
  mockSharp.mockImplementation((options) => {
    if (options && options.create) {
      return {
        jpeg: jest.fn().mockReturnThis(),
        toBuffer: jest.fn().mockResolvedValue(Buffer.from('test-image'))
      };
    }
    return mockSharp();
  });
  
  return mockSharp;
});

jest.mock('../backend/services/cost-tracker', () => {
  return jest.fn().mockImplementation(() => ({
    initialize: jest.fn().mockResolvedValue(),
    checkLimits: jest.fn().mockReturnValue({ allowed: true }),
    checkRateLimit: jest.fn().mockReturnValue({ allowed: true }),
    data: {
      transactions: [],
      statistics: { totalSpent: 0 }
    },
    getStatistics: jest.fn().mockReturnValue({})
  }));
});

jest.mock('../backend/services/file-manager');

const OCRService = require('../backend/services/ocr-service');
const OpenAI = require('openai');
const sharp = require('sharp');

// Mock console methods to reduce test output noise
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

describe('OCRService Error Handling and Fallback Logic', () => {
  let ocrService;
  let mockOpenAIClient;
  let mockCostTracker;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock console methods
    console.log = jest.fn();
    console.error = jest.fn();
    
    // Mock OpenAI client
    mockOpenAIClient = {
      chat: {
        completions: {
          create: jest.fn()
        }
      }
    };
    
    // Mock OpenAI constructor to return our mock client
    OpenAI.mockImplementation(() => mockOpenAIClient);
    
    // Create new instance
    ocrService = new OCRService();
    
    // Mock cost tracker
    mockCostTracker = {
      initialize: jest.fn().mockResolvedValue(),
      checkLimits: jest.fn().mockReturnValue({ allowed: true }),
      checkRateLimit: jest.fn().mockReturnValue({ allowed: true }),
      data: {
        transactions: [],
        statistics: { totalSpent: 0 }
      }
    };
    
    ocrService.costTracker = mockCostTracker;
  });

  afterEach(() => {
    // Restore console methods
    console.log = originalConsoleLog;
    console.error = originalConsoleError;
  });

  describe('API Error Handling', () => {
    beforeEach(() => {
      // Configure a test provider
      ocrService.configureProvider('openrouter', {
        apiKey: 'test-api-key'
      });
    });

    test('should handle network timeout errors', async () => {
      const timeoutError = new Error('Request timeout');
      timeoutError.code = 'ECONNABORTED';
      
      mockOpenAIClient.chat.completions.create.mockRejectedValue(timeoutError);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Request timeout');
      expect(result.retryCount).toBe(3); // Should exhaust all retries
    });

    test('should handle API rate limit errors', async () => {
      const rateLimitError = new Error('Rate limit exceeded');
      rateLimitError.status = 429;
      
      mockOpenAIClient.chat.completions.create.mockRejectedValue(rateLimitError);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Rate limit exceeded');
    });

    test('should handle invalid API key errors', async () => {
      const authError = new Error('Invalid API key');
      authError.status = 401;
      
      mockOpenAIClient.chat.completions.create.mockRejectedValue(authError);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid API key');
    });

    test('should handle JSON parsing errors in response', async () => {
      const mockResponse = {
        choices: [{ message: { content: 'Invalid JSON response {incomplete}' } }],
        usage: { prompt_tokens: 100, completion_tokens: 50, total_tokens: 150 }
      };
      
      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockResponse);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(true); // Should still succeed with fallback parsing
      expect(result.result.parseError).toBeDefined();
    });

    test('should handle server errors (5xx)', async () => {
      const serverError = new Error('Internal server error');
      serverError.status = 500;
      
      mockOpenAIClient.chat.completions.create.mockRejectedValue(serverError);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Internal server error');
    });
  });

  describe('Fallback Logic', () => {
    beforeEach(() => {
      // Configure multiple providers for fallback testing
      ocrService.configureProvider('openrouter', {
        apiKey: 'test-openrouter-key'
      });
    });

    test('should fallback to next available model when primary fails', async () => {
      // First model fails
      const primaryError = new Error('Primary model failed');
      mockOpenAIClient.chat.completions.create
        .mockRejectedValueOnce(primaryError)
        .mockResolvedValueOnce({
          choices: [{ message: { content: JSON.stringify({
            firstName: 'John',
            lastName: 'Doe',
            licenseNumber: '12345',
            confidence: 0.9
          }) } }],
          usage: { prompt_tokens: 100, completion_tokens: 50, total_tokens: 150 }
        });

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(true);
      expect(result.note).toContain('fallback');
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledTimes(2);
    });

    test('should not use fallback when user specifies a model', async () => {
      const modelError = new Error('Specified model failed');
      mockOpenAIClient.chat.completions.create.mockRejectedValue(modelError);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Specified model failed');
      expect(result.note).toBeUndefined(); // No fallback note
    });

    test('should fail gracefully when all fallback models fail', async () => {
      const error = new Error('All models failed');
      mockOpenAIClient.chat.completions.create.mockRejectedValue(error);

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(false);
      expect(result.error).toContain('All models failed');
      expect(result.retryCount).toBe(3);
    });
  });

  describe('Retry Mechanism', () => {
    beforeEach(() => {
      ocrService.configureProvider('openrouter', {
        apiKey: 'test-api-key'
      });
      
      // Mock sleep to speed up tests
      jest.spyOn(ocrService, 'sleep').mockResolvedValue();
    });

    test('should retry on transient errors', async () => {
      const transientError = new Error('Temporary failure');
      transientError.code = 'ECONNRESET';
      
      mockOpenAIClient.chat.completions.create
        .mockRejectedValueOnce(transientError)
        .mockRejectedValueOnce(transientError)
        .mockResolvedValueOnce({
          choices: [{ message: { content: JSON.stringify({
            firstName: 'Success',
            lastName: 'After Retry',
            confidence: 0.95
          }) } }],
          usage: { prompt_tokens: 100, completion_tokens: 50, total_tokens: 150 }
        });

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(true);
      expect(result.retryCount).toBe(2); // Third attempt succeeded
      expect(ocrService.sleep).toHaveBeenCalledTimes(2); // Called between retries
    });

    test('should increase delay between retries', async () => {
      const error = new Error('Keep failing');
      mockOpenAIClient.chat.completions.create.mockRejectedValue(error);

      await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      // Check that sleep was called with increasing delays
      expect(ocrService.sleep).toHaveBeenNthCalledWith(1, 1000); // First retry: 1s
      expect(ocrService.sleep).toHaveBeenNthCalledWith(2, 2000); // Second retry: 2s
    });
  });

  describe('Rate Limiting', () => {
    test('should block requests when rate limited', async () => {
      // Create a model with rate limiting enabled
      const model = ocrService.findModel('google/gemini-flash-1.5');
      if (model) {
        model.rateLimit = { requests: 10, period: 3600 };
      }
      
      // Mock isRateLimited to return true
      jest.spyOn(ocrService, 'isRateLimited').mockReturnValue(true);

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(false);
      expect(result.error).toContain('Rate limit exceeded');
      expect(mockOpenAIClient.chat.completions.create).not.toHaveBeenCalled();
    });

    test('should allow requests when under rate limit', async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: JSON.stringify({
          firstName: 'Rate',
          lastName: 'Limited',
          confidence: 0.8
        }) } }],
        usage: { prompt_tokens: 100, completion_tokens: 50, total_tokens: 150 }
      });

      ocrService.configureProvider('openrouter', {
        apiKey: 'test-api-key'
      });

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(true);
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalled();
    });
  });

  describe('Cost Tracking', () => {
    test('should block requests when cost limit exceeded', async () => {
      // Mock isCostLimitExceeded to return true
      jest.spyOn(ocrService, 'isCostLimitExceeded').mockReturnValue(true);

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(false);
      expect(result.error).toContain('Monthly cost limit exceeded');
    });

    test('should update cost tracking on successful requests', async () => {
      mockOpenAIClient.chat.completions.create.mockResolvedValue({
        choices: [{ message: { content: JSON.stringify({
          firstName: 'Cost',
          lastName: 'Tracked',
          confidence: 0.9
        }) } }],
        usage: { prompt_tokens: 100, completion_tokens: 50, total_tokens: 150 }
      });

      ocrService.configureProvider('openrouter', {
        apiKey: 'test-api-key'
      });

      const updateCostTrackingSpy = jest.spyOn(ocrService, 'updateCostTracking');

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(true);
      expect(updateCostTrackingSpy).toHaveBeenCalledWith(
        expect.any(String), // modelId
        expect.any(Number), // cost
        true // success
      );
    });
  });

  describe('Model Availability', () => {
    test('should return error when model not found', async () => {
      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'nonexistent-model'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('not available');
      expect(result.error).toContain('Available models:');
    });

    test('should return error when provider not configured', async () => {
      // Don't configure any providers
      const provider = ocrService.providers.get('openrouter');
      provider.isConfigured = false;

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('requires');
      expect(result.error).toContain('API configuration');
    });

    test('should handle disabled models', async () => {
      // Disable all models
      const provider = ocrService.providers.get('openrouter');
      provider.models.forEach(model => {
        model.isEnabled = false;
      });
      const openaiProvider = ocrService.providers.get('openai');
      openaiProvider.models.forEach(model => {
        model.isEnabled = false;
      });

      expect(() => {
        ocrService.selectOptimalModel();
      }).toThrow('No available models configured');
    });
  });

  describe('Configuration Validation', () => {
    test('should validate provider configuration', () => {
      expect(() => {
        ocrService.configureProvider('nonexistent', { apiKey: 'test' });
      }).toThrow('Provider nonexistent not found');
    });

    test('should handle missing API keys gracefully', async () => {
      // Clear environment variables
      delete process.env.OPENROUTER_API_KEY;
      delete process.env.OPENAI_API_KEY;
      
      // Create fresh service without configured providers
      const freshService = new OCRService();
      freshService.costTracker = mockCostTracker;
      
      // Don't configure providers
      freshService.providers.get('openrouter').isConfigured = false;
      freshService.providers.get('openai').isConfigured = false;
      
      const result = await freshService.processImage(Buffer.from('test-image'));
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('not configured');
    });
  });

  describe('Response Parsing Edge Cases', () => {
    beforeEach(() => {
      ocrService.configureProvider('openrouter', {
        apiKey: 'test-api-key'
      });
    });

    test('should handle empty response', async () => {
      const mockResponse = {
        choices: [{ message: { content: '' } }],
        usage: { prompt_tokens: 100, completion_tokens: 0, total_tokens: 100 }
      };
      
      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockResponse);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(true);
      expect(result.result.parseError).toBeDefined();
    });

    test('should handle response with extra text around JSON', async () => {
      const mockResponse = {
        choices: [{ message: { content: 'Here is the data: {"firstName": "John", "lastName": "Doe"} Hope this helps!' } }],
        usage: { prompt_tokens: 100, completion_tokens: 50, total_tokens: 150 }
      };
      
      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockResponse);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(true);
      expect(result.result.firstName).toBe('John');
      expect(result.result.lastName).toBe('Doe');
    });
  });
});