/**
 * Server-side Port Configuration Utility
 *
 * This utility provides dynamic port resolution for server-side code,
 * ensuring they always connect to the correct backend port as determined
 * by the unified port synchronization system.
 */

import fs from "fs";
import path from "path";

interface PortConfig {
  ports: {
    frontend: number;
    backend: number;
    ngrok: number;
  };
  environment: {
    FRONTEND_PORT: string;
    BACKEND_PORT: string;
    NGROK_PORT: string;
    PORT: string;
  };
  timestamp: string;
  processInfo?: any;
}

/**
 * Get the current backend port from the SINGLE SOURCE OF TRUTH
 * @returns {number} Backend port number
 */
export function getBackendPort(): number {
  try {
    // Import from the scripts/port-config.js (SINGLE SOURCE OF TRUTH)
    const {
      getBackendPort: getRootBackendPort,
    } = require("../../scripts/port-config.js");
    return getRootBackendPort();
  } catch (error) {
    console.warn(
      "Failed to load port from scripts config, using fallback:",
      error
    );
    return 8003; // Updated fallback to match current configuration
  }
}

/**
 * Get the current frontend port from the SINGLE SOURCE OF TRUTH
 * @returns {number} Frontend port number
 */
export function getFrontendPort(): number {
  try {
    // Import from the scripts/port-config.js (SINGLE SOURCE OF TRUTH)
    const {
      getFrontendPort: getRootFrontendPort,
    } = require("../../scripts/port-config.js");
    return getRootFrontendPort();
  } catch (error) {
    console.warn(
      "Failed to load port from scripts config, using fallback:",
      error
    );
    return 8030; // Updated fallback to match current configuration
  }
}

/**
 * Get the complete backend URL for API calls
 * @returns {string} Complete backend URL
 */
export function getBackendUrl(): string {
  try {
    // Import from the scripts/port-config.js (SINGLE SOURCE OF TRUTH)
    const {
      getBackendUrl: getRootBackendUrl,
    } = require("../../scripts/port-config.js");
    return getRootBackendUrl();
  } catch (error) {
    console.warn(
      "Failed to load backend URL from scripts config, using fallback:",
      error
    );
    const port = getBackendPort();
    return `http://127.0.0.1:${port}`;
  }
}

/**
 * Get the complete port configuration
 * @returns {PortConfig | null} Complete port configuration or null if not available
 */
export function getPortConfig(): PortConfig | null {
  try {
    const configPath = path.join(process.cwd(), "data", "port-config.json");

    if (fs.existsSync(configPath)) {
      const config: PortConfig = JSON.parse(
        fs.readFileSync(configPath, "utf8")
      );

      // Check if configuration is not too old (5 minutes)
      const configAge = Date.now() - new Date(config.timestamp).getTime();
      if (configAge < 5 * 60 * 1000) {
        return config;
      } else {
        console.warn("Port configuration is stale");
      }
    }
  } catch (error) {
    console.warn("Failed to load port configuration:", error);
  }

  return null;
}

/**
 * Validate that the current port configuration is still valid
 * @returns {Promise<boolean>} True if configuration is valid
 */
export async function validatePortConfig(): Promise<boolean> {
  try {
    const config = getPortConfig();
    if (!config) return false;

    // Try to connect to the backend to verify it's actually running
    const backendUrl = getBackendUrl();
    const response = await fetch(`${backendUrl}/api/health`, {
      method: "GET",
      signal: AbortSignal.timeout(3000),
    });

    return response.ok;
  } catch (error) {
    console.warn("Port configuration validation failed:", error);
    return false;
  }
}

/**
 * Log port configuration for debugging
 */
export function logPortConfig(): void {
  console.log("🔍 Current Port Configuration:");
  console.log(`  Frontend: ${getFrontendPort()}`);
  console.log(`  Backend: ${getBackendPort()}`);
  console.log(`  Backend URL: ${getBackendUrl()}`);

  const config = getPortConfig();
  if (config) {
    console.log(
      `  Configuration age: ${Math.round(
        (Date.now() - new Date(config.timestamp).getTime()) / 1000
      )}s`
    );
  } else {
    console.log("  No dynamic configuration found, using defaults");
  }
}
