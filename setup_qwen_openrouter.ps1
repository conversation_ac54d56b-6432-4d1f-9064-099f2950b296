#!/usr/bin/env pwsh
# 🚀 Qwen Code + OpenRouter Setup Script
# Configures Qwen CLI to use OpenRouter with Qwen3-Coder models

Write-Host "🚀 Qwen Code + OpenRouter Configuration" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan

# Change to project directory
Set-Location "C:\claude\dl-organizer"
Write-Host "📁 Working in: $(Get-Location)" -ForegroundColor Green

# Check if .env exists and has OpenRouter key
Write-Host "`n🔑 Checking OpenRouter API Key..." -ForegroundColor Yellow

# Function to prompt for API key
function Get-OpenRouterKey {
    Write-Host "`n🔐 OpenRouter API Key Setup" -ForegroundColor Magenta
    Write-Host "You need an OpenRouter API key to use Qwen3-Coder with tool calling support." -ForegroundColor White
    Write-Host "1. Go to: https://openrouter.ai/" -ForegroundColor Cyan
    Write-Host "2. Sign up and get your API key" -ForegroundColor Cyan
    Write-Host "3. Make sure you have credits for the paid models" -ForegroundColor Yellow
    Write-Host ""
    
    $key = Read-Host "Enter your OpenRouter API key (sk-or-v1-...)"
    if ($key -and $key.StartsWith("sk-or-v1-")) {
        return $key
    } else {
        Write-Host "❌ Invalid key format. Should start with 'sk-or-v1-'" -ForegroundColor Red
        return $null
    }
}

# Check for existing key in environment
$apiKey = $env:OPENAI_API_KEY
if (-not $apiKey -or -not $apiKey.StartsWith("sk-or-v1-")) {
    # Try to read from .env file
    if (Test-Path ".env") {
        $envContent = Get-Content ".env" | Where-Object { $_ -match "OPENAI_API_KEY=" }
        if ($envContent) {
            $apiKey = ($envContent -split "=", 2)[1].Trim()
        }
    }
    
    # If still no valid key, prompt user
    if (-not $apiKey -or $apiKey -eq "sk-or-v1-your-openrouter-api-key-here") {
        $apiKey = Get-OpenRouterKey
        if (-not $apiKey) {
            Write-Host "❌ No valid API key provided. Exiting." -ForegroundColor Red
            exit 1
        }
        
        # Update .env file
        $envConfig = @"
# 🚀 OPENROUTER + QWEN3-CODER CONFIGURATION
# OpenRouter API Configuration for Qwen Code CLI
OPENAI_API_KEY=$apiKey
OPENAI_BASE_URL=https://openrouter.ai/api/v1

# Qwen3-Coder models available:
# PAID: qwen/qwen3-coder (full tool calling support)
# FREE: qwen/qwen3-coder:free (try this first)
"@
        Set-Content ".env" $envConfig
        Write-Host "✅ API key saved to .env file" -ForegroundColor Green
    }
}

# Set environment variable for current session
$env:OPENAI_API_KEY = $apiKey
$env:OPENAI_BASE_URL = "https://openrouter.ai/api/v1"

Write-Host "✅ OpenRouter configuration ready" -ForegroundColor Green

# Test Qwen installation
Write-Host "`n🔍 Testing Qwen Code installation..." -ForegroundColor Yellow
try {
    $version = & qwen --version 2>$null
    if ($version) {
        Write-Host "✅ Qwen Code installed: v$version" -ForegroundColor Green
    } else {
        Write-Host "❌ Qwen command not found. Trying alternative..." -ForegroundColor Red
        $version = & "C:\Users\<USER>\.mcp-modules\qwen.cmd" --version 2>$null
        if ($version) {
            Write-Host "✅ Qwen Code found: v$version" -ForegroundColor Green
            # Create an alias for easier access
            Set-Alias -Name qwen -Value "C:\Users\<USER>\.mcp-modules\qwen.cmd" -Scope Global
        } else {
            Write-Host "❌ Qwen Code not found. Please install it first." -ForegroundColor Red
            exit 1
        }
    }
} catch {
    Write-Host "❌ Error testing Qwen: $_" -ForegroundColor Red
    exit 1
}

# Test with free model first
Write-Host "`n🧪 Testing Qwen3-Coder (free) with tool calling..." -ForegroundColor Yellow
Write-Host "Model: qwen/qwen3-coder:free" -ForegroundColor Cyan

try {
    $testResult = & qwen --model "qwen/qwen3-coder:free" --prompt "Just say 'Hello from Qwen3-Coder!' and nothing else. This is a connection test." 2>&1
    
    if ($testResult -match "Hello from Qwen3-Coder") {
        Write-Host "✅ FREE model working! Tool calling should be supported." -ForegroundColor Green
        $workingModel = "qwen/qwen3-coder:free"
    } elseif ($testResult -match "404.*tool use") {
        Write-Host "❌ Free model doesn't support tool calling. Trying paid model..." -ForegroundColor Yellow
        
        # Test paid model
        Write-Host "🧪 Testing Qwen3-Coder (paid)..." -ForegroundColor Yellow
        $testResult = & qwen --model "qwen/qwen3-coder" --prompt "Just say 'Hello from Qwen3-Coder!' and nothing else. This is a connection test." 2>&1
        
        if ($testResult -match "Hello from Qwen3-Coder") {
            Write-Host "✅ PAID model working with tool calling support!" -ForegroundColor Green
            $workingModel = "qwen/qwen3-coder"
        } else {
            Write-Host "❌ Both models failed. Error: $testResult" -ForegroundColor Red
            $workingModel = $null
        }
    } else {
        Write-Host "⚠️  Unexpected response: $testResult" -ForegroundColor Yellow
        $workingModel = "qwen/qwen3-coder:free"
    }
} catch {
    Write-Host "❌ Test failed: $_" -ForegroundColor Red
    $workingModel = $null
}

# Create qwen init command like claude code
Write-Host "`n📝 Creating QWEN.md initialization..." -ForegroundColor Yellow
if (Test-Path "QWEN.md") {
    Write-Host "✅ QWEN.md already exists" -ForegroundColor Green
} else {
    Write-Host "❌ QWEN.md not found. Creating it..." -ForegroundColor Yellow
    # The QWEN.md file was already created above
}

# Summary and next steps
Write-Host "`n🎉 SETUP COMPLETE!" -ForegroundColor Green
Write-Host "==================" -ForegroundColor Green

if ($workingModel) {
    Write-Host "✅ Working model: $workingModel" -ForegroundColor Cyan
    Write-Host "✅ OpenRouter API configured" -ForegroundColor Green
    Write-Host "✅ QWEN.md configuration file ready" -ForegroundColor Green
    
    Write-Host "`n🚀 Ready to use Qwen Code CLI!" -ForegroundColor Magenta
    Write-Host "Try these commands:" -ForegroundColor White
    Write-Host "  qwen --model $workingModel" -ForegroundColor Cyan
    Write-Host "  qwen --model $workingModel --prompt 'Help me with coding'" -ForegroundColor Cyan
    
    Write-Host "`n💡 The QWEN.md file configures Qwen to be a specialized coding assistant" -ForegroundColor Yellow
    Write-Host "   similar to how Claude Code works with its configuration." -ForegroundColor White
    
} else {
    Write-Host "❌ Setup incomplete. Check your OpenRouter credits and API key." -ForegroundColor Red
    Write-Host "   Make sure you have credits for Qwen3-Coder models." -ForegroundColor Yellow
}

Write-Host "`n🔧 Configuration files created:" -ForegroundColor Cyan
Write-Host "  📄 .env - OpenRouter API configuration" -ForegroundColor White  
Write-Host "  📄 QWEN.md - Agent behavior configuration" -ForegroundColor White

Write-Host "`nQwen Code CLI is now configured for agentic coding! 🤖" -ForegroundColor Green
