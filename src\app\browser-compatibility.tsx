'use client'

import { useEffect } from 'react'

/**
 * Enhanced Browser Compatibility Component
 * Handles crypto wallet extensions, browser extension conflicts, and SSR safety
 */

export function BrowserCompatibility() {
  useEffect(() => {
    // Basic SSR safety check
    if (typeof window === 'undefined') return

    // Development logging
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 Browser compatibility initialized')
    }

    // Enhanced extension conflict handling
    try {
      // Strategy 1: Advanced ethereum property conflict resolution
      const handleEthereumProperty = () => {
        try {
          // Check if ethereum property exists and has getter-only restriction
          const ethereumDescriptor = Object.getOwnPropertyDescriptor(window, 'ethereum')
          
          if (ethereumDescriptor) {
            // If it's a getter-only property (common with MetaMask conflicts)
            if (ethereumDescriptor.get && !ethereumDescriptor.set) {
              const currentValue = (window as any).ethereum
              
              // Redefine with proper setter to prevent "Cannot set property" errors
              try {
                Object.defineProperty(window, 'ethereum', {
                  get: () => currentValue,
                  set: (value) => {
                    // Create a new property descriptor that allows writing
                    try {
                      Object.defineProperty(window, 'ethereum', {
                        value: value,
                        writable: true,
                        configurable: true,
                        enumerable: true
                      })
                    } catch (e) {
                      // If redefinition fails, silently ignore - this prevents console spam
                      if (process.env.NODE_ENV === 'development') {
                        console.debug('🔧 Ethereum property redefinition blocked (expected)', e)
                      }
                    }
                  },
                  configurable: true,
                  enumerable: true
                })
                
                if (process.env.NODE_ENV === 'development') {
                  console.log('✅ Ethereum property conflict resolved')
                }
              } catch (e) {
                // Property redefinition failed - this is expected in some cases
                if (process.env.NODE_ENV === 'development') {
                  console.debug('🔧 Ethereum property already protected by extension')
                }
              }
            }
          }
        } catch (e) {
          // Silently handle failures - property access might be restricted
          if (process.env.NODE_ENV === 'development') {
            console.debug('🔧 Ethereum property access handled')
          }
        }
      }

      // Strategy 2: Enhanced console error filtering for cleaner development experience
      const originalError = console.error
      const originalWarn = console.warn
      
      // Filter out extension-related console spam
      console.error = (...args) => {
        const message = args[0]?.toString() || ''
        
        const extensionErrorPatterns = [
          'A listener indicated an asynchronous response',
          'message channel closed before a response',
          'Cannot set property ethereum',
          'ethereum of #<Window> which has only a getter',
          'Nano Defender',
          'Unstoppable Domains',
          'USO-aw-dx',
          'contentProxy.bundle.js',
          'pageProvider.js',
          'inpage.js',
          'webextension-polyfill',
          'chrome-extension://',
          'moz-extension://',
          'The message port closed before a response was received',
          'Extension context invalidated'
        ]
        
        const isExtensionError = extensionErrorPatterns.some(pattern => 
          message.toLowerCase().includes(pattern.toLowerCase())
        )
        
        if (!isExtensionError) {
          originalError.apply(console, args)
        } else if (process.env.NODE_ENV === 'development') {
          // Log filtered extension errors as debug info
          console.debug('🔧 Extension error filtered:', message.substring(0, 100) + (message.length > 100 ? '...' : ''))
        }
      }

      // Also filter warnings to reduce noise
      console.warn = (...args) => {
        const message = args[0]?.toString() || ''
        
        const extensionWarningPatterns = [
          'webextension-polyfill',
          'chrome-extension',
          'moz-extension',
          'Extension'
        ]
        
        const isExtensionWarning = extensionWarningPatterns.some(pattern => 
          message.toLowerCase().includes(pattern.toLowerCase())
        )
        
        if (!isExtensionWarning) {
          originalWarn.apply(console, args)
        }
      }

      // Strategy 3: Handle form submission conflicts from extensions
      document.addEventListener('submit', (e) => {
        const form = e.target as HTMLFormElement
        if (form && !form.action && !e.defaultPrevented) {
          // Prevent default only if form has no action and event isn't already handled
          e.preventDefault()
        }
      }, true)

      // Strategy 4: Handle extension message channel errors globally
      window.addEventListener('error', (e) => {
        const message = e.message || ''
        const filename = e.filename || ''
        
        const extensionErrorPatterns = [
          'message channel closed',
          'listener indicated an asynchronous response',
          'ethereum',
          'chrome-extension',
          'moz-extension',
          'webextension-polyfill'
        ]
        
        const isExtensionError = extensionErrorPatterns.some(pattern => 
          message.toLowerCase().includes(pattern.toLowerCase()) || 
          filename.toLowerCase().includes(pattern.toLowerCase())
        )
        
        if (isExtensionError) {
          e.preventDefault()
          e.stopPropagation()
          return false
        }
      }, true)

      // Strategy 5: Handle unhandled promise rejections from extensions
      window.addEventListener('unhandledrejection', (e) => {
        const reason = e.reason?.toString() || ''
        
        const extensionErrorPatterns = [
          'message channel closed',
          'listener indicated an asynchronous response',
          'ethereum',
          'chrome-extension',
          'moz-extension',
          'webextension-polyfill',
          'Extension context invalidated'
        ]
        
        const isExtensionError = extensionErrorPatterns.some(pattern => 
          reason.toLowerCase().includes(pattern.toLowerCase())
        )
        
        if (isExtensionError) {
          e.preventDefault()
          return false
        }
      })

      // Strategy 6: Protect against extension-induced property conflicts
      const protectGlobalProperties = () => {
        const problematicProperties = ['ethereum', 'web3', 'wallet']
        
        problematicProperties.forEach(prop => {
          try {
            const descriptor = Object.getOwnPropertyDescriptor(window, prop)
            if (descriptor && descriptor.get && !descriptor.set) {
              const currentValue = (window as any)[prop]
              
              // Create a more permissive property descriptor
              Object.defineProperty(window, prop, {
                get: () => currentValue,
                set: () => {
                  // No-op setter to prevent errors
                  // Extensions can still read the property but won't crash on write attempts
                },
                configurable: true,
                enumerable: true
              })
            }
          } catch (e) {
            // Property protection failed - this is acceptable
            // The goal is to prevent crashes, not to force property modification
          }
        })
      }
      
      // Strategy 7: Handle extension content script injection conflicts
      const handleContentScriptConflicts = () => {
        // Prevent extension scripts from interfering with our React components
        const originalCreateElement = document.createElement
        document.createElement = function(tagName: string, options?: ElementCreationOptions) {
          const element = originalCreateElement.call(this, tagName, options)
          
          // Add protection against extension modification of our elements
          if (tagName.toLowerCase() === 'script') {
            // Prevent extensions from modifying our script elements
            Object.defineProperty(element, 'src', {
              set: function(value) {
                // Only allow our own scripts and trusted CDNs
                if (value.startsWith('/') || 
                    value.startsWith('http://localhost') || 
                    value.startsWith('http://127.0.0.1') ||
                    value.includes('nextjs') ||
                    value.includes('vercel')) {
                  this.setAttribute('src', value)
                }
              },
              get: function() {
                return this.getAttribute('src')
              }
            })
          }
          
          return element
        }
      }
      
      // Execute all protection strategies
      handleEthereumProperty()
      protectGlobalProperties()
      handleContentScriptConflicts()
      
      // Run ethereum property fix with progressive timing to catch late-loading extensions
      setTimeout(handleEthereumProperty, 100)
      setTimeout(handleEthereumProperty, 500)
      setTimeout(handleEthereumProperty, 1000)
      setTimeout(protectGlobalProperties, 1500)
      
      // Strategy 8: Periodic cleanup of extension artifacts
      const cleanupInterval = setInterval(() => {
        try {
          // Clean up any extension-injected global pollution
          const potentialExtensionGlobals = ['__METAMASK_EXTENSION_ID__', '__NANO_DEFENDER__', '__USO_AW_DX__']
          potentialExtensionGlobals.forEach(global => {
            if ((window as any)[global] && typeof (window as any)[global] === 'object') {
              // Don't delete, just neutralize to prevent conflicts
              try {
                (window as any)[global] = null
              } catch (e) {
                // Ignore cleanup failures
              }
            }
          })
          
          // After 30 seconds, we can stop the aggressive cleanup
          if (Date.now() - startTime > 30000) {
            clearInterval(cleanupInterval)
          }
        } catch (e) {
          // Ignore cleanup errors
        }
      }, 2000)

      const startTime = Date.now()
      
    } catch (error) {
      // Silently handle any extension compatibility issues
      if (process.env.NODE_ENV === 'development') {
        console.debug('🔧 Extension compatibility system active')
      }
    }

    // Enhanced localStorage safety wrapper with better error handling
    const safeStorage = {
      getItem: (key: string) => {
        try {
          return localStorage.getItem(key)
        } catch (e) {
          if (process.env.NODE_ENV === 'development') {
            console.debug('🔧 localStorage.getItem failed for key:', key)
          }
          return null
        }
      },
      setItem: (key: string, value: string) => {
        try {
          localStorage.setItem(key, value)
          return true
        } catch (e) {
          if (process.env.NODE_ENV === 'development') {
            console.debug('🔧 localStorage.setItem failed for key:', key)
          }
          return false
        }
      },
      removeItem: (key: string) => {
        try {
          localStorage.removeItem(key)
          return true
        } catch (e) {
          if (process.env.NODE_ENV === 'development') {
            console.debug('🔧 localStorage.removeItem failed for key:', key)
          }
          return false
        }
      }
    }

    // Make safe storage available globally for components that need it
    ;(window as any).safeStorage = safeStorage

    // Final success notification
    if (process.env.NODE_ENV === 'development') {
      setTimeout(() => {
        console.log('✅ Browser compatibility system fully initialized')
      }, 2000)
    }

  }, [])

  return null
}

// Export helper functions for use in other components
export const isBrowser = () => typeof window !== 'undefined'

export const safeLocalStorage = {
  getItem: (key: string): string | null => {
    if (!isBrowser()) return null
    try {
      return localStorage.getItem(key)
    } catch (e) {
      if (process.env.NODE_ENV === 'development') {
        console.debug('🔧 safeLocalStorage.getItem failed for key:', key)
      }
      return null
    }
  },
  setItem: (key: string, value: string): boolean => {
    if (!isBrowser()) return false
    try {
      localStorage.setItem(key, value)
      return true
    } catch (e) {
      if (process.env.NODE_ENV === 'development') {
        console.debug('🔧 safeLocalStorage.setItem failed for key:', key)
      }
      return false
    }
  },
  removeItem: (key: string): boolean => {
    if (!isBrowser()) return false
    try {
      localStorage.removeItem(key)
      return true
    } catch (e) {
      if (process.env.NODE_ENV === 'development') {
        console.debug('🔧 safeLocalStorage.removeItem failed for key:', key)
      }
      return false
    }
  }
}

// Utility function to check if specific extension types are installed
export const detectExtensions = () => {
  if (!isBrowser()) return {}
  
  return {
    hasMetaMask: !!(window as any).ethereum?.isMetaMask,
    hasNanoDefender: !!(window as any).__NANO_DEFENDER__,
    hasUSOAwDx: !!(window as any).__USO_AW_DX__,
    hasUnstoppableDomains: !!document.querySelector('script[src*="unstoppabledomains"]'),
    hasChromeExtensions: !!(window as any).chrome?.runtime,
    hasWebExtensionPolyfill: !!(window as any).browser
  }
}

// Export a cleanup function for manual extension conflict resolution
export const resolveExtensionConflicts = () => {
  if (!isBrowser()) return false
  
  try {
    // Re-run the ethereum property fix
    const ethereumDescriptor = Object.getOwnPropertyDescriptor(window, 'ethereum')
    if (ethereumDescriptor?.get && !ethereumDescriptor.set) {
      const currentValue = (window as any).ethereum
      Object.defineProperty(window, 'ethereum', {
        get: () => currentValue,
        set: () => {},
        configurable: true,
        enumerable: true
      })
    }
    
    return true
  } catch (e) {
    console.debug('🔧 Manual extension conflict resolution completed')
    return false
  }
}