const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');

/**
 * Image Analysis Cache System
 * Saves individual JSON files for each image analysis result
 * Provides fast loading and persistence across application restarts
 */
class ImageAnalysisCache {
  constructor(options = {}) {
    this.cacheDir = options.cacheDir || path.join(__dirname, '../../data/analysis-cache');
    this.maxCacheAge = options.maxCacheAge || 30 * 24 * 60 * 60 * 1000; // 30 days
    
    this.initializeCache();
  }

  /**
   * Initialize cache directory
   */
  async initializeCache() {
    try {
      await fs.mkdir(this.cacheDir, { recursive: true });
      console.log(`✅ Image analysis cache initialized: ${this.cacheDir}`);
    } catch (error) {
      console.error('❌ Error initializing analysis cache:', error);
    }
  }

  /**
   * Generate cache key for an image
   * @param {string} imagePath - Full path to the image
   * @returns {string} - Unique cache key
   */
  generateCacheKey(imagePath) {
    // Use image path + file stats for unique key
    const hash = crypto.createHash('md5');
    hash.update(imagePath);
    return hash.digest('hex');
  }

  /**
   * Get cache file path for an image
   * @param {string} imagePath - Full path to the image
   * @returns {string} - Path to cache file
   */
  getCacheFilePath(imagePath) {
    const cacheKey = this.generateCacheKey(imagePath);
    return path.join(this.cacheDir, `${cacheKey}.json`);
  }

  /**
   * Save analysis result to cache
   * @param {string} imagePath - Full path to the image
   * @param {Object} analysisResult - The analysis result to cache
   * @param {Object} metadata - Additional metadata
   */
  async saveAnalysis(imagePath, analysisResult, metadata = {}) {
    try {
      const cacheFile = this.getCacheFilePath(imagePath);
      const stats = await fs.stat(imagePath);
      
      const cacheData = {
        imagePath: imagePath,
        filename: path.basename(imagePath),
        cacheKey: this.generateCacheKey(imagePath),
        createdAt: new Date().toISOString(),
        lastModified: stats.mtime.toISOString(),
        fileSize: stats.size,
        analysisResult: analysisResult,
        metadata: {
          ...metadata,
          cacheVersion: '1.0',
          analysisType: metadata.analysisType || 'unknown'
        }
      };

      await fs.writeFile(cacheFile, JSON.stringify(cacheData, null, 2));
      console.log(`💾 Analysis cached: ${path.basename(imagePath)} -> ${path.basename(cacheFile)}`);
      
      return cacheData;
    } catch (error) {
      console.error(`❌ Error saving analysis cache for ${imagePath}:`, error);
      throw error;
    }
  }

  /**
   * Load analysis result from cache
   * @param {string} imagePath - Full path to the image
   * @returns {Object|null} - Cached analysis result or null if not found/invalid
   */
  async loadAnalysis(imagePath) {
    try {
      const cacheFile = this.getCacheFilePath(imagePath);
      
      // Check if cache file exists
      try {
        await fs.access(cacheFile);
      } catch {
        return null; // Cache file doesn't exist
      }

      // Load cache data
      const cacheData = JSON.parse(await fs.readFile(cacheFile, 'utf8'));
      
      // Validate cache against current file
      const isValid = await this.isCacheValid(imagePath, cacheData);
      if (!isValid) {
        console.log(`🗑️ Invalid cache for ${path.basename(imagePath)}, removing...`);
        await this.removeAnalysis(imagePath);
        return null;
      }

      console.log(`📋 Analysis loaded from cache: ${path.basename(imagePath)}`);
      return cacheData;
      
    } catch (error) {
      console.error(`❌ Error loading analysis cache for ${imagePath}:`, error);
      // If cache is corrupted, remove it
      await this.removeAnalysis(imagePath);
      return null;
    }
  }

  /**
   * Check if cached analysis is still valid
   * @param {string} imagePath - Full path to the image
   * @param {Object} cacheData - Cached data to validate
   * @returns {boolean} - True if cache is valid
   */
  async isCacheValid(imagePath, cacheData) {
    try {
      // Check if original file still exists
      const stats = await fs.stat(imagePath);
      
      // Check if file has been modified since cache creation
      if (stats.mtime.toISOString() !== cacheData.lastModified) {
        return false;
      }
      
      // Check if file size has changed
      if (stats.size !== cacheData.fileSize) {
        return false;
      }
      
      // Check cache age
      const cacheAge = Date.now() - new Date(cacheData.createdAt).getTime();
      if (cacheAge > this.maxCacheAge) {
        return false;
      }
      
      return true;
    } catch (error) {
      // If file doesn't exist or any other error, cache is invalid
      return false;
    }
  }

  /**
   * Remove analysis from cache
   * @param {string} imagePath - Full path to the image
   */
  async removeAnalysis(imagePath) {
    try {
      const cacheFile = this.getCacheFilePath(imagePath);
      await fs.unlink(cacheFile);
      console.log(`🗑️ Analysis cache removed: ${path.basename(imagePath)}`);
    } catch (error) {
      // Ignore errors if file doesn't exist
      if (error.code !== 'ENOENT') {
        console.error(`❌ Error removing analysis cache for ${imagePath}:`, error);
      }
    }
  }

  /**
   * Load all cached analyses for a directory
   * @param {string} directoryPath - Path to directory
   * @returns {Object} - Map of imagePath -> cacheData
   */
  async loadDirectoryAnalyses(directoryPath) {
    const analyses = {};
    
    try {
      // Get all cache files
      const cacheFiles = await fs.readdir(this.cacheDir);
      const jsonFiles = cacheFiles.filter(file => file.endsWith('.json'));
      
      for (const jsonFile of jsonFiles) {
        try {
          const cacheFilePath = path.join(this.cacheDir, jsonFile);
          const cacheData = JSON.parse(await fs.readFile(cacheFilePath, 'utf8'));
          
          // Check if this cache belongs to the directory
          if (cacheData.imagePath && cacheData.imagePath.startsWith(directoryPath)) {
            // Validate cache
            const isValid = await this.isCacheValid(cacheData.imagePath, cacheData);
            if (isValid) {
              analyses[cacheData.imagePath] = cacheData;
            } else {
              // Remove invalid cache
              await fs.unlink(cacheFilePath);
            }
          }
        } catch (error) {
          console.error(`❌ Error loading cache file ${jsonFile}:`, error);
          // Remove corrupted cache file
          try {
            await fs.unlink(path.join(this.cacheDir, jsonFile));
          } catch (unlinkError) {
            // Ignore unlink errors
          }
        }
      }
      
      console.log(`📂 Loaded ${Object.keys(analyses).length} cached analyses for ${directoryPath}`);
      return analyses;
      
    } catch (error) {
      console.error(`❌ Error loading directory analyses for ${directoryPath}:`, error);
      return {};
    }
  }

  /**
   * Clean up old cache files
   * @param {number} maxAge - Maximum age in milliseconds (optional)
   */
  async cleanupOldCache(maxAge = null) {
    const cutoffAge = maxAge || this.maxCacheAge;
    let cleanedCount = 0;
    
    try {
      const cacheFiles = await fs.readdir(this.cacheDir);
      const jsonFiles = cacheFiles.filter(file => file.endsWith('.json'));
      
      for (const jsonFile of jsonFiles) {
        try {
          const cacheFilePath = path.join(this.cacheDir, jsonFile);
          const cacheData = JSON.parse(await fs.readFile(cacheFilePath, 'utf8'));
          
          const cacheAge = Date.now() - new Date(cacheData.createdAt).getTime();
          if (cacheAge > cutoffAge) {
            await fs.unlink(cacheFilePath);
            cleanedCount++;
          }
        } catch (error) {
          // Remove corrupted files
          try {
            await fs.unlink(path.join(this.cacheDir, jsonFile));
            cleanedCount++;
          } catch (unlinkError) {
            // Ignore unlink errors
          }
        }
      }
      
      if (cleanedCount > 0) {
        console.log(`🧹 Cleaned up ${cleanedCount} old cache files`);
      }
      
    } catch (error) {
      console.error('❌ Error during cache cleanup:', error);
    }
  }

  /**
   * Get cache statistics
   * @returns {Object} - Cache statistics
   */
  async getCacheStats() {
    try {
      const cacheFiles = await fs.readdir(this.cacheDir);
      const jsonFiles = cacheFiles.filter(file => file.endsWith('.json'));
      
      let totalSize = 0;
      let validCount = 0;
      let invalidCount = 0;
      
      for (const jsonFile of jsonFiles) {
        try {
          const cacheFilePath = path.join(this.cacheDir, jsonFile);
          const stats = await fs.stat(cacheFilePath);
          totalSize += stats.size;
          
          const cacheData = JSON.parse(await fs.readFile(cacheFilePath, 'utf8'));
          const isValid = await this.isCacheValid(cacheData.imagePath, cacheData);
          
          if (isValid) {
            validCount++;
          } else {
            invalidCount++;
          }
        } catch (error) {
          invalidCount++;
        }
      }
      
      return {
        totalFiles: jsonFiles.length,
        validCount,
        invalidCount,
        totalSizeMB: (totalSize / (1024 * 1024)).toFixed(2),
        cacheDirectory: this.cacheDir
      };
      
    } catch (error) {
      console.error('❌ Error getting cache stats:', error);
      return {
        totalFiles: 0,
        validCount: 0,
        invalidCount: 0,
        totalSizeMB: '0.00',
        cacheDirectory: this.cacheDir
      };
    }
  }
}

module.exports = ImageAnalysisCache;