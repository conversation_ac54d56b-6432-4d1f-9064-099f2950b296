@echo off
REM Test GEMINI_MODEL environment variable
cd /d "C:\claude\dl-organizer"

REM Set all environment variables
set GEMINI_MODEL=qwen/qwen3-coder
set OPENAI_API_KEY=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
set OPENAI_BASE_URL=https://openrouter.ai/api/v1

echo ========================================
echo Testing GEMINI_MODEL environment variable
echo ========================================
echo GEMINI_MODEL: %GEMINI_MODEL%
echo OPENAI_API_KEY: %OPENAI_API_KEY:~0,20%...
echo OPENAI_BASE_URL: %OPENAI_BASE_URL%
echo ========================================

qwen --prompt "Just say hello test"

pause
