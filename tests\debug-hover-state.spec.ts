import { test, expect } from '@playwright/test';

// Debug hover state test
test.describe('Debug Hover State', () => {
  test('should debug hover state and visibility', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Find first thumbnail and its group container
    const firstThumbnail = page.locator('img[width="48"]').first();
    const groupContainer = firstThumbnail.locator('..').locator('..');
    
    // Check initial state
    console.log('=== INITIAL STATE ===');
    
    const hasGroupClass = await groupContainer.evaluate((el) => el.classList.contains('group'));
    console.log('Group container has "group" class:', hasGroupClass);
    
    const groupClasses = await groupContainer.getAttribute('class');
    console.log('Group container classes:', groupClasses);
    
    // Find hover preview elements
    const hoverPreview = page.locator('img[width="96"]').first();
    const previewContainer = hoverPreview.locator('..');
    const previewParent = previewContainer.locator('..');
    
    const previewParentClasses = await previewParent.getAttribute('class');
    console.log('Preview parent classes:', previewParentClasses);
    
    const previewContainerClasses = await previewContainer.getAttribute('class');
    console.log('Preview container classes:', previewContainerClasses);
    
    // Check initial visibility
    const initialVisibility = await hoverPreview.isVisible();
    console.log('Initial hover preview visibility:', initialVisibility);
    
    const initialDisplay = await hoverPreview.evaluate((el) => window.getComputedStyle(el).display);
    console.log('Initial display style:', initialDisplay);
    
    // Take screenshot before hover
    await page.screenshot({ path: 'debug-before-hover.png' });
    
    console.log('=== HOVERING ===');
    
    // Hover over the group container
    await groupContainer.hover();
    await page.waitForTimeout(1000);
    
    // Check hover state
    const afterHoverVisibility = await hoverPreview.isVisible();
    console.log('After hover visibility:', afterHoverVisibility);
    
    const afterHoverDisplay = await hoverPreview.evaluate((el) => window.getComputedStyle(el).display);
    console.log('After hover display style:', afterHoverDisplay);
    
    // Check if group is in hover state
    const groupHoverState = await groupContainer.evaluate((el) => {
      return el.matches(':hover');
    });
    console.log('Group container in hover state:', groupHoverState);
    
    // Check if parent has hover classes
    const parentHoverClasses = await previewParent.evaluate((el) => {
      const styles = window.getComputedStyle(el);
      return {
        display: styles.display,
        visibility: styles.visibility,
        opacity: styles.opacity,
        position: styles.position,
        zIndex: styles.zIndex
      };
    });
    console.log('Preview parent computed styles:', parentHoverClasses);
    
    // Take screenshot after hover
    await page.screenshot({ path: 'debug-after-hover.png' });
    
    console.log('=== MANUAL FORCE SHOW ===');
    
    // Try to force show the preview
    await previewParent.evaluate((el) => {
      el.classList.remove('hidden');
      el.classList.add('block');
      el.style.display = 'block';
    });
    
    await page.waitForTimeout(1000);
    
    const forceShowVisibility = await hoverPreview.isVisible();
    console.log('Force show visibility:', forceShowVisibility);
    
    // Take screenshot after force show
    await page.screenshot({ path: 'debug-force-show.png' });
    
    console.log('=== TESTING DIRECT HOVER ON PREVIEW PARENT ===');
    
    // Try hovering directly on the preview parent
    await previewParent.hover();
    await page.waitForTimeout(1000);
    
    const directHoverVisibility = await hoverPreview.isVisible();
    console.log('Direct hover on preview parent visibility:', directHoverVisibility);
    
    // Test CSS selector
    const cssTest = await page.evaluate(() => {
      const groupElements = document.querySelectorAll('.group');
      const previewElements = document.querySelectorAll('.group-hover\\:block');
      
      return {
        groupCount: groupElements.length,
        previewCount: previewElements.length,
        hasGroupHoverCSS: !!document.querySelector('.group:hover .group-hover\\:block')
      };
    });
    
    console.log('CSS selector test:', cssTest);
    
    console.log('=== FINAL TEST ===');
    
    // Final comprehensive test
    await page.evaluate(() => {
      // Add some debugging CSS
      const style = document.createElement('style');
      style.textContent = `
        .group:hover .group-hover\\:block {
          display: block !important;
          visibility: visible !important;
          opacity: 1 !important;
        }
        .debug-hover {
          border: 2px solid red !important;
        }
      `;
      document.head.appendChild(style);
      
      // Add debug class to preview elements
      document.querySelectorAll('.group-hover\\:block').forEach(el => {
        el.classList.add('debug-hover');
      });
    });
    
    await groupContainer.hover();
    await page.waitForTimeout(1000);
    
    const finalVisibility = await hoverPreview.isVisible();
    console.log('Final visibility with CSS override:', finalVisibility);
    
    // Take final screenshot
    await page.screenshot({ path: 'debug-final-test.png' });
  });
});