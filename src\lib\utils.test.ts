import { cn } from './utils'

describe('utils', () => {
  test('cn should merge classnames correctly', () => {
    expect(cn('bg-red-500', 'text-white')).toContain('bg-red-500')
    expect(cn('bg-red-500', 'text-white')).toContain('text-white')
  })

  test('cn should handle conditional classnames', () => {
    expect(cn('base', true && 'active')).toContain('base')
    expect(cn('base', true && 'active')).toContain('active')
    expect(cn('base', false && 'inactive')).not.toContain('inactive')
  })
})