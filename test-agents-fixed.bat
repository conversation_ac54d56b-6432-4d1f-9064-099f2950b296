@echo off
title DL Organizer - Fixed Agent Test
cls

echo.
echo ===============================================
echo DL Organizer Agent System - Fixed Version Test
echo ===============================================
echo.

echo Checking configuration updates...
echo.

if exist ".claude\agents-config.json" (
    echo [OK] agents-config.json exists
) else (
    echo [ERROR] agents-config.json missing
    pause
    exit /b 1
)

if exist ".claude\claude-code-config.json" (
    echo [OK] claude-code-config.json exists
) else (
    echo [ERROR] claude-code-config.json missing
    pause
    exit /b 1
)

echo.
echo Configuration improvements made:
echo - Increased token limit: 5000 → 12000
echo - Reduced timeout: 10min → 3min
echo - Added tool limits and progress checks
echo - Simplified agent instructions
echo - Added error handling and retries
echo.

echo TESTING RECOMMENDATIONS:
echo.
echo 1. Test UI agent (should be much faster):
echo    claude-code "ui-design-specialist: Fix image hover functionality"
echo.
echo 2. Test infrastructure agent:
echo    claude-code "infrastructure-specialist: Check npm run dev status"
echo.
echo 3. Monitor for hanging:
echo    - Should complete within 3 minutes
echo    - Should show progress every 30 seconds
echo    - Will auto-timeout gracefully if stuck
echo.

echo [CHANGES MADE TO FIX HANGING]:
echo - Reduced agent complexity and verbosity
echo - Set strict 3-minute timeouts
echo - Limited tool usage per agent
echo - Added progress monitoring
echo - Improved error handling
echo.

echo Test the agents now - they should be much more responsive!
echo.
pause