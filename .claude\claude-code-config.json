{"agentSystem": {"enabled": true, "autoTrigger": true, "agentMapping": {"infrastructure": "port-manager", "ui": "frontend-guardian", "frontend": "frontend-guardian", "typescript": "frontend-guardian", "backend": "api-specialist", "api": "api-specialist", "database": "api-specialist", "ocr": "ocr-specialist", "ai": "ocr-specialist", "model": "ocr-specialist", "test": "qa-specialist", "testing": "qa-specialist", "jest": "qa-specialist", "playwright": "qa-specialist"}, "triggerKeywords": {"port-manager": ["port", "win32", "launcher", "startup", "npm", "node.js", "bulletproof", "error", "process", "windows", "powershell", "infrastructure"], "frontend-guardian": ["typescript", "component", "frontend", "react", "style", "compile", "tsx", "jsx", "css", "tailwind", "shadcn", "ui", "next"], "api-specialist": ["api", "backend", "database", "routing", "express", "sqlite", "query", "endpoint", "sse", "performance", "server"], "ocr-specialist": ["ocr", "openrouter", "model", "ai", "gpt", "license", "detection", "confidence", "prompt", "openai", "smart-analyzer", "readysearch"], "qa-specialist": ["test", "jest", "playwright", "coverage", "unit", "e2e", "testing", "spec", "quality", "supertest"]}}, "tokenBudget": {"enforceLimit": true, "maxTokensPerTask": 4000, "warningThreshold": 3000, "emergencyStop": true}, "performance": {"timeoutTasks": 30000, "maxConcurrentOperations": 1, "preventHangingTests": true, "quickStart": true, "earlyTermination": true, "streamlinedContext": true, "fastMode": true, "agentTimeout": 25000, "forceExit": true}}