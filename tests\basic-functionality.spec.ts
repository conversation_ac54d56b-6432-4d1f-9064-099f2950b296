import { test, expect } from '@playwright/test';

// Basic functionality tests for DL Organizer
test.describe('DL Organizer Basic Functionality', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('http://localhost:3001');
  });

  test('should load the home page', async ({ page }) => {
    // Wait for the page to load
    await page.waitForLoadState('networkidle');
    
    // Check if the main heading or project creation button is displayed
    const heading = page.getByRole('heading').first();
    const createButton = page.getByRole('button', { name: /new project/i });
    
    // Wait for either the heading or button to be visible
    await expect(heading.or(createButton)).toBeVisible();
  });

  test('should show project creation form', async ({ page }) => {
    // Look for the create project button
    const createButton = page.getByRole('button', { name: /create.*project/i });
    await expect(createButton).toBeVisible();
  });

  test('should have backend API connectivity', async ({ page }) => {
    // Check if backend health endpoint is accessible
    const response = await page.request.get('http://localhost:3003/api/health');
    expect(response.status()).toBe(200);
  });

  test('should display theme toggle', async ({ page }) => {
    // Check if theme toggle is present
    const themeToggle = page.locator('button[title*="theme"], button[title*="mode"]');
    await expect(themeToggle).toBeVisible();
  });
});