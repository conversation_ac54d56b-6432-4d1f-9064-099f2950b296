// Unit tests for batch OCR functionality

describe('Batch OCR - formatOCRResultAsText', () => {
  // Test the formatOCRResultAsText function
  function formatOCRResultAsText(ocrData, documentType) {
    const lines = [];
    
    lines.push('=== OCR EXTRACTION RESULTS ===');
    lines.push(`Extraction Date: ${new Date().toISOString()}`);
    lines.push(`Document Type: ${documentType}`);
    lines.push('');
    
    if (documentType.includes('driver_license')) {
      lines.push('Driver License Information:');
      lines.push(`Name: ${ocrData.firstName || ''} ${ocrData.lastName || ''}`);
      if (ocrData.dateOfBirth) lines.push(`Date of Birth: ${ocrData.dateOfBirth}`);
      if (ocrData.licenseNumber) lines.push(`License Number: ${ocrData.licenseNumber}`);
      if (ocrData.expirationDate) lines.push(`Expiration Date: ${ocrData.expirationDate}`);
      if (ocrData.address) lines.push(`Address: ${ocrData.address}`);
      if (ocrData.state) lines.push(`State: ${ocrData.state}`);
    } else if (documentType === 'passport') {
      lines.push('Passport Information:');
      lines.push(`Name: ${ocrData.firstName || ''} ${ocrData.lastName || ''}`);
      if (ocrData.passportNumber) lines.push(`Passport Number: ${ocrData.passportNumber}`);
      if (ocrData.dateOfBirth) lines.push(`Date of Birth: ${ocrData.dateOfBirth}`);
      if (ocrData.expirationDate) lines.push(`Expiration Date: ${ocrData.expirationDate}`);
      if (ocrData.nationality) lines.push(`Nationality: ${ocrData.nationality}`);
    } else {
      lines.push('Extracted Information:');
      Object.entries(ocrData).forEach(([key, value]) => {
        if (value && key !== 'confidence' && key !== 'rawText') {
          lines.push(`${key}: ${value}`);
        }
      });
    }
    
    if (ocrData.confidence) {
      lines.push('');
      lines.push(`Confidence Score: ${(ocrData.confidence * 100).toFixed(1)}%`);
    }
    
    if (ocrData.rawText) {
      lines.push('');
      lines.push('=== RAW TEXT ===');
      lines.push(ocrData.rawText);
    }
    
    return lines.join('\\n');
  }

  beforeEach(() => {
    jest.clearAllMocks();
    // Mock current date for consistent testing
    jest.spyOn(Date.prototype, 'toISOString').mockReturnValue('2024-01-01T00:00:00.000Z');
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('should format US driver license data correctly', () => {
    const ocrData = {
      firstName: 'John',
      lastName: 'Doe',
      dateOfBirth: '1990-01-01',
      licenseNumber: '********',
      expirationDate: '2025-01-01',
      address: '123 Main St, City, State',
      state: 'CA',
      confidence: 0.95,
      rawText: 'Raw extracted text here'
    };

    const result = formatOCRResultAsText(ocrData, 'us_driver_license');

    expect(result).toContain('=== OCR EXTRACTION RESULTS ===');
    expect(result).toContain('Document Type: us_driver_license');
    expect(result).toContain('Driver License Information:');
    expect(result).toContain('Name: John Doe');
    expect(result).toContain('Date of Birth: 1990-01-01');
    expect(result).toContain('License Number: ********');
    expect(result).toContain('State: CA');
    expect(result).toContain('Confidence Score: 95.0%');
    expect(result).toContain('=== RAW TEXT ===');
    expect(result).toContain('Raw extracted text here');
  });

  test('should format passport data correctly', () => {
    const ocrData = {
      firstName: 'Jane',
      lastName: 'Smith',
      passportNumber: '********',
      dateOfBirth: '1985-05-15',
      expirationDate: '2030-05-15',
      nationality: 'US',
      confidence: 0.88
    };

    const result = formatOCRResultAsText(ocrData, 'passport');

    expect(result).toContain('Passport Information:');
    expect(result).toContain('Name: Jane Smith');
    expect(result).toContain('Passport Number: ********');
    expect(result).toContain('Nationality: US');
    expect(result).toContain('Confidence Score: 88.0%');
  });

  test('should handle missing fields gracefully', () => {
    const ocrData = {
      firstName: 'John',
      // lastName missing
      confidence: 0.75
    };

    const result = formatOCRResultAsText(ocrData, 'us_driver_license');

    expect(result).toContain('Name: John '); // Should handle empty lastName
    expect(result).toContain('Confidence Score: 75.0%');
    expect(result).not.toContain('undefined');
  });

  test('should format unknown document types using generic format', () => {
    const ocrData = {
      customField1: 'value1',
      customField2: 'value2',
      confidence: 0.65,
      rawText: 'Some raw text'
    };

    const result = formatOCRResultAsText(ocrData, 'unknown_document');

    expect(result).toContain('Extracted Information:');
    expect(result).toContain('customField1: value1');
    expect(result).toContain('customField2: value2');
    expect(result).not.toContain('confidence: 0.65'); // Should exclude confidence from generic list
    expect(result).not.toContain('rawText: Some raw text'); // Should exclude rawText from generic list
    expect(result).toContain('=== RAW TEXT ===');
    expect(result).toContain('Some raw text');
  });
});

describe('Batch OCR API Integration', () => {
  test('should validate required fields for batch processing', () => {
    const requiredFields = ['images', 'mode', 'regionContext'];
    const validRequest = {
      images: [
        { path: '/test/image1.jpg', filename: 'image1.jpg' },
        { path: '/test/image2.jpg', filename: 'image2.jpg' }
      ],
      mode: 'auto-detect',
      regionContext: 'us'
    };

    // Test that all required fields are present
    requiredFields.forEach(field => {
      expect(validRequest).toHaveProperty(field);
    });

    expect(Array.isArray(validRequest.images)).toBe(true);
    expect(validRequest.images.length).toBeGreaterThan(0);
    expect(['auto-detect', 'us_driver_license', 'australian_driver_license']).toContain(validRequest.mode);
    expect(['us', 'australian']).toContain(validRequest.regionContext);
  });

  test('should handle chunked processing correctly', () => {
    const CHUNK_SIZE = 10;
    const images = Array.from({ length: 25 }, (_, i) => ({
      path: `/test/image${i}.jpg`,
      filename: `image${i}.jpg`
    }));

    // Calculate expected chunks
    const expectedChunks = Math.ceil(images.length / CHUNK_SIZE);
    expect(expectedChunks).toBe(3); // 25 images / 10 per chunk = 3 chunks

    // Verify chunk sizes
    for (let i = 0; i < images.length; i += CHUNK_SIZE) {
      const chunk = images.slice(i, i + CHUNK_SIZE);
      expect(chunk.length).toBeLessThanOrEqual(CHUNK_SIZE);
      
      if (i + CHUNK_SIZE >= images.length) {
        // Last chunk
        expect(chunk.length).toBe(images.length % CHUNK_SIZE || CHUNK_SIZE);
      } else {
        // Full chunk
        expect(chunk.length).toBe(CHUNK_SIZE);
      }
    }
  });
});