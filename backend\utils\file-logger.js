const fs = require('fs');
const path = require('path');

class FileLogger {
  constructor(logFile = 'smart-analyzer-debug.log') {
    this.logPath = path.join(__dirname, '../../data/logs', logFile);
    this.ensureLogDir();
    this.log('='.repeat(80));
    this.log(`SMART ANALYZER DEBUG SESSION STARTED: ${new Date().toISOString()}`);
    this.log('='.repeat(80));
  }

  ensureLogDir() {
    const logDir = path.dirname(this.logPath);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  log(message, data = null) {
    const timestamp = new Date().toISOString();
    let logEntry = `[${timestamp}] ${message}`;
    
    if (data) {
      logEntry += `\n${JSON.stringify(data, null, 2)}`;
    }
    
    logEntry += '\n';
    
    // Write to file
    fs.appendFileSync(this.logPath, logEntry);
    
    // Also log to console
    console.log(message);
    if (data) {
      console.log(data);
    }
  }

  error(message, error = null) {
    const timestamp = new Date().toISOString();
    let logEntry = `[${timestamp}] ERROR: ${message}`;
    
    if (error) {
      logEntry += `\nError Details: ${error.message}`;
      logEntry += `\nStack: ${error.stack}`;
    }
    
    logEntry += '\n';
    
    fs.appendFileSync(this.logPath, logEntry);
    console.error(message, error);
  }
}

module.exports = FileLogger;