import { NextRequest, NextResponse } from "next/server";
import { getBackendUrl } from "@/utils/port-config-server";

export async function POST(request: NextRequest) {
  try {
    const BACKEND_URL = getBackendUrl();

    // Get the request body
    const body = await request.text();

    // Forward the request to the backend
    const response = await fetch(`${BACKEND_URL}/api/filesystem/scan`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // Forward relevant headers
        "User-Agent": request.headers.get("User-Agent") || "",
      },
      body: body,
    });

    // Get response data
    const data = await response.text();

    // Return response with same status and headers
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("API Proxy Error (filesystem/scan):", error);
    return NextResponse.json(
      {
        success: false,
        error: "Proxy error: Failed to forward request to backend",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
