import { test, expect } from '@playwright/test';

test.describe('Gray Mode Theme Implementation', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');
  });

  test('should cycle through all four theme modes', async ({ page }) => {
    // Find the theme toggle button
    const themeToggle = page.locator('button[title*="mode"], button[title*="theme"]').first();
    await expect(themeToggle).toBeVisible();

    const htmlElement = page.locator('html');
    
    // First, let's reset to a known state by clicking until we get to light mode
    let attempts = 0;
    while (attempts < 5) {
      const currentTitle = await themeToggle.getAttribute('title');
      if (currentTitle?.toLowerCase().includes('light')) {
        break;
      }
      await themeToggle.click();
      await page.waitForTimeout(300);
      attempts++;
    }
    
    // Now test the cycle from light mode
    // Light → Dark
    await themeToggle.click();
    await page.waitForTimeout(500);
    await expect(htmlElement).toHaveClass(/dark/);
    
    // Dark → Gray
    await themeToggle.click();
    await page.waitForTimeout(500);
    await expect(htmlElement).toHaveClass(/gray/);
    
    // Gray → System
    await themeToggle.click();
    await page.waitForTimeout(500);
    const htmlClassSystem = await htmlElement.getAttribute('class');
    expect(htmlClassSystem).not.toContain('dark');
    expect(htmlClassSystem).not.toContain('gray');
    
    // System → Light
    await themeToggle.click();
    await page.waitForTimeout(500);
    const htmlClassFinal = await htmlElement.getAttribute('class');
    expect(htmlClassFinal).not.toContain('dark');
    expect(htmlClassFinal).not.toContain('gray');
  });

  test('should display correct theme icons', async ({ page }) => {
    const themeToggle = page.locator('button[title*="mode"], button[title*="theme"]').first();
    
    // Reset to light mode first
    let attempts = 0;
    while (attempts < 5) {
      const currentTitle = await themeToggle.getAttribute('title');
      if (currentTitle?.toLowerCase().includes('light')) {
        break;
      }
      await themeToggle.click();
      await page.waitForTimeout(300);
      attempts++;
    }
    
    // Test the cycle: Light → Dark → Gray → System → Light
    const expectedTitles = ['Dark mode', 'Gray mode', 'System theme', 'Light mode'];
    
    for (let i = 0; i < expectedTitles.length; i++) {
      await themeToggle.click();
      await page.waitForTimeout(500);
      
      const buttonTitle = await themeToggle.getAttribute('title');
      expect(buttonTitle).toBe(expectedTitles[i]);
    }
  });

  test('should persist theme selection', async ({ page }) => {
    const themeToggle = page.locator('button[title*="mode"], button[title*="theme"]').first();
    
    // Reset to light mode first
    let attempts = 0;
    while (attempts < 5) {
      const currentTitle = await themeToggle.getAttribute('title');
      if (currentTitle?.toLowerCase().includes('light')) {
        break;
      }
      await themeToggle.click();
      await page.waitForTimeout(300);
      attempts++;
    }
    
    // Set to gray mode: Light → Dark → Gray
    await themeToggle.click(); // Light → Dark
    await page.waitForTimeout(300);
    await themeToggle.click(); // Dark → Gray
    await page.waitForTimeout(500);
    
    // Verify gray mode is applied
    await expect(page.locator('html')).toHaveClass(/gray/);
    
    // Reload page and verify theme persists
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(500);
    
    // Gray mode should still be active
    await expect(page.locator('html')).toHaveClass(/gray/);
  });

  test('should apply gray theme styles to components', async ({ page }) => {
    const themeToggle = page.locator('button[title*="mode"], button[title*="theme"]').first();
    
    // Reset to light mode first
    let attempts = 0;
    while (attempts < 5) {
      const currentTitle = await themeToggle.getAttribute('title');
      if (currentTitle?.toLowerCase().includes('light')) {
        break;
      }
      await themeToggle.click();
      await page.waitForTimeout(300);
      attempts++;
    }
    
    // Set to gray mode: Light → Dark → Gray
    await themeToggle.click(); // Light → Dark
    await page.waitForTimeout(300);
    await themeToggle.click(); // Dark → Gray
    await page.waitForTimeout(500);
    
    // Verify gray mode is applied
    await expect(page.locator('html')).toHaveClass(/gray/);
    
    // Test that components have gray theme styles
    // Check if cards have the correct background color
    const cards = page.locator('[class*="card"], .card, [data-testid*="card"]');
    if (await cards.count() > 0) {
      const cardElement = cards.first();
      const cardStyles = await cardElement.evaluate((el) => {
        const computedStyles = window.getComputedStyle(el);
        return {
          backgroundColor: computedStyles.backgroundColor,
          color: computedStyles.color
        };
      });
      
      // Verify that the background and text colors are applied
      expect(cardStyles.backgroundColor).not.toBe('rgba(0, 0, 0, 0)');
      expect(cardStyles.color).not.toBe('rgba(0, 0, 0, 0)');
    }
    
    // Test buttons have gray theme styles
    const buttons = page.locator('button').first();
    if (await buttons.isVisible()) {
      const buttonStyles = await buttons.evaluate((el) => {
        const computedStyles = window.getComputedStyle(el);
        return {
          backgroundColor: computedStyles.backgroundColor,
          color: computedStyles.color
        };
      });
      
      expect(buttonStyles.backgroundColor).not.toBe('rgba(0, 0, 0, 0)');
      expect(buttonStyles.color).not.toBe('rgba(0, 0, 0, 0)');
    }
  });

  test('should handle gray theme in modals/dialogs', async ({ page }) => {
    const themeToggle = page.locator('button[title*="mode"], button[title*="theme"]').first();
    
    // Reset to light mode first
    let attempts = 0;
    while (attempts < 5) {
      const currentTitle = await themeToggle.getAttribute('title');
      if (currentTitle?.toLowerCase().includes('light')) {
        break;
      }
      await themeToggle.click();
      await page.waitForTimeout(300);
      attempts++;
    }
    
    // Set to gray mode: Light → Dark → Gray
    await themeToggle.click(); // Light → Dark
    await page.waitForTimeout(300);
    await themeToggle.click(); // Dark → Gray
    await page.waitForTimeout(500);
    
    // Look for any button that might open a modal/dialog
    const modalTriggers = page.locator('button:has-text("Settings"), button:has-text("Create"), button:has-text("Add"), button:has-text("New")');
    
    if (await modalTriggers.count() > 0) {
      const trigger = modalTriggers.first();
      await trigger.click();
      await page.waitForTimeout(500);
      
      // Check if a modal/dialog is visible
      const modal = page.locator('[role="dialog"], .dialog, [data-testid*="dialog"], [data-testid*="modal"]');
      
      if (await modal.count() > 0) {
        await expect(modal.first()).toBeVisible();
        
        // Verify the modal has gray theme styles
        const modalStyles = await modal.first().evaluate((el) => {
          const computedStyles = window.getComputedStyle(el);
          return {
            backgroundColor: computedStyles.backgroundColor,
            color: computedStyles.color,
            borderColor: computedStyles.borderColor
          };
        });
        
        // Verify gray theme is applied to modal
        expect(modalStyles.backgroundColor).not.toBe('rgba(0, 0, 0, 0)');
        expect(modalStyles.color).not.toBe('rgba(0, 0, 0, 0)');
        
        // Close modal by pressing Escape or clicking outside
        await page.keyboard.press('Escape');
        await page.waitForTimeout(300);
      }
    }
  });

  test('should support keyboard navigation in gray theme', async ({ page }) => {
    const themeToggle = page.locator('button[title*="mode"], button[title*="theme"]').first();
    
    // Reset to light mode first
    let attempts = 0;
    while (attempts < 5) {
      const currentTitle = await themeToggle.getAttribute('title');
      if (currentTitle?.toLowerCase().includes('light')) {
        break;
      }
      await themeToggle.click();
      await page.waitForTimeout(300);
      attempts++;
    }
    
    // Set to gray mode: Light → Dark → Gray
    await themeToggle.click(); // Light → Dark
    await page.waitForTimeout(300);
    await themeToggle.click(); // Dark → Gray
    await page.waitForTimeout(500);
    
    // Test keyboard navigation
    await page.keyboard.press('Tab');
    await page.waitForTimeout(200);
    
    // Check that focus indicators are visible in gray theme
    const focusedElement = page.locator(':focus');
    if (await focusedElement.count() > 0) {
      const focusStyles = await focusedElement.evaluate((el) => {
        const computedStyles = window.getComputedStyle(el);
        return {
          outline: computedStyles.outline,
          boxShadow: computedStyles.boxShadow,
          borderColor: computedStyles.borderColor
        };
      });
      
      // Verify focus indicators are visible
      const hasFocusIndicator = focusStyles.outline !== 'none' || 
                               focusStyles.boxShadow !== 'none' || 
                               focusStyles.borderColor !== 'rgba(0, 0, 0, 0)';
      expect(hasFocusIndicator).toBe(true);
    }
  });

  test('should maintain accessibility in gray theme', async ({ page }) => {
    const themeToggle = page.locator('button[title*="mode"], button[title*="theme"]').first();
    
    // Reset to light mode first
    let attempts = 0;
    while (attempts < 5) {
      const currentTitle = await themeToggle.getAttribute('title');
      if (currentTitle?.toLowerCase().includes('light')) {
        break;
      }
      await themeToggle.click();
      await page.waitForTimeout(300);
      attempts++;
    }
    
    // Set to gray mode: Light → Dark → Gray
    await themeToggle.click(); // Light → Dark
    await page.waitForTimeout(300);
    await themeToggle.click(); // Dark → Gray
    await page.waitForTimeout(500);
    
    // Run basic accessibility checks
    await expect(page.locator('html')).toHaveClass(/gray/);
    
    // Verify theme toggle has proper accessibility attributes
    await expect(themeToggle).toHaveAttribute('title');
    const srText = page.locator('button[title*="mode"], button[title*="theme"] .sr-only').first();
    if (await srText.count() > 0) {
      await expect(srText).toBeAttached();
    }
    
    // Check that text contrast is maintained
    const textElements = page.locator('p, h1, h2, h3, h4, h5, h6, span, div').first();
    if (await textElements.count() > 0) {
      const textStyles = await textElements.evaluate((el) => {
        const computedStyles = window.getComputedStyle(el);
        return {
          color: computedStyles.color,
          backgroundColor: computedStyles.backgroundColor
        };
      });
      
      // Verify text has color applied (basic contrast check)
      expect(textStyles.color).not.toBe('rgba(0, 0, 0, 0)');
    }
  });
});