"use client";

import { useState, useEffect, use<PERSON><PERSON>back, useRef, useMemo } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import Fuse from "fuse.js";
import {
  RotateCw,
  RotateCcw,
  Eye,
  Download,
  Trash2,
  RefreshCw,
  Loader2,
  CheckCircle2,
  AlertCircle,
  Clock,
  FlipVertical2,
  Save,
  Database,
  X,
} from "lucide-react";
import Image from "next/image";
import { ImageFile } from "@/types";
import { Filter, FilterStack } from "@/types/filters";
import FilterBuilder from "./filter-builder";
import SelectionControls from "./selection-controls";
import PaginationControls from "./pagination-controls";
import SmartChips from "./smart-chips";
import { useS<PERSON><PERSON><PERSON>yzer } from "@/hooks/useSmartAnalyzer";
import { cn } from "@/lib/utils";

// Helper functions for smart chip selection
function matchesSizeRange(sizeKB: number, range: string): boolean {
  switch (range) {
    case "< 100KB":
      return sizeKB < 100;
    case "100KB - 300KB":
      return sizeKB >= 100 && sizeKB < 300;
    case "300KB - 500KB":
      return sizeKB >= 300 && sizeKB < 500;
    case "500KB - 1MB":
      return sizeKB >= 500 && sizeKB < 1024;
    case "1MB - 2MB":
      return sizeKB >= 1024 && sizeKB < 2048;
    case "2MB - 5MB":
      return sizeKB >= 2048 && sizeKB < 5120;
    case "> 5MB":
      return sizeKB >= 5120;
    default:
      return false;
  }
}

function matchesResolutionRange(
  width: number,
  height: number,
  range: string
): boolean {
  const megapixels = (width * height) / (1024 * 1024);
  switch (range) {
    case "< 1MP":
      return megapixels < 1;
    case "1-2MP":
      return megapixels >= 1 && megapixels < 2;
    case "2-5MP":
      return megapixels >= 2 && megapixels < 5;
    case "5-8MP":
      return megapixels >= 5 && megapixels < 8;
    case "8-12MP":
      return megapixels >= 8 && megapixels < 12;
    case "> 12MP":
      return megapixels >= 12;
    default:
      return false;
  }
}

interface EnhancedImageGridProps {
  images: ImageFile[]; // Currently displayed images (paginated)
  allImages: ImageFile[]; // All images in folder
  filteredImages: ImageFile[]; // All filtered images (before pagination)
  folderPath?: string; // Path to current folder for Smart Analyzer
  onImageSelect: (image: ImageFile) => void;
  onImageRotate: (imageId: string, degrees: number) => void;
  selectedImageId?: string;
  gridSize: number;
  className?: string;
  onClearCache?: (imageId?: string) => void; // Optional cache clearing callback
  // Multi-select props
  multiSelect?: boolean;
  selectedIds?: Set<string>;
  onSelectionChange?: (ids: Set<string>) => void;
  // Filter system props
  filters: FilterStack;
  onAddFilter: (filter: Filter) => void;
  onRemoveFilter: (index: number) => void;
  onClearAllFilters: () => void;
  // Pagination props
  showAllImages: boolean;
  onShowAllToggle: () => void;
  currentPage: number;
  totalPages: number;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  // Cache map update callback
  onHasCacheMapUpdate?: (map: Map<string, boolean>) => void;
}

interface ImageOperationStatus {
  id: string;
  operation: "rotating" | "processing" | "completed" | "error";
  progress?: number;
  message?: string;
}

export default function EnhancedImageGrid({
  images = [],
  allImages = [],
  filteredImages = [],
  folderPath,
  onImageSelect,
  onImageRotate,
  selectedImageId,
  gridSize,
  className,
  onClearCache,
  multiSelect = false,
  selectedIds = new Set(),
  onSelectionChange,
  // Filter system props
  filters,
  onAddFilter,
  onRemoveFilter,
  onClearAllFilters,
  // Pagination props
  showAllImages,
  onShowAllToggle,
  currentPage,
  totalPages,
  pageSize,
  onPageChange,
  onPageSizeChange,
  onHasCacheMapUpdate,
}: EnhancedImageGridProps) {
  const [imageStates, setImageStates] = useState<
    Map<string, ImageOperationStatus>
  >(new Map());
  const [refreshingImages, setRefreshingImages] = useState<Set<string>>(
    new Set()
  );
  const [cacheStatus, setCacheStatus] = useState<Map<string, boolean>>(
    new Map()
  );
  const [hoveredImage, setHoveredImage] = useState<string | null>(null);
  const [hoverTimeout, setHoverTimeout] = useState<NodeJS.Timeout | null>(null);
  const [previewPosition, setPreviewPosition] = useState({ x: 0, y: 0 });
  const hoverTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Smart Filter Analyzer hook
  const smartAnalyzer = useSmartAnalyzer(folderPath);

  // Optimized multi-select handler - minimal re-renders
  const selectedIdsRef = useRef<Set<string>>(selectedIds);

  // Update ref when props change
  useEffect(() => {
    selectedIdsRef.current = selectedIds;
  }, [selectedIds]);

  const toggleImageSelection = useCallback(
    (imageId: string) => {
      if (!multiSelect || !onSelectionChange) return;

      // Work with the ref for immediate updates
      const currentSelection = selectedIdsRef.current;
      const newSelectedIds = new Set(currentSelection);

      if (newSelectedIds.has(imageId)) {
        newSelectedIds.delete(imageId);
      } else {
        newSelectedIds.add(imageId);
      }

      // Update the ref immediately
      selectedIdsRef.current = newSelectedIds;

      // Notify parent with minimal delay to batch updates
      onSelectionChange(newSelectedIds);
    },
    [multiSelect, onSelectionChange]
  );

  // Combined click handler for both single and multi-select modes
  const handleImageClick = useCallback(
    (image: ImageFile) => {
      if (multiSelect) {
        toggleImageSelection(image.id);
      } else {
        onImageSelect(image);
      }
    },
    [multiSelect, toggleImageSelection, onImageSelect]
  );

  // Track images that are being refreshed by the parent component
  useEffect(() => {
    // When images prop changes, clear any refreshing states for updated images
    setRefreshingImages((prev) => {
      const newSet = new Set(prev);
      if (Array.isArray(images) && images.length > 0) {
        images.forEach((image) => {
          // If an image was refreshing and now has a new timestamp, clear the refreshing state
          if (image && image.id && newSet.has(image.id)) {
            newSet.delete(image.id);
          }
        });
      }
      return newSet;
    });
  }, [images]);

  // Enhanced rotate function with progress tracking
  const handleRotate = async (imageId: string, degrees: number) => {
    // Set rotating state with initial progress
    setImageStates((prev) =>
      new Map(prev).set(imageId, {
        id: imageId,
        operation: "rotating",
        progress: 0,
        message: `Rotating ${
          degrees === 180
            ? "180°"
            : degrees > 0
            ? "clockwise"
            : "counter-clockwise"
        }...`,
      })
    );

    // Simulate progress updates for user feedback
    const progressInterval = setInterval(() => {
      setImageStates((prev) => {
        const current = prev.get(imageId);
        if (
          current &&
          current.progress !== undefined &&
          current.progress < 90
        ) {
          return new Map(prev).set(imageId, {
            ...current,
            progress: current.progress + 15,
          });
        }
        return prev;
      });
    }, 100);

    try {
      // Call the parent callback to handle rotation
      if (onImageRotate) {
        await onImageRotate(imageId, degrees);
      }

      // Clear progress interval
      clearInterval(progressInterval);

      // Set completed state
      setImageStates((prev) =>
        new Map(prev).set(imageId, {
          id: imageId,
          operation: "completed",
          progress: 100,
          message: "Rotation completed!",
        })
      );

      // The parent component will update the images state with new timestamps
      // which will trigger our useEffect to clear the refreshing state

      // Clear status after delay to show completion feedback
      setTimeout(() => {
        setImageStates((prev) => {
          const newMap = new Map(prev);
          newMap.delete(imageId);
          return newMap;
        });
      }, 1500);
    } catch (error) {
      clearInterval(progressInterval);

      setImageStates((prev) =>
        new Map(prev).set(imageId, {
          id: imageId,
          operation: "error",
          message: error instanceof Error ? error.message : "Rotation failed",
        })
      );

      // Clear error status after delay
      setTimeout(() => {
        setImageStates((prev) => {
          const newMap = new Map(prev);
          newMap.delete(imageId);
          return newMap;
        });
      }, 3000);
    }
  };

  const getImageUrl = useCallback((image: ImageFile) => {
    // Always use the provided thumbnailUrl - it may have been cache-busted by parent
    const baseUrl = image.thumbnailUrl.split("?")[0];

    // If parent already added cache busting, use it
    if (image.thumbnailUrl.includes("?t=")) {
      return image.thumbnailUrl;
    }

    // Otherwise, add our own cache busting based on lastModified
    const timestamp = new Date(image.lastModified).getTime();
    return `${baseUrl}?t=${timestamp}&cb=${Math.random()
      .toString(36)
      .substr(2, 9)}`;
  }, []);

  const getImageOperationState = useCallback(
    (imageId: string) => {
      return imageStates.get(imageId);
    },
    [imageStates]
  );

  // Hover preview functionality
  const handleMouseEnter = useCallback(
    (image: ImageFile, event: React.MouseEvent<HTMLDivElement>) => {
      // Check if this specific image has an operation in progress
      const operationStatus = imageStates.get(image.id);
      if (
        operationStatus?.operation === "rotating" ||
        operationStatus?.operation === "processing"
      ) {
        return;
      }

      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }

      const rect = event.currentTarget.getBoundingClientRect();
      const scrollTop =
        window.pageYOffset || document.documentElement.scrollTop;
      const scrollLeft =
        window.pageXOffset || document.documentElement.scrollLeft;

      // Calculate optimal position for preview
      const previewWidth = 650;
      const previewHeight = 500;

      let x = rect.right + scrollLeft + 15;
      let y = rect.top + scrollTop - (previewHeight - rect.height) / 2;

      // Adjust if preview would go off screen
      if (x + previewWidth > window.innerWidth + scrollLeft) {
        x = rect.left + scrollLeft - previewWidth - 15;
      }
      if (y < scrollTop + 10) {
        y = scrollTop + 10;
      }
      if (y + previewHeight > scrollTop + window.innerHeight - 10) {
        y = scrollTop + window.innerHeight - previewHeight - 10;
      }

      setPreviewPosition({ x, y });

      hoverTimeoutRef.current = setTimeout(() => {
        // Double-check operation status before showing preview
        const currentStatus = imageStates.get(image.id);
        if (
          !currentStatus ||
          (currentStatus.operation !== "rotating" &&
            currentStatus.operation !== "processing")
        ) {
          setHoveredImage(image.id);
        }
      }, 2000); // 2 second delay as specified
    },
    [imageStates]
  );

  const handleMouseLeave = useCallback(() => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
    setHoveredImage(null);
  }, []);

  // Cache clearing functionality
  const handleClearCache = async (imageId: string) => {
    try {
      const image = Array.isArray(images)
        ? images.find((img) => img.id === imageId)
        : null;
      if (!image) return;

      const encodedPath = btoa(image.path);
      const response = await fetch("/api/ocr/cache/delete", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ imagePath: encodedPath }),
      });

      if (response.ok) {
        // Update cache status locally
        setCacheStatus((prev) => {
          const newStatus = new Map(prev);
          newStatus.set(imageId, false);
          return newStatus;
        });

        // Notify parent component if callback provided
        if (onClearCache) {
          onClearCache(imageId);
        }

        console.log("Cache cleared for image:", image.filename);
      } else {
        console.error("Failed to clear cache for image:", image.filename);
      }
    } catch (error) {
      console.error("Error clearing cache:", error);
    }
  };

  // Cache checking with improved performance and error handling
  const checkedImages = useRef(new Set<string>());
  const lastCheckTime = useRef(0);
  const isCheckingCache = useRef(false);
  const lastImageCount = useRef(0);
  const failureCount = useRef(0);
  const circuitBreakerOpen = useRef(false);

  // Clear checked images cache when image list changes significantly
  useEffect(() => {
    if (allImages.length !== lastImageCount.current) {
      // If the number of images changed significantly, clear the cache
      const difference = Math.abs(allImages.length - lastImageCount.current);
      if (difference > 10 || allImages.length === 0) {
        console.log(
          "🔄 Clearing checked images cache due to significant change"
        );
        checkedImages.current.clear();
      }
      lastImageCount.current = allImages.length;
    }
  }, [allImages.length]);

  // Check cache status for images with intelligent batching and caching
  useEffect(() => {
    const checkCacheStatus = async () => {
      if (!Array.isArray(allImages) || allImages.length === 0) return;

      // Prevent multiple simultaneous cache checks
      if (isCheckingCache.current) {
        console.log("🔄 Cache check already in progress, skipping...");
        return;
      }

      // Circuit breaker: if too many failures, stop checking for a while
      if (circuitBreakerOpen.current) {
        console.log("🚨 Circuit breaker open, skipping cache checks");
        return;
      }

      // Rate limiting: don't check more than once every 2 seconds
      const now = Date.now();
      if (now - lastCheckTime.current < 2000) {
        console.log("🔄 Rate limiting cache checks, skipping...");
        return;
      }

      isCheckingCache.current = true;
      lastCheckTime.current = now;

      const newCacheStatus = new Map<string, boolean>(cacheStatus);

      // Only check images that haven't been checked yet or visible images
      const imagesToCheck = allImages.filter(
        (image) => !checkedImages.current.has(image.id)
      );

      if (imagesToCheck.length === 0) {
        isCheckingCache.current = false;
        return;
      }

      console.log(
        `🔍 Checking cache for ${imagesToCheck.length} new images...`
      );

      // Prioritize visible images first, then check others gradually
      const visibleImages = images.filter((img) =>
        imagesToCheck.some((checkImg) => checkImg.id === img.id)
      );
      const otherImages = imagesToCheck.filter(
        (img) => !visibleImages.some((visImg) => visImg.id === img.id)
      );

      // Check visible images first with smaller batches
      const batchSize = 2; // Further reduced batch size to prevent overwhelming
      const maxBackgroundChecks = Math.min(5, otherImages.length); // Even more conservative
      const allImagesToProcess = [
        ...visibleImages,
        ...otherImages.slice(0, maxBackgroundChecks),
      ]; // Limit background checks

      // Safety check: don't process too many images at once
      if (allImagesToProcess.length > 20) {
        console.log("🚨 Too many images to check, limiting to 20 for safety");
        allImagesToProcess.splice(20);
      }

      try {
        for (let i = 0; i < allImagesToProcess.length; i += batchSize) {
          const batch = allImagesToProcess.slice(i, i + batchSize);

          const promises = batch.map(async (image) => {
            try {
              const controller = new AbortController();
              const timeoutId = setTimeout(() => controller.abort(), 3000); // Reduced timeout

              const response = await fetch(`/api/images/${image.id}/cache`, {
                method: "GET",
                signal: controller.signal,
              });

              clearTimeout(timeoutId);

              if (response.ok) {
                const cacheInfo = await response.json();
                const hasCache =
                  cacheInfo.exists &&
                  cacheInfo.availableVariations &&
                  cacheInfo.availableVariations.length > 0;

                // Mark as checked
                checkedImages.current.add(image.id);

                return { imageId: image.id, hasCache };
              } else {
                console.warn(
                  `Cache check failed for ${image.filename}: ${response.status}`
                );
                failureCount.current++;
              }
            } catch (error) {
              failureCount.current++;
              if (error instanceof Error && error.name === "AbortError") {
                console.warn(`Cache check timeout for: ${image.filename}`);
              } else {
                console.warn(`Cache check error for: ${image.filename}`, error);
              }
            }

            // Mark as checked even if failed to avoid repeated attempts
            checkedImages.current.add(image.id);
            return { imageId: image.id, hasCache: false };
          });

          const results = await Promise.all(promises);
          results.forEach(({ imageId, hasCache }) => {
            newCacheStatus.set(imageId, hasCache);
          });

          // Update state incrementally for better UX
          setCacheStatus(new Map(newCacheStatus));

          // Longer delay between batches to prevent overwhelming
          if (i + batchSize < allImagesToProcess.length) {
            await new Promise((resolve) => setTimeout(resolve, 200));
          }
        }
      } catch (error) {
        console.warn("Cache status check failed:", error);
        failureCount.current += 5; // Penalize batch failures more heavily
      }

      // Circuit breaker logic
      if (failureCount.current > 10) {
        console.warn(
          "🚨 Too many cache check failures, opening circuit breaker for 30 seconds"
        );
        circuitBreakerOpen.current = true;
        setTimeout(() => {
          console.log("🔄 Circuit breaker reset, resuming cache checks");
          circuitBreakerOpen.current = false;
          failureCount.current = 0;
        }, 30000);
      }

      // Update parent cache map for filtering
      if (onHasCacheMapUpdate) {
        onHasCacheMapUpdate(newCacheStatus);
      }

      isCheckingCache.current = false;
    };

    // Longer debounce to prevent excessive API calls
    const timeoutId = setTimeout(() => {
      checkCacheStatus();
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [allImages, images, onHasCacheMapUpdate, cacheStatus]);

  if (!Array.isArray(allImages) || allImages.length === 0) {
    return (
      <Card className={cn("h-full", className)}>
        <CardContent className="flex items-center justify-center h-full text-muted-foreground">
          <div className="text-center">
            <Eye className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <p className="mb-2">No images in this folder</p>
            <p className="text-sm">
              Select a folder containing images to get started
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Power Filter System */}
      <FilterBuilder
        filters={filters}
        onAddFilter={onAddFilter}
        onRemoveFilter={onRemoveFilter}
        onClearAll={onClearAllFilters}
        smartAnalyzer={smartAnalyzer}
      />

      {/* Selection Controls */}
      {multiSelect && (
        <SelectionControls
          filteredImages={filteredImages}
          totalImages={allImages}
          selectedIds={selectedIds}
          onSelectionChange={onSelectionChange || (() => {})}
        />
      )}

      {/* Pagination Controls */}
      <PaginationControls
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        totalItems={filteredImages.length}
        onPageChange={onPageChange}
        onPageSizeChange={onPageSizeChange}
        showAll={showAllImages}
        onShowAllToggle={onShowAllToggle}
      />

      {/* Grid Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className="font-medium">Images</h3>
          <Badge variant="outline">{(images || []).length} showing</Badge>
          {(filteredImages || []).length !== (allImages || []).length && (
            <Badge variant="secondary">
              of {(filteredImages || []).length} filtered
            </Badge>
          )}
          {(allImages || []).length !== (filteredImages || []).length && (
            <Badge variant="outline">({(allImages || []).length} total)</Badge>
          )}
          {multiSelect && selectedIds.size > 0 && (
            <Badge variant="secondary" className="bg-primary/10">
              {selectedIds.size} selected
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <span>Grid size: {gridSize}px</span>
          {multiSelect && <span>• Multi-select mode</span>}
          {filters.length > 0 && <span>• {filters.length} filters active</span>}
        </div>
      </div>

      {/* Image Grid */}
      <div
        className="grid gap-6 p-2 overflow-y-auto"
        style={{
          gridTemplateColumns: `repeat(auto-fill, minmax(${gridSize}px, 1fr))`,
        }}
      >
        {Array.isArray(images) && images.length > 0
          ? images.map((image) => {
              const operationStatus = imageStates.get(image.id);
              const isRefreshing = refreshingImages.has(image.id);
              const isSelected = multiSelect
                ? selectedIds.has(image.id)
                : selectedImageId === image.id;
              const hasCache = cacheStatus.get(image.id) || false;
              if (hasCache) {
                console.log(`💾 Showing cache icon for ${image.filename}`);
              }

              return (
                <Card
                  key={image.id}
                  className={cn(
                    "relative overflow-hidden transition-all duration-300 hover:shadow-lg cursor-pointer group focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2",
                    isSelected &&
                      "ring-2 ring-purple-500 shadow-xl shadow-purple-500/60 dark:shadow-purple-400/50 gray:shadow-purple-400/50 ring-offset-1 ring-offset-background",
                    operationStatus?.operation === "error" &&
                      "ring-2 ring-red-500"
                  )}
                  onClick={() => !operationStatus && handleImageClick(image)}
                  onMouseEnter={(e) => handleMouseEnter(image, e)}
                  onMouseLeave={handleMouseLeave}
                  onKeyDown={(e) => {
                    if (
                      (e.key === "Enter" || e.key === " ") &&
                      !operationStatus
                    ) {
                      e.preventDefault();
                      handleImageClick(image);
                    }
                  }}
                  tabIndex={0}
                  role="button"
                  aria-label={`Select image ${image.filename}, ${Math.round(
                    image.fileSize / 1024
                  )}KB, ${image.width} × ${image.height} pixels`}
                  aria-pressed={isSelected}
                >
                  <CardContent className="p-0">
                    {/* Image container */}
                    <div
                      className="relative overflow-hidden bg-muted"
                      style={{
                        height: gridSize,
                        width: "100%",
                        position: "relative",
                      }}
                    >
                      <Image
                        src={getImageUrl(image)}
                        alt={`${image.filename} - ${Math.round(
                          image.fileSize / 1024
                        )}KB image`}
                        fill
                        className={cn(
                          "object-cover transition-all duration-500 ease-in-out",
                          (isRefreshing ||
                            operationStatus?.operation === "rotating") &&
                            "opacity-60 scale-95"
                        )}
                        sizes={`${gridSize}px`}
                        loading="lazy"
                        placeholder="blur"
                        blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQEhIQFhAVEhIVEhIVEhIVEhIVEhIVEhIVEhISFhUVGhgYGhgYGhf/2wBDAQcHBwoIChMKChMYFhIWGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBgYGBj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkgEgUXB/xAAVAQEBAAAAAAAAAAAAAAAAAAAQAf/aAAwDAQACEQMRAD8A0NbvFovF6z2hcqrVCMYVoSK4pnG4KjN4CyqVdF9lLVu5IhGGQGTFFNJFmO6VkpNmD7z5lGnJr14sNZAQAAAAAA//2Q=="
                        key={`thumb-${
                          image.id
                        }-${image.lastModified.getTime()}`}
                        onLoad={() => {
                          // Image loaded successfully - could remove any loading state
                        }}
                        onError={(e) => {
                          console.warn(
                            `Failed to load thumbnail: ${image.filename}`
                          );
                          // The Next.js Image component handles fallbacks automatically
                        }}
                      />

                      {/* Loading overlay */}
                      {(isRefreshing || operationStatus) && (
                        <div className="absolute inset-0 bg-black/50 flex items-center justify-center transition-opacity duration-300">
                          <div className="text-center text-white">
                            {operationStatus?.operation === "rotating" && (
                              <>
                                <Loader2 className="h-6 w-6 animate-spin mx-auto mb-2" />
                                <div className="text-xs mb-1">
                                  {operationStatus.message}
                                </div>
                                {operationStatus.progress !== undefined && (
                                  <Progress
                                    value={operationStatus.progress}
                                    className="w-20 h-1 bg-primary/20"
                                  />
                                )}
                              </>
                            )}

                            {operationStatus?.operation === "completed" && (
                              <>
                                <CheckCircle2 className="h-6 w-6 mx-auto mb-1 text-green-400" />
                                <div className="text-xs">
                                  {operationStatus.message}
                                </div>
                              </>
                            )}

                            {operationStatus?.operation === "error" && (
                              <>
                                <AlertCircle className="h-6 w-6 mx-auto mb-1 text-red-400" />
                                <div className="text-xs">
                                  {operationStatus.message}
                                </div>
                              </>
                            )}

                            {isRefreshing && !operationStatus && (
                              <>
                                <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-1" />
                                <div className="text-xs">Refreshing...</div>
                              </>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Multi-select checkbox */}
                      {multiSelect && (
                        <div className="absolute top-2 left-2 z-20">
                          <Checkbox
                            checked={selectedIds.has(image.id)}
                            onCheckedChange={() =>
                              toggleImageSelection(image.id)
                            }
                            onClick={(e) => e.stopPropagation()}
                            className="bg-white/90 shadow-md h-4 w-4"
                            aria-label={`Select ${image.filename}`}
                          />
                        </div>
                      )}

                      {/* Cache indicator */}
                      {hasCache && !operationStatus && (
                        <div
                          className={cn(
                            "absolute z-10",
                            multiSelect ? "top-2 right-2" : "top-2 left-2"
                          )}
                        >
                          <div className="flex items-center justify-center w-6 h-5 bg-gradient-to-r from-blue-500 to-cyan-500 text-white text-xs font-bold rounded-full shadow-lg animate-pulse">
                            <Database size={10} />
                          </div>
                        </div>
                      )}

                      {/* Control overlay */}
                      <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                        <div className="flex gap-1">
                          <Button
                            size="sm"
                            variant="info"
                            className="h-8 w-8 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRotate(image.id, -90);
                            }}
                            disabled={!!operationStatus}
                            title="Rotate counter-clockwise"
                            aria-label={`Rotate ${image.filename} counter-clockwise`}
                          >
                            <RotateCcw className="h-5 w-5" aria-hidden="true" />
                          </Button>

                          <Button
                            size="sm"
                            variant="premium"
                            className="h-8 w-8 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRotate(image.id, 180);
                            }}
                            disabled={!!operationStatus}
                            title="Rotate 180 degrees"
                            aria-label={`Rotate ${image.filename} 180 degrees`}
                          >
                            <FlipVertical2
                              className="h-5 w-5"
                              aria-hidden="true"
                            />
                          </Button>

                          <Button
                            size="sm"
                            variant="success"
                            className="h-8 w-8 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRotate(image.id, 90);
                            }}
                            disabled={!!operationStatus}
                            title="Rotate clockwise"
                            aria-label={`Rotate ${image.filename} clockwise`}
                          >
                            <RotateCw className="h-5 w-5" aria-hidden="true" />
                          </Button>

                          {/* Cache clear button - only show if image has cache */}
                          {hasCache && (
                            <Button
                              size="sm"
                              variant="destructive"
                              className="h-8 w-8 p-0"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleClearCache(image.id);
                              }}
                              disabled={!!operationStatus}
                              title="Clear cached OCR results"
                              aria-label={`Clear cached OCR results for ${image.filename}`}
                            >
                              <X className="h-5 w-5" aria-hidden="true" />
                            </Button>
                          )}
                        </div>
                      </div>

                      {/* Status indicator */}
                      {isSelected && (
                        <div className="absolute top-2 right-2 z-20">
                          <Badge className="bg-primary text-primary-foreground shadow-md">
                            Selected
                          </Badge>
                        </div>
                      )}
                    </div>

                    {/* Image info */}
                    <div className="p-3">
                      <div className="space-y-1">
                        <p
                          className="text-sm font-medium truncate"
                          title={image.filename}
                        >
                          {image.filename}
                        </p>
                        <div className="flex items-center justify-between text-xs text-muted-foreground">
                          <span>{Math.round(image.fileSize / 1024)}KB</span>
                          <span>
                            {image.width} × {image.height}
                          </span>
                        </div>
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          <span>
                            {new Date(image.lastModified).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })
          : []}

        {/* No results message */}
        {(!Array.isArray(images) || images.length === 0) &&
          (!Array.isArray(filteredImages) || filteredImages.length === 0) &&
          Array.isArray(allImages) &&
          allImages.length > 0 && (
            <div className="col-span-full flex items-center justify-center p-8 text-muted-foreground">
              <div className="text-center">
                <Eye className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p className="mb-2">No images match the current filters</p>
                <p className="text-sm">
                  Try adjusting your filters or clearing them
                </p>
              </div>
            </div>
          )}
      </div>

      {/* Enhanced Hover Preview Overlay with 150% Zoom */}
      {hoveredImage && (
        <div
          className="fixed z-50 bg-background/95 backdrop-blur-sm border-2 border-primary/20 rounded-xl shadow-2xl p-4 animate-fade-in pointer-events-none"
          style={{
            left: `${previewPosition.x}px`,
            top: `${previewPosition.y}px`,
            width: "750px", // Increased width for 150% zoom
            maxHeight: "600px", // Increased height for 150% zoom
            opacity: 1,
            transform: "translateY(0) scale(1)",
            transition: "opacity 0.3s ease-out, transform 0.3s ease-out",
            boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)",
          }}
        >
          {(() => {
            const previewImage = Array.isArray(images)
              ? images.find((img) => img.id === hoveredImage)
              : null;
            if (!previewImage) return null;

            const hasCache = cacheStatus.get(previewImage.id) || false;

            return (
              <div className="space-y-4">
                {/* Header with enhanced styling */}
                <div className="flex items-center justify-between border-b border-border/50 pb-2">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-base truncate text-foreground">
                      {previewImage.filename}
                    </h3>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-sm text-muted-foreground">
                        {previewImage.width} × {previewImage.height}
                      </span>
                      <span className="text-sm text-muted-foreground">•</span>
                      <span className="text-sm text-muted-foreground">
                        {Math.round(previewImage.fileSize / 1024)}KB
                      </span>
                      {hasCache && (
                        <>
                          <span className="text-sm text-muted-foreground">•</span>
                          <Badge variant="secondary" className="text-xs px-2 py-0.5">
                            <Database className="h-3 w-3 mr-1" />
                            Cached
                          </Badge>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                {/* Enhanced Image Preview with 150% effective zoom */}
                <div
                  className="relative bg-muted/30 rounded-lg overflow-hidden border border-border/20"
                  style={{ height: "480px" }} // Increased height for better preview
                >
                  <Image
                    src={previewImage.previewUrl || previewImage.thumbnailUrl}
                    alt={`Enhanced preview of ${previewImage.filename}`}
                    fill
                    className="object-contain transition-transform duration-300"
                    style={{ 
                      transform: "scale(1.5)", // 150% zoom as requested
                      transformOrigin: "center center"
                    }}
                    sizes="750px"
                    priority
                  />
                  
                  {/* Subtle gradient overlay for better text readability */}
                  <div className="absolute inset-x-0 bottom-0 h-16 bg-gradient-to-t from-black/20 to-transparent pointer-events-none" />
                </div>

                {/* Enhanced Footer */}
                <div className="flex items-center justify-between text-sm pt-2 border-t border-border/50">
                  <div className="text-muted-foreground">
                    Modified: {new Date(previewImage.lastModified).toLocaleDateString()}
                  </div>
                  <div className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                    150% Zoom Preview
                  </div>
                </div>
              </div>
            );
          })()}
        </div>
      )}
    </div>
  );
}
