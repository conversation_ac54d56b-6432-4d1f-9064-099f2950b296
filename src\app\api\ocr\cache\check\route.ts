import { NextRequest, NextResponse } from "next/server";
import { getBackendUrl } from "@/utils/port-config";

export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.text();

    // Get the backend URL (await the Promise)
    const backendUrl = await getBackendUrl();

    // Forward the request to the backend
    const response = await fetch(`${backendUrl}/api/ocr/cache/check`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "User-Agent": request.headers.get("User-Agent") || "",
      },
      body: body,
    });

    // Get response data
    const data = await response.text();

    // Return response with same status and headers
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("API Proxy Error (ocr/cache/check):", error);
    return NextResponse.json(
      {
        success: false,
        error: "Proxy error: Failed to forward cache check request to backend",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
