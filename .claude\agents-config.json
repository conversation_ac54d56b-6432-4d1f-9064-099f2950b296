{"version": "1.3.1", "projectName": "DL Organizer", "agents": {"port-manager": {"description": "Windows infrastructure, bulletproof startup system, port management", "keywords": ["port", "win32", "launcher", "startup", "bulletproof", "windows", "infrastructure", "process"], "maxTools": 3, "priority": "high", "timeout": 20000}, "frontend-guardian": {"description": "Next.js 14.2+, React components, Tailwind CSS, TypeScript 5.8+", "keywords": ["next", "react", "typescript", "tailwind", "radix", "component", "frontend", "ui"], "maxTools": 3, "priority": "high", "timeout": 20000}, "api-specialist": {"description": "Express.js backend, SQLite database, SSE streaming, API integration", "keywords": ["express", "api", "sqlite", "database", "sse", "backend", "routing", "cors"], "maxTools": 3, "priority": "medium", "timeout": 20000}, "ocr-specialist": {"description": "Multi-provider OCR (OpenAI GPT-4o, OpenRouter), Smart Analyzer, ReadySearch v3.0", "keywords": ["ocr", "openai", "openrouter", "gpt-4o", "smart-analyzer", "readysearch", "ai", "model"], "maxTools": 3, "priority": "medium", "timeout": 20000}, "qa-specialist": {"description": "Jest unit tests, Playwright E2E, coverage analysis, performance validation", "keywords": ["jest", "playwright", "test", "e2e", "coverage", "quality", "supertest", "lint"], "maxTools": 3, "priority": "medium", "timeout": 20000}}, "performance": {"tokenLimit": 4000, "timeoutMinutes": 0.5, "preventHanging": true, "maxConcurrentTools": 1, "earlyExit": true, "progressChecks": 5, "streamlinedDocs": true, "fastMode": true, "batchToolCalls": false, "forceTimeout": true}, "errorHandling": {"maxRetries": 1, "gracefulTimeout": true, "fallbackToBasic": true, "cooldownSeconds": 1, "smartThrottling": true, "immediateExit": true}, "projectInfo": {"version": "1.3.1", "framework": "Next.js + Express.js", "primaryLanguages": ["TypeScript", "JavaScript"], "database": "SQLite", "testing": ["Jest", "Playwright"], "platform": "Windows"}}