import { NextRequest, NextResponse } from "next/server";
import { getBackendUrl } from "@/utils/port-config-server";

// Catch-all API route handler for any endpoints not explicitly handled
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return forwardRequest(request, "GET", params.slug);
}

export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return forwardRequest(request, "POST", params.slug);
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return forwardRequest(request, "PUT", params.slug);
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { slug: string[] } }
) {
  return forwardRequest(request, "DELETE", params.slug);
}

async function forwardRequest(
  request: NextRequest,
  method: string,
  slug: string[]
) {
  try {
    const BACKEND_URL = getBackendUrl();
    const path = slug.join("/");
    const url = `${BACKEND_URL}/api/${path}`;

    // Prepare request options
    const requestOptions: RequestInit = {
      method,
      headers: {
        "Content-Type":
          request.headers.get("Content-Type") || "application/json",
        "User-Agent": request.headers.get("User-Agent") || "",
      },
    };

    // Add body for POST/PUT requests
    if (method === "POST" || method === "PUT") {
      const contentType =
        request.headers.get("Content-Type") || "application/json";

      if (contentType.includes("application/json")) {
        // Parse JSON and re-stringify to ensure proper formatting
        try {
          const jsonData = await request.json();
          requestOptions.body = JSON.stringify(jsonData);
        } catch (error) {
          // Fallback to text if JSON parsing fails
          requestOptions.body = await request.text();
        }
      } else {
        requestOptions.body = await request.text();
      }
    }

    // Forward search parameters
    const searchParams = request.nextUrl.searchParams;
    const backendUrl = new URL(url);
    searchParams.forEach((value, key) => {
      backendUrl.searchParams.append(key, value);
    });

    // Forward the request to the backend
    const response = await fetch(backendUrl.toString(), requestOptions);

    // Get response data
    const data = await response.text();

    // Return response with same status and headers
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        "Content-Type":
          response.headers.get("Content-Type") || "application/json",
      },
    });
  } catch (error) {
    console.error(`API Proxy Error (${method} /${slug.join("/")})`, error);
    return NextResponse.json(
      {
        success: false,
        error: `Proxy error: Failed to forward ${method} request to backend`,
        details: error instanceof Error ? error.message : "Unknown error",
        path: `/${slug.join("/")}`,
      },
      { status: 500 }
    );
  }
}
