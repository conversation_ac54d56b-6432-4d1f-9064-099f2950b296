const Service = require('node-windows').Service;
const path = require('path');

// Create a new service object
const svc = new Service({
  name: 'DL Organizer',
  description: 'Driver License Organizer with OCR capabilities',
  script: path.join(__dirname, '..', 'backend', 'server.js'),
  nodeOptions: [
    '--max-old-space-size=2048'
  ],
  env: {
    name: 'NODE_ENV',
    value: 'production'
  },
  wait: 2,
  grow: 0.5
});

// Listen for the "install" event, which indicates the process is available as a service.
svc.on('install', () => {
  console.log('✅ DL Organizer service installed successfully');
  console.log('Starting service...');
  svc.start();
});

svc.on('start', () => {
  console.log('✅ DL Organizer service started');
  console.log('Service is now running in the background');
});

svc.on('error', (err) => {
  console.error('❌ Service error:', err);
});

// Check if running as administrator
function isAdmin() {
  try {
    require('child_process').execSync('net session', { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
}

if (!isAdmin()) {
  console.error('❌ This script must be run as Administrator');
  console.log('Right-click Command Prompt and select "Run as Administrator"');
  process.exit(1);
}

console.log('Installing DL Organizer as Windows Service...');
svc.install();