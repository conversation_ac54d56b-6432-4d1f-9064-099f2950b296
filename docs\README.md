# DL Organizer Documentation

This directory contains the comprehensive documentation for DL Organizer.

## 📁 Directory Structure

### `/development/`
Development guides and technical documentation for contributors.

- **`AGENTS.md`** - AI agent specifications and workflows for automated assistance

### `/technical/`
Technical architecture and API documentation.

- **`STYLING_GUIDELINES.md`** - UI/UX styling standards and design system guidelines

### `/troubleshooting/`
Troubleshooting guides and solutions for common issues.

- **`PORT-MANAGEMENT.md`** - Port configuration system and conflict resolution
- **`PORT-HUNTING-SOLUTION.md`** - Bulletproof startup system preventing port conflicts
- **`FINAL_FIX_SUMMARY.md`** - Complete system fixes and validation procedures

## 📚 Main Documentation

The primary documentation is located in the repository root:

- **`README.md`** - Main project overview, features, and quick start guide
- **`CLAUDE.md`** - Developer guidance for working with the codebase
- **`CHANGELOG.md`** - Version history and feature releases
- **`LICENSE`** - MIT license information

## 🛠️ For Developers

### Getting Started
1. Read the main `README.md` for project overview
2. Review `CLAUDE.md` for development workflow
3. Check `docs/troubleshooting/` for common issues
4. Follow `docs/development/AGENTS.md` for AI-assisted development

### Common Tasks
- **Port Issues**: See `docs/troubleshooting/PORT-HUNTING-SOLUTION.md`
- **Styling Changes**: Follow `docs/technical/STYLING_GUIDELINES.md`
- **System Issues**: Check `docs/troubleshooting/FINAL_FIX_SUMMARY.md`

### Architecture
DL Organizer uses a hybrid Next.js + Express.js architecture optimized for Windows. Key components:

- **Frontend**: Next.js 14+ with TypeScript, Tailwind CSS, Radix UI
- **Backend**: Express.js with SQLite database and Sharp image processing
- **AI Integration**: Multi-provider OCR with OpenAI GPT-4o and OpenRouter
- **Windows Integration**: Native filesystem APIs and advanced port management

## 🔍 Quick Reference

### Development Commands
```bash
npm run dev              # Start development (bulletproof startup)
npm run test             # Run tests
npm run build            # Production build
npm run ports:check      # Check port health
```

### Troubleshooting
- Port conflicts → Use `npm run dev` (never fails)
- OCR issues → Check API keys in Settings panel
- Performance → Enable caching and use proper pagination

### Support
- **Issues**: GitHub Issues for bug reports
- **Documentation**: Check relevant doc sections first
- **Development**: Follow CLAUDE.md guidance for code changes