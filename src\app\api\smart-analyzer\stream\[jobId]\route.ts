import { NextRequest } from "next/server";
import { getBackendPort } from "@/utils/port-config-server";

export async function GET(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  const { jobId } = params;
  const backendPort = getBackendPort();

  console.log(
    `🔄 Smart Analyzer SSE: Proxying stream request for job ${jobId} to backend port ${backendPort}`
  );

  try {
    // Create a fetch stream to the backend
    const backendResponse = await fetch(
      `http://localhost:${backendPort}/api/smart-analyzer/stream/${jobId}`,
      {
        method: "GET",
        headers: {
          Accept: "text/event-stream",
          "Cache-Control": "no-cache",
        },
      }
    );

    if (!backendResponse.ok) {
      console.error(
        `❌ Smart Analyzer SSE: Backend error ${backendResponse.status}`
      );
      return new Response(
        JSON.stringify({ error: "Backend stream connection failed" }),
        {
          status: backendResponse.status,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // Create a readable stream for the SSE response
    const stream = new ReadableStream({
      start(controller) {
        console.log(`✅ Smart Analyzer SSE: Starting stream for job ${jobId}`);

        // Handle the backend response stream
        const reader = backendResponse.body?.getReader();
        if (!reader) {
          console.error(
            `❌ Smart Analyzer SSE: No reader available for job ${jobId}`
          );
          controller.close();
          return;
        }

        const pump = () => {
          reader
            .read()
            .then(({ done, value }) => {
              if (done) {
                console.log(
                  `🏁 Smart Analyzer SSE: Stream ended for job ${jobId}`
                );
                controller.close();
                return;
              }

              // Log the data being forwarded
              const chunk = new TextDecoder().decode(value);
              console.log(
                `📡 Smart Analyzer SSE: Forwarding chunk for job ${jobId}:`,
                chunk
              );

              // Forward the chunk to the client
              controller.enqueue(value);
              pump();
            })
            .catch((error) => {
              console.error(
                `❌ Smart Analyzer SSE: Stream error for job ${jobId}:`,
                error
              );
              controller.error(error);
            });
        };

        pump();
      },

      cancel() {
        console.log(`⏹️ Smart Analyzer SSE: Stream cancelled for job ${jobId}`);
      },
    });

    // Return the SSE response with proper headers
    return new Response(stream, {
      status: 200,
      headers: {
        "Content-Type": "text/event-stream",
        Connection: "keep-alive",
        "Cache-Control": "no-cache",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "Cache-Control",
      },
    });
  } catch (error) {
    console.error(
      `❌ Smart Analyzer SSE: Proxy error for job ${jobId}:`,
      error
    );
    return new Response(
      JSON.stringify({
        error: "Failed to establish SSE connection",
        details: error instanceof Error ? error.message : String(error),
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}
