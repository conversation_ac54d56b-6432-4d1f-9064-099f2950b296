"use client";

import React, { useState, useCallback } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Filter } from '@/types/filters';
import { AnalyzerManifest } from '@/hooks/useSmartAnalyzer';
import { <PERSON>, <PERSON>Pointer, Sparkles, ChevronDown, Search, Wand2 } from 'lucide-react';

interface SmartChip {
  id: string;
  label: string;
  type: 'side' | 'size' | 'filename' | 'resolution';
  predicate: Filter;
  count: number;
}

interface SmartSelection {
  id: string;
  label: string;
  description: string;
  filters: Filter[];
  count?: number;
}

interface SmartChipsProps {
  manifest: AnalyzerManifest | null;
  onAddFilter: (filter: Filter) => void;
  onSelectMatchingImages?: (filter: Filter) => void;
  className?: string;
}

const SmartChips: React.FC<SmartChipsProps> = ({ 
  manifest, 
  onAddFilter, 
  onSelectMatchingImages,
  className = "" 
}) => {
  const [showSmartSelections, setShowSmartSelections] = useState(false);
  const [smartQuery, setSmartQuery] = useState('');
  const [processingQuery, setProcessingQuery] = useState(false);

  // Process natural language query for smart selection
  const processSmartQuery = useCallback(async (query: string) => {
    if (!query.trim() || !onSelectMatchingImages || !manifest) return;
    
    setProcessingQuery(true);
    
    try {
      // Parse common selection patterns
      const lowerQuery = query.toLowerCase();
      
      // Pattern: "select all front" or "front images" or "front licenses"
      if (lowerQuery.includes('front') && manifest.sideCounts.front > 0) {
        onSelectMatchingImages({ kind: 'side', value: 'front' });
        return;
      }
      
      // Pattern: "select all back" or "back images" or "back licenses"
      if (lowerQuery.includes('back') && manifest.sideCounts.back > 0) {
        onSelectMatchingImages({ kind: 'side', value: 'back' });
        return;
      }
      
      // Pattern: "select selfies" or "selfie images"
      if (lowerQuery.includes('selfie') && manifest.sideCounts.selfie > 0) {
        onSelectMatchingImages({ kind: 'side', value: 'selfie' });
        return;
      }
      
      // Pattern: "large files" or "big images" or "MB"
      if ((lowerQuery.includes('large') || lowerQuery.includes('big') || lowerQuery.includes('mb')) && manifest.sizeBuckets.length > 0) {
        const largeBucket = manifest.sizeBuckets.find(b => 
          b.label.includes('MB') && b.count > 0
        );
        if (largeBucket) {
          onSelectMatchingImages({ kind: 'size-range', range: largeBucket.label });
          return;
        }
      }
      
      // Pattern: "small files" or "small images" or "KB"
      if ((lowerQuery.includes('small') || lowerQuery.includes('tiny') || lowerQuery.includes('kb')) && manifest.sizeBuckets.length > 0) {
        const smallBucket = manifest.sizeBuckets.find(b => 
          b.label.includes('KB') && b.count > 0
        );
        if (smallBucket) {
          onSelectMatchingImages({ kind: 'size-range', range: smallBucket.label });
          return;
        }
      }
      
      // Pattern: "high quality" or "high resolution" or "high res"
      if ((lowerQuery.includes('high quality') || lowerQuery.includes('high res') || lowerQuery.includes('high resolution')) && manifest.resBuckets.length > 0) {
        const highResBucket = manifest.resBuckets.find(b => 
          (b.label.includes('8-12MP') || b.label.includes('> 12MP')) && b.count > 0
        );
        if (highResBucket) {
          onSelectMatchingImages({ kind: 'resolution-range', range: highResBucket.label });
          return;
        }
      }
      
      // Pattern: "unprocessed" or "not analyzed" or "no cache"
      if ((lowerQuery.includes('unprocessed') || lowerQuery.includes('not analyzed') || lowerQuery.includes('no cache')) && manifest.cachedPct < 100) {
        onSelectMatchingImages({ kind: 'cached', value: false });
        return;
      }
      
      // Pattern: "processed" or "analyzed" or "cached"
      if ((lowerQuery.includes('processed') || lowerQuery.includes('analyzed') || lowerQuery.includes('cached')) && manifest.cachedPct > 0) {
        onSelectMatchingImages({ kind: 'cached', value: true });
        return;
      }
      
      // Pattern: filename contains - look for quoted text or specific tokens
      const quotedMatch = query.match(/["'](.*?)["']/);
      if (quotedMatch) {
        const token = quotedMatch[1];
        onSelectMatchingImages({ kind: 'filename-contains', text: token });
        return;
      }
      
      // Pattern: specific filename tokens that exist in the manifest
      const tokenMatch = manifest.filenameClusters.find(cluster => 
        lowerQuery.includes(cluster.token.toLowerCase())
      );
      if (tokenMatch) {
        onSelectMatchingImages({ kind: 'filename-contains', text: tokenMatch.token });
        return;
      }
      
      // If no pattern matched, show a helpful message
      console.log('No matching pattern found for query:', query);
      
    } finally {
      setProcessingQuery(false);
    }
  }, [manifest, onSelectMatchingImages]);
  
  const handleSmartQuerySubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault();
    if (smartQuery.trim()) {
      processSmartQuery(smartQuery);
    }
  }, [smartQuery, processSmartQuery]);

  if (!manifest) {
    return null;
  }

  // Generate smart chips from manifest
  const generateChips = (): SmartChip[] => {
    const chips: SmartChip[] = [];

    // Side-based chips
    Object.entries(manifest.sideCounts).forEach(([side, count]) => {
      if (count > 0) {
        chips.push({
          id: `side-${side}`,
          label: `${count} ${side}${count === 1 ? '' : 's'}`,
          type: 'side',
          predicate: { kind: 'side', value: side as 'front' | 'back' | 'selfie' | 'unknown' },
          count
        });
      }
    });

    // Size-based chips
    manifest.sizeBuckets.forEach((bucket, index) => {
      if (bucket.count > 0) {
        chips.push({
          id: `size-${index}`,
          label: `${bucket.label} (${bucket.count})`,
          type: 'size',
          predicate: { kind: 'size-range', range: bucket.label },
          count: bucket.count
        });
      }
    });

    // Filename token chips (only show tokens with 3+ occurrences to reduce noise)
    manifest.filenameClusters
      .filter(cluster => cluster.count >= 3)
      .slice(0, 8) // Limit to top 8
      .forEach((cluster, index) => {
        chips.push({
          id: `token-${cluster.token}`,
          label: `token:${cluster.token} (${cluster.count})`,
          type: 'filename',
          predicate: { kind: 'filename-contains', text: cluster.token },
          count: cluster.count
        });
      });

    // Resolution chips
    manifest.resBuckets.forEach((bucket, index) => {
      if (bucket.count > 0) {
        chips.push({
          id: `res-${index}`,
          label: `${bucket.label} (${bucket.count})`,
          type: 'resolution',
          predicate: { kind: 'resolution-range', range: bucket.label },
          count: bucket.count
        });
      }
    });

    return chips.sort((a, b) => b.count - a.count); // Sort by count descending
  };

  // Generate smart selections based on common patterns
  const generateSmartSelections = (): SmartSelection[] => {
    const selections: SmartSelection[] = [];
    
    // Smart selection: "Front DLs only" (if we have both front and back)
    if (manifest.sideCounts.front > 0 && manifest.sideCounts.back > 0) {
      selections.push({
        id: 'front-only',
        label: 'Front DLs Only',
        description: `Select all ${manifest.sideCounts.front} front-facing driver licenses`,
        filters: [{ kind: 'side', value: 'front' }]
      });
    }
    
    // Smart selection: "Large front DLs" (front + larger file size)
    if (manifest.sideCounts.front > 1 && manifest.sizeBuckets.some(b => b.label.includes('MB'))) {
      const largeSizeBucket = manifest.sizeBuckets.find(b => 
        (b.label.includes('1MB') || b.label.includes('2MB') || b.label.includes('5MB') || b.label.includes('> 5MB')) && b.count > 0
      );
      if (largeSizeBucket) {
        selections.push({
          id: 'large-fronts',
          label: 'Large Front DLs',
          description: `Select front licenses with ${largeSizeBucket.label} file size`,
          filters: [
            { kind: 'side', value: 'front' },
            { kind: 'size-range', range: largeSizeBucket.label }
          ]
        });
      }
    }
    
    // Smart selection: "High-res documents" (high resolution + front or back)
    const highResBucket = manifest.resBuckets.find(b => 
      (b.label.includes('> 12MP') || b.label.includes('8-12MP')) && b.count > 0
    );
    if (highResBucket && (manifest.sideCounts.front > 0 || manifest.sideCounts.back > 0)) {
      selections.push({
        id: 'high-res-docs',
        label: 'High-Resolution Documents',
        description: `Select ${highResBucket.label} license images`,
        filters: [{ kind: 'resolution-range', range: highResBucket.label }]
      });
    }
    
    // Smart selection: "Processed vs Unprocessed" (if we have cache data)
    if (manifest.cachedPct > 0 && manifest.cachedPct < 100) {
      selections.push({
        id: 'unprocessed',
        label: 'Unprocessed Images',
        description: `Select images that haven't been analyzed yet`,
        filters: [{ kind: 'cached', value: false }]
      });
    }
    
    // Smart selection: "Specific filename pattern + type" (common tokens + side)
    const topToken = manifest.filenameClusters.find(c => c.count >= 3);
    if (topToken && manifest.sideCounts.front > 0) {
      selections.push({
        id: 'pattern-fronts',
        label: `"${topToken.token}" Front DLs`,
        description: `Select front licenses with "${topToken.token}" in filename (${topToken.count} found)`,
        filters: [
          { kind: 'side', value: 'front' },
          { kind: 'filename-contains', text: topToken.token }
        ]
      });
    }
    
    return selections;
  };

  const chips = generateChips();
  const smartSelections = generateSmartSelections();

  if (chips.length === 0) {
    return (
      <div className={`text-sm text-muted-foreground py-2 ${className}`}>
        No patterns found in this folder
      </div>
    );
  }

  const getChipVariant = (type: string) => {
    switch (type) {
      case 'side': return 'default';
      case 'size': return 'secondary';
      case 'filename': return 'outline';
      case 'resolution': return 'secondary';
      default: return 'outline';
    }
  };

  const getChipColor = (type: string) => {
    switch (type) {
      case 'side': return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'size': return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'filename': return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      case 'resolution': return 'bg-orange-100 text-orange-800 hover:bg-orange-200';
      default: return '';
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h4 className="text-xs font-medium text-foreground">Smart Chips</h4>
          <Badge variant="outline" className="text-xs h-5 px-1.5">
            {manifest.totalImages} analyzed
          </Badge>
          {manifest.cachedPct > 0 && (
            <Badge variant="success" className="text-xs h-5 px-1.5">
              {manifest.cachedPct}% cached
            </Badge>
          )}
        </div>
        <div className="text-xs text-muted-foreground">
          {new Date(manifest.generatedAt).toLocaleTimeString()}
        </div>
      </div>

      {/* Chips Grid */}
      <div className="flex flex-wrap gap-1 items-center">
        {chips.map((chip) => (
          <Badge
            key={chip.id}
            variant={getChipVariant(chip.type)}
            className={`
              h-6 px-2 text-xs cursor-pointer select-none transition-all duration-200 
              hover:scale-105 active:scale-95
              ${getChipColor(chip.type)}
              border border-transparent hover:border-current/30
            `}
            onClick={() => onAddFilter(chip.predicate)}
            title={`Click to add "${chip.label}" filter`}
          >
            <MousePointer className="w-3 h-3 mr-1 opacity-60" />
            {chip.label}
          </Badge>
        ))}
      </div>

      {/* Smart Query Input */}
      {onSelectMatchingImages && (
        <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-2 border border-indigo-200">
          <div className="flex items-center gap-2 mb-1.5">
            <Wand2 className="w-3 h-3 text-indigo-600" />
            <h5 className="text-xs font-medium text-indigo-900">Smart Selection</h5>
            <Badge variant="outline" className="text-xs h-4 px-1 bg-indigo-100 text-indigo-700">
              NL
            </Badge>
          </div>
          
          <form onSubmit={handleSmartQuerySubmit} className="flex gap-1.5">
            <Input
              placeholder='Try "front images", "large files", "unprocessed"...'
              value={smartQuery}
              onChange={(e) => setSmartQuery(e.target.value)}
              className="flex-1 text-xs h-7 bg-white border-indigo-200 focus:border-indigo-400"
              disabled={processingQuery}
            />
            <Button
              type="submit"
              size="sm"
              variant="outline"
              className="h-7 w-7 p-0 bg-indigo-50 hover:bg-indigo-100 border-indigo-200 text-indigo-700"
              disabled={processingQuery || !smartQuery.trim()}
            >
              {processingQuery ? (
                <div className="w-3 h-3 border-2 border-indigo-600 border-t-transparent rounded-full animate-spin" />
              ) : (
                <Search className="w-3 h-3" />
              )}
            </Button>
          </form>
          
          <div className="mt-1 text-xs text-indigo-700">
            💡 &ldquo;front licenses&rdquo;, &ldquo;large images&rdquo;, &ldquo;unprocessed&rdquo;
          </div>
        </div>
      )}

      {/* Actions */}
      <div className="flex items-center justify-between pt-0.5">
        <div className="flex items-center gap-2">
          <span className="text-xs text-muted-foreground">
            {chips.length} patterns
          </span>
          {manifest.filenameClusters.length > 8 && (
            <span className="text-xs text-muted-foreground">
              (+{manifest.filenameClusters.length - 8} more)
            </span>
          )}
        </div>
        
        <div className="flex items-center gap-1">
          {onSelectMatchingImages && (
            <Button
              size="sm"
              variant="outline"
              className="text-xs h-6 px-2"
              onClick={() => {
                onSelectMatchingImages({ kind: 'select-visible' });
              }}
            >
              Select Visible
            </Button>
          )}
          
          {smartSelections.length > 0 && onSelectMatchingImages && (
            <Button
              size="sm"
              variant="ghost"
              className="text-xs h-6 px-1"
              onClick={() => setShowSmartSelections(!showSmartSelections)}
            >
              <Sparkles className="w-3 h-3 mr-1" />
              Advanced
              <ChevronDown className={`w-3 h-3 ml-1 transition-transform ${showSmartSelections ? 'rotate-180' : ''}`} />
            </Button>
          )}
        </div>
      </div>

      {/* Smart Selections Panel */}
      {showSmartSelections && smartSelections.length > 0 && (
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-3 border border-blue-200">
          <div className="flex items-center space-x-2 mb-3">
            <Sparkles className="w-4 h-4 text-blue-600" />
            <h5 className="text-sm font-medium text-blue-900">Smart Selections</h5>
            <Badge variant="outline" className="text-xs bg-blue-100 text-blue-700">
              {smartSelections.length} available
            </Badge>
          </div>
          
          <div className="space-y-2">
            {smartSelections.map((selection) => (
              <div 
                key={selection.id}
                className="flex items-center justify-between p-2 bg-white rounded border hover:border-blue-300 transition-colors"
              >
                <div className="flex-1">
                  <div className="font-medium text-sm text-gray-900">{selection.label}</div>
                  <div className="text-xs text-gray-600">{selection.description}</div>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  className="text-xs h-7 px-3 bg-blue-50 hover:bg-blue-100 border-blue-200"
                  onClick={() => {
                    // Apply all filters in the selection as a combined selection
                    if (onSelectMatchingImages) {
                      // For now, we'll apply the first filter as a demonstration
                      // In a full implementation, this would need to handle multiple filters
                      selection.filters.forEach(filter => {
                        onAddFilter(filter);
                      });
                      
                      // Then select matching images based on all filters
                      onSelectMatchingImages(selection.filters[0]); // Simplified for demo
                    }
                  }}
                >
                  Select
                </Button>
              </div>
            ))}
          </div>
          
          <div className="mt-3 text-xs text-blue-700 bg-blue-100 rounded p-2">
            💡 Smart selections combine multiple criteria to find exactly what you need
          </div>
        </div>
      )}

      {/* Legend */}
      <div className="flex items-center space-x-4 text-xs text-muted-foreground">
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 rounded-full bg-blue-400"></div>
          <span>License Sides</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 rounded-full bg-green-400"></div>
          <span>File Sizes</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 rounded-full bg-purple-400"></div>
          <span>Filename Patterns</span>
        </div>
        <div className="flex items-center space-x-1">
          <div className="w-2 h-2 rounded-full bg-orange-400"></div>
          <span>Resolutions</span>
        </div>
      </div>
    </div>
  );
};

export default SmartChips;