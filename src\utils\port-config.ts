/**
 * Port Configuration Utility for Frontend API Routes
 *
 * This utility provides dynamic port resolution for frontend API routes,
 * ensuring they always connect to the correct backend port as determined
 * by the unified port synchronization system.
 */

interface PortConfig {
  ports: {
    frontend: number;
    backend: number;
    ngrok: number;
  };
  environment: {
    FRONTEND_PORT: string;
    BACKEND_PORT: string;
    NGROK_PORT: string;
    PORT: string;
  };
  timestamp: string;
  processInfo?: any;
}

/**
 * Dynamically fetch the current port configuration from the running system
 * @returns {Promise<PortConfig | null>} Current port configuration
 */
async function fetchPortConfig(): Promise<PortConfig | null> {
  try {
    // First try to fetch from the port configuration API endpoint
    const response = await fetch('/api/port-config', {
      method: 'GET',
      signal: AbortSignal.timeout(2000),
    });

    if (response.ok) {
      const config = await response.json();
      console.log('📡 Port config loaded from API:', config);
      return config;
    }
  } catch (error) {
    console.debug('🔧 Port config API not available, using fallback');
  }

  // Fallback: Try to load from the data file location via Next.js API
  try {
    const dataResponse = await fetch('/data/port-config.json', {
      method: 'GET',
      signal: AbortSignal.timeout(1000),
    });

    if (dataResponse.ok) {
      const config = await dataResponse.json();
      console.log('📡 Port config loaded from data file:', config);
      return config;
    }
  } catch (error) {
    console.debug('🔧 Port config data file not accessible');
  }

  return null;
}

/**
 * Get the current backend port from the configuration system
 * @returns {Promise<number>} Backend port number
 */
export async function getBackendPort(): Promise<number> {
  try {
    const config = await fetchPortConfig();
    if (config && config.ports && config.ports.backend) {
      console.log('✅ Backend port from config:', config.ports.backend);
      return config.ports.backend;
    }
  } catch (error) {
    console.debug('🔧 Dynamic port resolution failed, using fallback');
  }

  // Static fallback based on current port-config.json
  console.log('📡 Using static backend port fallback: 3574');
  return 3574;
}

/**
 * Get the current frontend port from the configuration system
 * @returns {Promise<number>} Frontend port number
 */
export async function getFrontendPort(): Promise<number> {
  try {
    const config = await fetchPortConfig();
    if (config && config.ports && config.ports.frontend) {
      return config.ports.frontend;
    }
  } catch (error) {
    console.debug('🔧 Dynamic port resolution failed, using fallback');
  }

  // Static fallback based on current port-config.json
  return 3110;
}

/**
 * Get the complete backend URL for API calls
 * @returns {Promise<string>} Complete backend URL
 */
export async function getBackendUrl(): Promise<string> {
  const port = await getBackendPort();
  const url = `http://127.0.0.1:${port}`;
  console.log('🌐 Backend URL resolved:', url);
  return url;
}

/**
 * Get the complete port configuration
 * @returns {Promise<PortConfig | null>} Complete port configuration or null if not available
 */
export async function getPortConfig(): Promise<PortConfig | null> {
  try {
    const config = await fetchPortConfig();
    if (config) {
      return config;
    }
  } catch (error) {
    console.debug('🔧 Port config resolution failed');
  }

  // Static fallback configuration
  return {
    ports: {
      frontend: 3110,
      backend: 3574,
      ngrok: 4040,
    },
    environment: {
      FRONTEND_PORT: "3110",
      BACKEND_PORT: "3574",
      NGROK_PORT: "4040",
      PORT: "3110",
    },
    timestamp: new Date().toISOString(),
    processInfo: {
      source: "static-fallback",
    },
  };
}

/**
 * Validate that the current port configuration is still valid
 * @returns {Promise<boolean>} True if configuration is valid
 */
export async function validatePortConfig(): Promise<boolean> {
  try {
    // Try to connect to the backend to verify it's actually running
    const backendUrl = await getBackendUrl();
    console.log('🔍 Validating backend at:', backendUrl);
    
    const response = await fetch(`${backendUrl}/api/health`, {
      method: "GET",
      signal: AbortSignal.timeout(3000),
    });

    // Backend should respond with OK status
    const isValid = response.ok;
    console.log(isValid ? '✅ Port configuration is valid' : '❌ Port configuration is invalid');
    return isValid;
  } catch (error) {
    console.warn("⚠️ Port configuration validation failed:", error);
    return false;
  }
}

/**
 * Log port configuration for debugging
 */
export async function logPortConfig(): Promise<void> {
  const config = await getPortConfig();
  console.log("🔍 Current Port Configuration:");
  console.log(`  Frontend: ${config?.ports.frontend || 'unknown'}`);
  console.log(`  Backend: ${config?.ports.backend || 'unknown'}`);
  console.log(`  Backend URL: ${await getBackendUrl()}`);
  console.log(`  Configuration source: ${config?.processInfo?.source || 'unknown'}`);
  console.log(`  Timestamp: ${config?.timestamp || 'unknown'}`);
}

/**
 * Test connectivity to backend and return detailed status
 * @returns {Promise<{connected: boolean, port: number, url: string, responseTime: number}>}
 */
export async function testBackendConnectivity() {
  const startTime = Date.now();
  const port = await getBackendPort();
  const url = await getBackendUrl();
  
  try {
    const response = await fetch(`${url}/api/health`, {
      method: "GET",
      signal: AbortSignal.timeout(5000),
    });
    
    const responseTime = Date.now() - startTime;
    const connected = response.ok;
    
    if (connected) {
      console.log(`✅ Backend connectivity test passed (${responseTime}ms)`);
    } else {
      console.warn(`❌ Backend connectivity test failed: ${response.status} ${response.statusText}`);
    }
    
    return {
      connected,
      port,
      url,
      responseTime,
      status: response.status,
      statusText: response.statusText
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    console.error(`❌ Backend connectivity test failed (${responseTime}ms):`, error);
    
    return {
      connected: false,
      port,
      url,
      responseTime,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}