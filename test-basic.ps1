# Test basic backend functionality
$body = @{
    folderPath = "C:\claude\dl-organizer\test-samples\i18"
} | ConvertTo-Json

Write-Host "Testing basic backend functionality with body: $body"

try {
    $response = Invoke-RestMethod -Uri "http://127.0.0.1:3574/api/filesystem/test-basic" -Method POST -Body $body -ContentType "application/json"
    Write-Host "Basic API Response:"
    $response | ConvertTo-Json -Depth 5
} catch {
    Write-Host "Basic API Error: $_"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody"
    }
}