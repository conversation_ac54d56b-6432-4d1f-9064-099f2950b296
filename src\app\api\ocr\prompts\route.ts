import { NextRequest, NextResponse } from "next/server";
import { getBackendUrl } from "@/utils/port-config";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  // Get the backend URL (await the Promise)
  const backendUrl = await getBackendUrl();

  try {
    console.log(
      "OCR Prompts API Route: Forwarding request to backend",
      `${backendUrl}/api/ocr/prompts`
    );

    // Forward the request to the backend
    const response = await fetch(`${backendUrl}/api/ocr/prompts`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "User-Agent": request.headers.get("User-Agent") || "",
      },
    });

    // Get response data
    const data = await response.text();

    console.log(
      "OCR Prompts API Route: Backend response status:",
      response.status
    );

    // Return response with same status and headers
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("API Proxy Error (ocr/prompts):", error);
    return NextResponse.json(
      {
        success: false,
        error: "Proxy error: Failed to forward OCR prompts request to backend",
        details: error instanceof Error ? error.message : "Unknown error",
        backendUrl: backendUrl,
      },
      { status: 500 }
    );
  }
}
