const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs').promises;

// Connection Pool Implementation
class ConnectionPool {
  constructor(dbPath, maxConnections = 10) {
    this.dbPath = dbPath;
    this.maxConnections = maxConnections;
    this.pool = [];
    this.activeConnections = 0;
    this.waitingQueue = [];
  }

  async getConnection() {
    if (this.pool.length > 0) {
      return this.pool.pop();
    }

    if (this.activeConnections < this.maxConnections) {
      const connection = await this.createConnection();
      this.activeConnections++;
      return connection;
    }

    // Wait for available connection
    return new Promise((resolve) => {
      this.waitingQueue.push(resolve);
    });
  }

  async createConnection() {
    return new Promise((resolve, reject) => {
      const db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('Error creating pooled connection:', err);
          reject(err);
        } else {
          // Configure connection for performance
          db.run('PRAGMA journal_mode = WAL');
          db.run('PRAGMA synchronous = NORMAL');
          db.run('PRAGMA cache_size = 10000');
          db.run('PRAGMA temp_store = memory');
          db.run('PRAGMA mmap_size = 268435456'); // 256MB
          resolve(db);
        }
      });
    });
  }

  releaseConnection(connection) {
    if (this.waitingQueue.length > 0) {
      const waitingResolve = this.waitingQueue.shift();
      waitingResolve(connection);
    } else {
      this.pool.push(connection);
    }
  }

  async closeAll() {
    const allConnections = [...this.pool];
    this.pool = [];
    
    for (const connection of allConnections) {
      await new Promise((resolve) => {
        connection.close(() => resolve());
      });
    }
    this.activeConnections = 0;
  }
}

// Query Result Cache Implementation
class QueryCache {
  constructor(maxSize = 1000, ttlSeconds = 300) {
    this.cache = new Map();
    this.maxSize = maxSize;
    this.ttl = ttlSeconds * 1000;
    this.stats = { hits: 0, misses: 0, sets: 0 };
  }

  generateKey(sql, params) {
    return `${sql}:${JSON.stringify(params)}`;
  }

  get(sql, params) {
    const key = this.generateKey(sql, params);
    const cached = this.cache.get(key);
    
    if (!cached) {
      this.stats.misses++;
      return null;
    }

    if (Date.now() - cached.timestamp > this.ttl) {
      this.cache.delete(key);
      this.stats.misses++;
      return null;
    }

    this.stats.hits++;
    return cached.data;
  }

  set(sql, params, data) {
    if (this.cache.size >= this.maxSize) {
      // Remove oldest entries (simple LRU)
      const oldestKey = this.cache.keys().next().value;
      this.cache.delete(oldestKey);
    }

    const key = this.generateKey(sql, params);
    this.cache.set(key, {
      data: JSON.parse(JSON.stringify(data)), // Deep copy
      timestamp: Date.now()
    });
    this.stats.sets++;
  }

  invalidate(pattern) {
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.includes(pattern)) {
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  clear() {
    this.cache.clear();
    this.stats = { hits: 0, misses: 0, sets: 0 };
  }

  getStats() {
    const total = this.stats.hits + this.stats.misses;
    return {
      ...this.stats,
      hitRate: total > 0 ? (this.stats.hits / total * 100).toFixed(2) + '%' : '0%',
      size: this.cache.size
    };
  }
}

class DatabaseManager {
  constructor(dbPath = null) {
    this.dbPath = dbPath || path.join(__dirname, '../../data/dl-organizer.db');
    this.db = null;
    this.isInitialized = false;
    this.connectionPool = null;
    this.queryCache = new QueryCache(1000, 300); // 1000 entries, 5min TTL
    this.folderCache = new QueryCache(500, 180); // Dedicated folder cache, 3min TTL
  }
  
  async initialize() {
    if (this.isInitialized) return;
    
    try {
      // Ensure data directory exists
      const dataDir = path.dirname(this.dbPath);
      await fs.mkdir(dataDir, { recursive: true });
      
      // Initialize connection pool
      this.connectionPool = new ConnectionPool(this.dbPath, 10);
      
      // Open main database connection for schema operations
      this.db = new sqlite3.Database(this.dbPath, (err) => {
        if (err) {
          console.error('Error opening database:', err);
          throw err;
        }
        console.log(`Connected to SQLite database: ${this.dbPath}`);
      });
      
      // Configure main connection for performance
      await this.run('PRAGMA foreign_keys = ON');
      await this.run('PRAGMA journal_mode = WAL');
      await this.run('PRAGMA synchronous = NORMAL');
      await this.run('PRAGMA cache_size = 10000');
      await this.run('PRAGMA temp_store = memory');
      await this.run('PRAGMA mmap_size = 268435456'); // 256MB
      
      // Create tables
      await this.createTables();
      
      // Create indexes for performance
      await this.createIndexes();
      
      this.isInitialized = true;
      console.log('Database initialized successfully with connection pooling and caching');
    } catch (error) {
      console.error('Failed to initialize database:', error);
      throw error;
    }
  }
  
  async createTables() {
    const tables = [
      // Projects table
      `CREATE TABLE IF NOT EXISTS projects (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        root_path TEXT NOT NULL,
        settings TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // Folders table
      `CREATE TABLE IF NOT EXISTS folders (
        id TEXT PRIMARY KEY,
        project_id TEXT NOT NULL,
        name TEXT NOT NULL,
        path TEXT NOT NULL UNIQUE,
        parent_id TEXT,
        relative_path TEXT,
        image_count INTEGER DEFAULT 0,
        has_text_file BOOLEAN DEFAULT FALSE,
        tags TEXT DEFAULT '[]',
        metadata TEXT DEFAULT '{}',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
        FOREIGN KEY (parent_id) REFERENCES folders(id) ON DELETE SET NULL
      )`,
      
      // Images table
      `CREATE TABLE IF NOT EXISTS images (
        id TEXT PRIMARY KEY,
        folder_id TEXT NOT NULL,
        filename TEXT NOT NULL,
        path TEXT NOT NULL UNIQUE,
        relative_path TEXT,
        file_size INTEGER,
        width INTEGER,
        height INTEGER,
        format TEXT,
        rotation INTEGER DEFAULT 0,
        thumbnail_path TEXT,
        preview_path TEXT,
        metadata TEXT DEFAULT '{}',
        last_modified DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (folder_id) REFERENCES folders(id) ON DELETE CASCADE
      )`,
      
      // OCR results table
      `CREATE TABLE IF NOT EXISTS ocr_results (
        id TEXT PRIMARY KEY,
        image_id TEXT NOT NULL,
        folder_id TEXT NOT NULL,
        first_name TEXT,
        last_name TEXT,
        date_of_birth TEXT,
        address TEXT,
        license_number TEXT,
        expiration_date TEXT,
        issue_date TEXT,
        state TEXT,
        confidence REAL DEFAULT 0,
        raw_text TEXT,
        model_used TEXT,
        processing_time INTEGER,
        cost REAL DEFAULT 0,
        metadata TEXT DEFAULT '{}',
        processed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE,
        FOREIGN KEY (folder_id) REFERENCES folders(id) ON DELETE CASCADE
      )`,
      
      // Processing jobs table (for batch operations)
      `CREATE TABLE IF NOT EXISTS processing_jobs (
        id TEXT PRIMARY KEY,
        project_id TEXT NOT NULL,
        type TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        progress INTEGER DEFAULT 0,
        total_items INTEGER DEFAULT 0,
        processed_items INTEGER DEFAULT 0,
        failed_items INTEGER DEFAULT 0,
        settings TEXT DEFAULT '{}',
        results TEXT DEFAULT '[]',
        errors TEXT DEFAULT '[]',
        started_at DATETIME,
        completed_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
      )`,
      
      // Settings table for application configuration
      `CREATE TABLE IF NOT EXISTS app_settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        type TEXT DEFAULT 'string',
        description TEXT,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      
      // Cost tracking table
      `CREATE TABLE IF NOT EXISTS cost_tracking (
        id TEXT PRIMARY KEY,
        model_id TEXT NOT NULL,
        provider_id TEXT NOT NULL,
        operation_type TEXT NOT NULL,
        tokens_used INTEGER DEFAULT 0,
        cost REAL NOT NULL,
        image_id TEXT,
        ocr_result_id TEXT,
        processing_time INTEGER,
        success BOOLEAN DEFAULT TRUE,
        error_message TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE SET NULL,
        FOREIGN KEY (ocr_result_id) REFERENCES ocr_results(id) ON DELETE SET NULL
      )`
    ];
    
    for (const tableSQL of tables) {
      await this.run(tableSQL);
    }
  }
  
  async createIndexes() {
    const indexes = [
      // Existing indexes
      'CREATE INDEX IF NOT EXISTS idx_folders_project_id ON folders(project_id)',
      'CREATE INDEX IF NOT EXISTS idx_folders_parent_id ON folders(parent_id)',
      'CREATE INDEX IF NOT EXISTS idx_folders_path ON folders(path)',
      'CREATE INDEX IF NOT EXISTS idx_images_folder_id ON images(folder_id)',
      'CREATE INDEX IF NOT EXISTS idx_images_path ON images(path)',
      'CREATE INDEX IF NOT EXISTS idx_ocr_results_image_id ON ocr_results(image_id)',
      'CREATE INDEX IF NOT EXISTS idx_ocr_results_folder_id ON ocr_results(folder_id)',
      'CREATE INDEX IF NOT EXISTS idx_ocr_results_processed_at ON ocr_results(processed_at)',
      'CREATE INDEX IF NOT EXISTS idx_processing_jobs_project_id ON processing_jobs(project_id)',
      'CREATE INDEX IF NOT EXISTS idx_processing_jobs_status ON processing_jobs(status)',
      'CREATE INDEX IF NOT EXISTS idx_cost_tracking_created_at ON cost_tracking(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_cost_tracking_model_id ON cost_tracking(model_id)',
      
      // Performance optimization indexes for folder queries
      'CREATE INDEX IF NOT EXISTS idx_folders_project_parent ON folders(project_id, parent_id)',
      'CREATE INDEX IF NOT EXISTS idx_folders_name ON folders(name)',
      'CREATE INDEX IF NOT EXISTS idx_folders_updated_at ON folders(updated_at)',
      'CREATE INDEX IF NOT EXISTS idx_folders_image_count ON folders(image_count)',
      'CREATE INDEX IF NOT EXISTS idx_folders_has_text_file ON folders(has_text_file)',
      
      // Composite indexes for common queries
      'CREATE INDEX IF NOT EXISTS idx_images_folder_filename ON images(folder_id, filename)',
      'CREATE INDEX IF NOT EXISTS idx_images_size_dimensions ON images(file_size, width, height)',
      
      // Covering index for folder stats
      'CREATE INDEX IF NOT EXISTS idx_folders_stats_covering ON folders(project_id, image_count, has_text_file, name, path)'
    ];
    
    for (const indexSQL of indexes) {
      await this.run(indexSQL);
    }
  }
  
  // Optimized database operations with connection pooling and caching
  async run(sql, params = []) {
    // Write operations invalidate cache
    if (sql.trim().toUpperCase().startsWith('INSERT') || 
        sql.trim().toUpperCase().startsWith('UPDATE') || 
        sql.trim().toUpperCase().startsWith('DELETE')) {
      this.invalidateCache(sql);
    }

    const connection = this.connectionPool ? await this.connectionPool.getConnection() : this.db;
    
    try {
      return new Promise((resolve, reject) => {
        connection.run(sql, params, function(err) {
          if (err) {
            console.error('Database run error:', err);
            reject(err);
          } else {
            resolve({ id: this.lastID, changes: this.changes });
          }
        });
      });
    } finally {
      if (this.connectionPool && connection !== this.db) {
        this.connectionPool.releaseConnection(connection);
      }
    }
  }
  
  async get(sql, params = []) {
    // Check cache for read operations
    const cachedResult = this.queryCache.get(sql, params);
    if (cachedResult) {
      return cachedResult;
    }

    const connection = this.connectionPool ? await this.connectionPool.getConnection() : this.db;
    
    try {
      const result = await new Promise((resolve, reject) => {
        connection.get(sql, params, (err, row) => {
          if (err) {
            console.error('Database get error:', err);
            reject(err);
          } else {
            resolve(row);
          }
        });
      });

      // Cache the result
      if (result) {
        this.queryCache.set(sql, params, result);
      }
      
      return result;
    } finally {
      if (this.connectionPool && connection !== this.db) {
        this.connectionPool.releaseConnection(connection);
      }
    }
  }
  
  async all(sql, params = []) {
    // Check cache for read operations
    const cachedResult = this.queryCache.get(sql, params);
    if (cachedResult) {
      return cachedResult;
    }

    const connection = this.connectionPool ? await this.connectionPool.getConnection() : this.db;
    
    try {
      const result = await new Promise((resolve, reject) => {
        connection.all(sql, params, (err, rows) => {
          if (err) {
            console.error('Database all error:', err);
            reject(err);
          } else {
            resolve(rows);
          }
        });
      });

      // Cache the result
      this.queryCache.set(sql, params, result);
      
      return result;
    } finally {
      if (this.connectionPool && connection !== this.db) {
        this.connectionPool.releaseConnection(connection);
      }
    }
  }

  invalidateCache(sql) {
    const upperSql = sql.toUpperCase();
    if (upperSql.includes('FOLDERS')) {
      this.folderCache.clear();
      this.queryCache.invalidate('folders');
    }
    if (upperSql.includes('IMAGES')) {
      this.queryCache.invalidate('images');
    }
    if (upperSql.includes('PROJECTS')) {
      this.queryCache.invalidate('projects');
    }
  }
  
  // Transaction support
  async transaction(operations) {
    await this.run('BEGIN TRANSACTION');
    
    try {
      const results = [];
      for (const operation of operations) {
        const result = await operation(this);
        results.push(result);
      }
      
      await this.run('COMMIT');
      return results;
    } catch (error) {
      await this.run('ROLLBACK');
      throw error;
    }
  }
  
  // Backup database
  async backup(backupPath) {
    return new Promise((resolve, reject) => {
      const backup = this.db.backup(backupPath);
      
      backup.step(-1, (err) => {
        if (err) {
          reject(err);
        } else {
          backup.finish((err) => {
            if (err) {
              reject(err);
            } else {
              resolve(backupPath);
            }
          });
        }
      });
    });
  }
  
  // Database maintenance
  async vacuum() {
    await this.run('VACUUM');
    console.log('Database vacuumed successfully');
  }
  
  async analyze() {
    await this.run('ANALYZE');
    console.log('Database statistics updated');
  }
  
  // Get database statistics
  async getStats() {
    const tables = ['projects', 'folders', 'images', 'ocr_results', 'processing_jobs', 'cost_tracking'];
    const stats = {};
    
    for (const table of tables) {
      try {
        const result = await this.get(`SELECT COUNT(*) as count FROM ${table}`);
        stats[table] = result.count;
      } catch (error) {
        stats[table] = 0;
      }
    }
    
    // Get database file size
    try {
      const fs = require('fs').promises;
      const dbStats = await fs.stat(this.dbPath);
      stats.databaseSize = dbStats.size;
      stats.databaseSizeMB = Math.round(dbStats.size / (1024 * 1024) * 100) / 100;
    } catch (error) {
      stats.databaseSize = 0;
      stats.databaseSizeMB = 0;
    }
    
    return stats;
  }
  
  // Optimized folder queries with dedicated caching
  async getFoldersByProject(projectId) {
    const cacheKey = `folders_by_project_${projectId}`;
    const cached = this.folderCache.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.folderCache.ttl) {
      this.folderCache.stats.hits++;
      return cached.data;
    }

    const sql = `
      SELECT f.*, 
             COUNT(i.id) as actual_image_count,
             EXISTS(SELECT 1 FROM folders WHERE parent_id = f.id) as has_children
      FROM folders f
      LEFT JOIN images i ON f.id = i.folder_id
      WHERE f.project_id = ?
      GROUP BY f.id
      ORDER BY f.path
    `;
    
    const result = await this.all(sql, [projectId]);
    
    // Cache with shorter TTL for frequently accessed data
    this.folderCache.cache.set(cacheKey, {
      data: result,
      timestamp: Date.now()
    });
    this.folderCache.stats.sets++;
    
    return result;
  }

  async getFolderHierarchy(projectId, parentId = null) {
    const cacheKey = `folder_hierarchy_${projectId}_${parentId || 'root'}`;
    const cached = this.folderCache.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.folderCache.ttl) {
      this.folderCache.stats.hits++;
      return cached.data;
    }

    const sql = `
      WITH RECURSIVE folder_tree AS (
        SELECT id, name, path, parent_id, relative_path, image_count, has_text_file, 0 as level
        FROM folders 
        WHERE project_id = ? AND parent_id IS NULL
        
        UNION ALL
        
        SELECT f.id, f.name, f.path, f.parent_id, f.relative_path, f.image_count, f.has_text_file, ft.level + 1
        FROM folders f
        INNER JOIN folder_tree ft ON f.parent_id = ft.id
        WHERE f.project_id = ?
      )
      SELECT * FROM folder_tree ORDER BY level, path
    `;
    
    const result = await this.all(sql, [projectId, projectId]);
    
    this.folderCache.cache.set(cacheKey, {
      data: result,
      timestamp: Date.now()
    });
    this.folderCache.stats.sets++;
    
    return result;
  }

  async updateFolderStats(folderId, imageCount, hasTextFile) {
    // Invalidate folder caches before update
    this.folderCache.clear();
    
    const sql = `
      UPDATE folders 
      SET image_count = ?, has_text_file = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `;
    
    return await this.run(sql, [imageCount, hasTextFile, folderId]);
  }

  // Get cache statistics
  getCacheStats() {
    return {
      queryCache: this.queryCache.getStats(),
      folderCache: this.folderCache.getStats(),
      connectionPool: {
        activeConnections: this.connectionPool?.activeConnections || 0,
        poolSize: this.connectionPool?.pool.length || 0,
        waitingQueue: this.connectionPool?.waitingQueue.length || 0
      }
    };
  }

  // Close database connection and cleanup
  async close() {
    try {
      // Close connection pool
      if (this.connectionPool) {
        await this.connectionPool.closeAll();
      }
      
      // Close main connection
      if (this.db) {
        await new Promise((resolve, reject) => {
          this.db.close((err) => {
            if (err) {
              console.error('Error closing database:', err);
              reject(err);
            } else {
              console.log('Database connection closed');
              this.isInitialized = false;
              resolve();
            }
          });
        });
      }
      
      // Clear caches
      this.queryCache.clear();
      this.folderCache.clear();
      
    } catch (error) {
      console.error('Error closing database:', error);
      throw error;
    }
  }
  
  // Health check
  async healthCheck() {
    try {
      await this.get('SELECT 1');
      return { status: 'healthy', timestamp: new Date().toISOString() };
    } catch (error) {
      return { status: 'unhealthy', error: error.message, timestamp: new Date().toISOString() };
    }
  }
}

module.exports = DatabaseManager;