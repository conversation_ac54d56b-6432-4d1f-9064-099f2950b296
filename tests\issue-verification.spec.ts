import { test, expect } from '@playwright/test';

// Test to verify the specific issues reported by the user are fixed
test.describe('Issue Verification Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
  });

  test('ISSUE 1: "Failed to scan project folders" error should be fixed', async ({ page }) => {
    // Test the specific backend endpoint that was failing
    const response = await page.request.post('http://localhost:3003/api/filesystem/scan', {
      data: {
        path: 'C:\\claude\\dl-organizer\\files'
      }
    });
    
    // Should not return error about path validation anymore
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    // Should not have any path validation errors
    expect(data.error).not.toMatch(/path validation failed/i);
  });

  test('ISSUE 2: Drive expansion should not spin forever', async ({ page }) => {
    // Test the drives endpoint performance
    const startTime = Date.now();
    
    const response = await page.request.post('http://localhost:3003/api/filesystem/folders', {
      data: {
        path: 'C:/',
        includeStats: false, // This is the key performance fix
        maxDepth: 1
      }
    });
    
    const responseTime = Date.now() - startTime;
    
    expect(response.status()).toBe(200);
    
    // Should respond quickly (under 1 second with the performance fix)
    expect(responseTime).toBeLessThan(1000);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.folders).toBeDefined();
    expect(data.folders.length).toBeGreaterThan(0);
  });

  test('Backend API health check', async ({ page }) => {
    // Verify backend is running and healthy
    const response = await page.request.get('http://localhost:3003/api/health');
    expect(response.status()).toBe(200);
  });

  test('Windows path validation fix verification', async ({ page }) => {
    // Test various Windows path formats that were problematic
    const testPaths = [
      'C:\\claude\\dl-organizer\\files',
      'C:\\Users\\<USER>\\Documents',
      'C:/claude/dl-organizer/files', // Forward slash variant
    ];

    for (const testPath of testPaths) {
      const response = await page.request.post('http://localhost:3003/api/filesystem/scan', {
        data: { path: testPath }
      });
      
      // Should not fail due to path validation anymore
      expect(response.status()).toBe(200);
      
      const data = await response.json();
      // Should either succeed or fail gracefully (not due to path validation)
      if (!data.success) {
        expect(data.error).not.toMatch(/path validation failed|invalid path|malformed path/i);
      }
    }
  });

  test('Drive performance optimization verification', async ({ page }) => {
    // Test that drive roots skip expensive image counting
    const startTime = Date.now();
    
    const response = await page.request.post('http://localhost:3003/api/filesystem/folders', {
      data: {
        path: 'C:/',
        includeStats: false,
        maxDepth: 1
      }
    });
    
    const responseTime = Date.now() - startTime;
    
    expect(response.status()).toBe(200);
    expect(responseTime).toBeLessThan(2000); // Should be much faster now
    
    const data = await response.json();
    expect(data.folders).toBeDefined();
    
    // With includeStats: false, imageCount should be 0 or undefined for performance
    const firstFolder = data.folders[0];
    if (firstFolder && firstFolder.imageCount !== undefined) {
      expect(firstFolder.imageCount).toBe(0);
    }
  });

  test('Frontend folder picker loads without critical errors', async ({ page }) => {
    const consoleErrors: string[] = [];
    
    page.on('console', msg => {
      if (msg.type() === 'error' && !msg.text().includes('favicon')) {
        consoleErrors.push(msg.text());
      }
    });

    // Navigate to project creation
    await page.getByRole('button', { name: /new project/i }).click();
    await page.waitForTimeout(1000);

    // Open folder picker
    const browseButton = page.getByRole('button', { name: /browse/i });
    if (await browseButton.isVisible()) {
      await browseButton.click();
      
      // Wait for dialog
      await expect(page.getByRole('dialog')).toBeVisible();
      
      // Wait for drives to load
      await page.waitForTimeout(3000);
      
      // Should see drives without errors
      await expect(page.getByText(/C:.*Drive/i)).toBeVisible();
    }

    // Verify no critical console errors related to our fixes
    const criticalErrors = consoleErrors.filter(error => 
      error.includes('Failed to scan') || 
      error.includes('path validation') ||
      error.includes('malformed path') ||
      error.includes('network error')
    );
    
    expect(criticalErrors).toEqual([]);
  });
});