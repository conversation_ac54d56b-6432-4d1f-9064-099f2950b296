# 🚗 DL Organizer

> **AI-Powered Driver's License OCR Processing System**  
> A comprehensive web application for organizing, processing, and extracting data from driver's license images using advanced OCR technology.

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Node.js](https://img.shields.io/badge/Node.js-18+-green.svg)](https://nodejs.org/)
[![Next.js](https://img.shields.io/badge/Next.js-14+-blue.svg)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.8+-blue.svg)](https://www.typescriptlang.org/)

## 🆕 Latest Updates (v1.3.0)

### 🤖 **Smart Filter Analyzer**
- **✅ AI-Powered Folder Analysis**: Automatically scan and analyze entire folders to identify patterns and content types
- **✅ License Side Detection**: Intelligent classification of driver's license images (front/back/selfie/unknown) using OCR technology
- **✅ Pattern Recognition**: Discover filename patterns, size distributions, resolution clusters, and naming conventions
- **✅ One-Click Filter Chips**: Generate instant filter chips based on analysis ("527 fronts", "≤ 300KB", "token:gen-1 (500)")
- **✅ Real-time Progress**: Live streaming analysis with Server-Sent Events for immediate feedback
- **✅ Smart Caching**: Cache side detection and analysis results for instant subsequent scans
- **✅ Seamless Integration**: Smart chips integrate directly with existing filter system for immediate use

### 🔍 **Power-Filter Workbench System** (v1.2.0)
- **✅ Stackable Filters**: Build complex queries with size, resolution, filename, and cache status filters
- **✅ Smart Selection Controls**: "Select All Visible" and "Select All in Folder" with additive accumulation
- **✅ Advanced Pagination**: Configurable page sizes (50-500 items) with "Show All" toggle for large datasets
- **✅ Real-time Filter Chips**: Visual filter management with one-click removal and clear indicators
- **✅ Performance Optimization**: Sub-100ms filtering with pre-computed image metadata caching
- **✅ Debounced Text Search**: Smart filename filtering that activates after 3+ characters
- **✅ Professional UI**: Enterprise-grade filtering interface with comprehensive statistics display

### 🤖 **Smart Filter Analyzer (NEW)**
- **✅ AI-Powered Pattern Recognition**: Automatically analyze folder contents to identify filename patterns, size distributions, and image types
- **✅ License Side Detection**: Intelligent classification of driver's license front/back/selfie images using OCR technology
- **✅ One-Click Filter Chips**: Generate instant filter chips based on discovered patterns (e.g., "527 fronts", "≤ 300KB", "token:gen-1 (500)")
- **✅ Real-time Analysis Progress**: Live streaming progress with SSE for immediate feedback during folder analysis
- **✅ Intelligent Caching**: Cache analysis results to make subsequent folder scans instant and cost-free
- **✅ Pattern-Based Selection**: Smart chips that integrate seamlessly with the existing filter system
- **✅ Performance Optimized**: Sub-3-second analysis using fast, cost-effective AI models

### 🎯 **Enhanced Image Management**
- **✅ Multi-Filter Workflows**: Layer filters to narrow down thousands of images to exact requirements
- **✅ Selection Accumulation**: Build complex selections across multiple filter states 
- **✅ Batch Operations**: Process filtered selections with full OCR pipeline integration
- **✅ Cache-Aware Filtering**: Filter by processed/unprocessed status with real-time cache checking
- **✅ Smart Pagination**: Handle large image collections with seamless page navigation
- **✅ Performance Monitoring**: Real-time filter statistics and processing feedback

### 🏗️ **Technical Architecture Enhancements**
- **✅ Type-Safe Filter System**: Discriminated union types with exhaustive TypeScript checking
- **✅ Memoized Filter Pipeline**: Efficient sequential filter application with performance optimization
- **✅ Debounced Performance**: Smart input handling prevents excessive API calls and UI updates
- **✅ Memory Management**: Intelligent caching with cleanup to prevent memory leaks
- **✅ Component Architecture**: Modular filter, selection, and pagination components

### 🚀 **Previous Updates (v1.1.0)**

#### 🤖 **AI Model Validation System** 
- **✅ OpenRouter Integration**: Real-time model availability checking with API validation
- **✅ Smart Model Management**: Automatic replacement suggestions for unavailable models
- **✅ Validation Dashboard**: Comprehensive model testing interface with success/failure tracking
- **✅ Auto-Fix Capabilities**: Intelligent model substitution with detailed change tracking
- **✅ Performance Metrics**: Cost analysis, response time monitoring, and accuracy scoring

#### 🧪 **Enhanced OCR Testing Playground**
- **✅ Multi-Model Testing**: Compare multiple OCR models side-by-side with validation integration
- **✅ Session Management**: Persistent test sessions with export capabilities
- **✅ Results Analysis**: Detailed accuracy metrics, cost breakdowns, and performance comparisons
- **✅ Batch Testing**: Automated testing across multiple images and models
- **✅ Data Export**: JSON export of test results for analysis and reporting

**Professional image management with enterprise-grade filtering and selection capabilities!**

---

## 🌟 Features

### 🔍 **Smart OCR Processing**
- **Multi-Provider Support**: OpenAI GPT-4o, OpenRouter, and local models
- **AI Model Validation**: Real-time model availability checking with auto-replacement
- **Validation Dashboard**: Comprehensive testing interface with performance metrics
- **Dual Region Support**: Specialized US and Australian driver's license processing
- **Country Mode Toggle**: Switch between US and Australian OCR optimization
- **Intelligent Field Extraction**: Automatic detection of names, dates, addresses, and license numbers
- **Cost Tracking**: Real-time API usage monitoring and rate limiting
- **ReadySearch Integration**: Advanced search capabilities for Australian driver's license data

### 📁 **Advanced File Management**
- **Windows Integration**: Native filesystem navigation with drive detection
- **Folder Hierarchy**: Deep directory scanning with image count statistics
- **Thumbnail Generation**: Fast preview generation with caching
- **Smart Filter Analyzer**: AI-powered folder analysis with license side detection and pattern recognition
- **Power-Filter Workbench**: Professional filtering system with stackable filters for size, resolution, filename, and cache status
- **Smart Selection Controls**: Select All Visible/Folder with additive accumulation across filter states
- **Advanced Pagination**: Configurable page sizes (50-500) with Show All toggle for large collections
- **Real-time Filter Management**: Visual filter chips with one-click removal and comprehensive statistics
- **Performance Optimization**: Sub-100ms filtering with intelligent caching and debounced text search
- **Batch Processing**: Bulk OCR processing for entire folders with filtered selection support
- **ReadySearch Integration**: Search and match extracted data against Australian DL database

### 🎨 **Modern User Interface**
- **Responsive Design**: Works seamlessly on desktop and mobile
- **Dark/Light Mode**: Automatic theme switching with system preference
- **USA/AUS Mode Toggle**: Smart country mode switching with field adaptation
- **Document Type Selection**: Mode-based defaults with cross-session persistence
- **Enhanced Template Editor**: Field deletion, restoration, and customization capabilities
- **Beautiful Gray Theme**: Consistent styling with improved accessibility
- **Auto-Connection Status**: Automatic API connection testing without manual intervention
- **Custom SVG Flag Icons**: Professional US and AUS flag graphics (replacing emoji)
- **Enhanced OCR Panel**: Wider layout (420px) with improved spacing and visual hierarchy  
- **Mode-Specific Fields**: Card Number for AUS mode, Issue Date for US mode
- **Intelligent Name Parsing**: Proper handling of first, middle, and last names
- **Smart Document Type Defaults**: Auto-Detect mode selected when switching countries
- **Real-time Updates**: Live progress tracking for long-running operations
- **Intuitive Navigation**: Clean, professional interface built with Radix UI

### ⚡ **Performance & Reliability**
- **Robust Architecture**: Comprehensive error handling and logging
- **Caching System**: Smart caching for OCR results and thumbnails
- **Health Monitoring**: Built-in system health checks and maintenance
- **Windows Service**: Optional background service installation

---

## 🚀 Quick Start

### Prerequisites

- **Node.js 18+** - [Download here](https://nodejs.org/)
- **Windows 10/11** - Optimized for Windows file system
- **API Keys** - OpenAI or OpenRouter API access (configured via in-app settings)

### 1. Clone & Install

```bash
git clone https://github.com/aaronvstory/dl-organizer.git
cd dl-organizer
npm install
```

### 2. Initial Setup

```bash
# Run automated setup
npm run setup

# Launch the application and configure:
# - API keys via Settings panel
# - Choose country mode (US/Australian)
# - Set up ReadySearch integration (for Australian DL)
```

### 3. Start Development

```bash
# Start both frontend and backend
npm run dev

# Or start individually
npm run dev:frontend  # Next.js on http://localhost:3030
npm run dev:backend   # Express API on http://localhost:3050
```

### 4. Open Application

Visit **http://localhost:3030** and start organizing your driver's license images!

---

## 🏗️ Architecture

DL Organizer is built as a **hybrid web application** combining the best of modern web technologies:

### Frontend (Next.js)
- **Framework**: Next.js 14+ with TypeScript
- **Styling**: Tailwind CSS with Radix UI components
- **State Management**: Zustand for reactive state
- **Forms**: React Hook Form with Zod validation

### Backend (Express.js)
- **Server**: Express.js with compression and security middleware
- **Database**: SQLite for local data storage
- **Image Processing**: Sharp for thumbnail generation and rotation
- **File System**: Native Windows API integration

### AI Integration
- **Model Validation System**: Real-time OpenRouter API integration with model availability checking
- **OCR Providers**: OpenAI GPT-4o, OpenRouter, local models with intelligent validation
- **Auto-Fix Capabilities**: Automatic model replacement suggestions with change tracking
- **Testing Playground**: Comprehensive multi-model testing interface with session management
- **Country-Specific Processing**: Optimized for US and Australian driver's licenses
- **ReadySearch Integration**: Australian DL database search and verification
- **Cost Tracking**: Real-time API usage monitoring with performance metrics
- **Smart Caching**: Persistent OCR result storage with validation data
- **Batch Processing**: Efficient bulk operations with country mode support

---

## 📖 Usage Guide

### Creating a Project

1. **Launch DL Organizer** and click "Create New Project"
2. **Name your project** and select the root folder containing your images
3. **Choose country mode** (US or Australian) for optimal OCR accuracy
4. **Configure ReadySearch** (for Australian DL projects) to enable advanced search features
5. **Start organizing** your driver's license images

### Processing Images

#### Single Image OCR
1. **Navigate** to any folder in your project
2. **Select country mode** (🇺🇸 USA or 🇦🇺 AUS) using the visual toggle
3. **Choose document type** from the smart dropdown (auto-defaults based on mode)
4. **Click on an image** to view it in detail
5. **Select OCR provider** and model from the dropdown
6. **Test connection** to verify API connectivity with visual status indicators
7. **Click "Analyze Image"** to extract driver's license data
8. **Review and save** the extracted information with enhanced field validation

#### Batch Processing
1. **Enable batch selection mode** using the checkbox toggle
2. **Select multiple folders** containing driver's license images
3. **Choose your OCR settings** (provider, model, country mode)
4. **Start batch processing** and monitor progress in real-time
5. **Review results** and export data when complete

#### ReadySearch Integration (Australian DL)
1. **Process Australian driver's licenses** using Australian country mode
2. **Extract personal information** (first name, last name, date of birth)
3. **Access ReadySearch panel** after successful OCR analysis
4. **Search Australian DL database** using extracted information
5. **Review search results** and cross-reference license details
6. **Verify identity** and compliance using integrated database lookup

### Advanced Features

#### Country-Specific Processing
- **US Mode**: Optimized for US driver's license formats and data fields
- **Australian Mode**: Specialized processing for Australian driver's licenses
- **Smart Mode Toggle**: Visual country selection (🇺🇸/🇦🇺) with auto-field adaptation
- **Document Type Management**: Mode-based defaults with persistence across sessions
- **Field Template Customization**: Add, remove, and restore fields for each document type
- **Auto-Detection**: Smart recognition of document country of origin
- **Persistent Settings**: All preferences saved across application restarts

#### OCR Testing Playground
- **Multi-Model Testing**: Compare multiple OCR models side-by-side with detailed metrics
- **Model Validation**: Real-time checking of model availability with automatic replacements
- **Session Management**: Persistent test sessions with comprehensive result tracking
- **Performance Analysis**: Cost breakdown, response time monitoring, and accuracy scoring
- **Data Export**: JSON export of test results for analysis and reporting
- **Batch Testing**: Automated testing across multiple images and model combinations

#### ReadySearch Integration (Australian DL)
- **Database Search**: Search extracted data against Australian DL database
- **Identity Verification**: Cross-reference license details for validation
- **Advanced Matching**: Fuzzy matching for names and partial data
- **Compliance Tools**: Support for verification and compliance workflows

#### Cost Tracking
- Monitor API usage and costs in real-time
- Set spending limits and alerts
- View detailed usage statistics
- Optimize costs by comparing providers

#### File Organization
- Automatically organize images by extracted data
- Rename files using driver's license information
- Create structured folder hierarchies
- Export organized data to CSV/JSON

---

## 🛠️ Development

### Project Structure

```
dl-organizer/
├── backend/                 # Express.js backend
│   ├── routes/             # API endpoints
│   ├── services/           # Business logic
│   ├── utils/              # Helper utilities
│   └── server.js           # Main server file
├── src/                    # Next.js frontend
│   ├── app/                # App router pages
│   ├── components/         # React components
│   ├── lib/                # Utility libraries
│   └── types/              # TypeScript definitions
├── tests/                  # E2E and integration tests
├── scripts/                # Maintenance and setup scripts
└── data/                   # Local data storage
```

### Available Scripts

```bash
# Development
npm run dev                 # Start both frontend and backend
npm run dev:frontend        # Start Next.js only
npm run dev:backend         # Start Express.js only

# Production
npm run build              # Build for production
npm run start              # Start production servers
npm run setup              # Production environment setup

# Testing
npm run test               # Run Jest unit tests
npm run test:e2e           # Run Playwright E2E tests
npm run lint               # ESLint code quality
npm run typecheck          # TypeScript validation

# Maintenance
npm run health             # System health check
npm run backup             # Create database backup
npm run maintenance        # Run maintenance tasks
npm run service:install    # Install as Windows service
```

### API Documentation

#### OCR Processing
```typescript
POST /api/ocr/analyze
{
  "imagePath": "path/to/image.jpg",
  "modelId": "gpt-4o",
  "countryMode": "us"
}

POST /api/ocr/analyze-by-path
{
  "imagePath": "path/to/image.jpg",
  "modelId": "openrouter/model-id",
  "extractionType": "driver_license"
}

GET /api/ocr/prompts                    # Get available OCR prompts
POST /api/ocr/save-results              # Save OCR results
GET /api/ocr/saved-data/:imageId        # Get saved OCR data
```

#### Model Validation
```typescript
GET /api/model-validation/available     # Get available models
POST /api/model-validation/validate     # Validate model list
POST /api/model-validation/auto-fix     # Auto-fix invalid models
DELETE /api/model-validation/cache      # Clear validation cache
```

#### File System Operations
```typescript
GET /api/filesystem/drives              # List available drives
POST /api/filesystem/scan               # Scan directory for images
GET /api/folders/:id/images             # Get images in folder
```

#### Image Operations
```typescript
GET /thumbnails/:filename               # Get cached thumbnail
POST /api/images/:id/rotate             # Rotate image
```

---

## 🔧 Configuration

### In-App Configuration

DL Organizer provides a user-friendly Settings panel for configuration:

#### OCR Provider Setup
1. **Launch the application** and navigate to Settings
2. **API Keys Section**: Enter your API keys directly in the secure settings panel
   - OpenAI API Key (for GPT-4o, GPT-4o-mini models)
   - OpenRouter API Key (for access to multiple providers)
3. **Country Mode**: Choose between US and Australian processing modes
4. **ReadySearch**: Configure Australian DL database integration
5. **Model Selection**: Choose default OCR models and providers

#### Getting API Keys

**OpenAI**
- Get API key from [OpenAI Platform](https://platform.openai.com/api-keys)
- Paste into Settings → OCR Providers → OpenAI API Key
- Available models: GPT-4o, GPT-4o-mini

**OpenRouter**
- Get API key from [OpenRouter](https://openrouter.ai/keys)
- Paste into Settings → OCR Providers → OpenRouter API Key
- Access to multiple model providers and competitive pricing

### Optional Environment Variables

For advanced configuration, create a `.env.local` file:

```env
# Server Configuration (optional)
PORT=3050
NODE_ENV=production

# Database (optional)
DATABASE_PATH=./data/dl-organizer.db

# Logging (optional)
LOG_LEVEL=info
LOG_DIRECTORY=./data/logs
```

> **Note**: API keys are managed through the Settings panel and stored securely using Windows Credential Manager. Environment variables are optional for advanced server configuration only.

---

## 🔐 Security & Privacy

- **Local Processing**: All data stays on your machine
- **Secure API Handling**: API keys stored securely using Windows Credential Manager
- **Rate Limiting**: Built-in protection against API abuse
- **Input Validation**: Comprehensive validation for all user inputs
- **Safe File Handling**: Protection against path traversal attacks

---

## 🧪 Testing

### Running Tests

```bash
# Unit tests
npm run test

# E2E tests (requires running application)
npm run test:e2e

# Specific test suites
npm run test -- --testPathPattern=ocr
npm run test:e2e -- --grep="image processing"
```

### Test Coverage

- **Unit Tests**: Core business logic and utilities
- **Integration Tests**: API endpoints and database operations
- **E2E Tests**: Complete user workflows with Playwright
- **Visual Tests**: Screenshot comparison for UI consistency

---

## 🚀 Deployment

### Windows Service Installation

```bash
# Install as Windows service
npm run service:install

# Start/stop service
net start "DL Organizer"
net stop "DL Organizer"

# Remove service
npm run service:uninstall
```

### Production Setup

```bash
# Complete production setup
npm run setup

# Manual steps
npm run build
npm run start
```

### System Requirements

- **OS**: Windows 10/11
- **Memory**: 4GB RAM minimum, 8GB recommended
- **Storage**: 1GB free space for application and data
- **Network**: Internet connection for OCR API access

---

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Setup

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and add tests
4. Ensure all tests pass: `npm run test && npm run test:e2e`
5. Commit your changes: `git commit -m 'Add amazing feature'`
6. Push to the branch: `git push origin feature/amazing-feature`
7. Open a Pull Request

---

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 🙋‍♂️ Support

### Getting Help

- **Documentation**: Check our [Wiki](https://github.com/aaronvstory/dl-organizer/wiki) for detailed guides
- **Issues**: Report bugs or request features on [GitHub Issues](https://github.com/aaronvstory/dl-organizer/issues)
- **Discussions**: Ask questions in [GitHub Discussions](https://github.com/aaronvstory/dl-organizer/discussions)

### Common Issues

<details>
<summary><strong>OCR not working / API errors</strong></summary>

1. Verify your API keys are correctly set in `.env.local`
2. Check your API usage limits and billing status
3. Try a different OCR provider or model
4. Review logs in `data/logs/` for detailed error messages
</details>

<details>
<summary><strong>Images not loading / Thumbnail errors</strong></summary>

1. Ensure images are in supported formats (JPG, PNG, WEBP)
2. Check file permissions for the image directories
3. Verify sufficient disk space for thumbnail cache
4. Try clearing the thumbnail cache in settings
</details>

<details>
<summary><strong>Performance issues / Slow processing</strong></summary>

1. Close other resource-intensive applications
2. Process smaller batches of images
3. Use faster OCR models (e.g., GPT-4o-mini)
4. Enable thumbnail caching for faster navigation
</details>

---

---

## 📋 Changelog

### v1.2.0 - "Power-Filter Workbench" (2025-01-24)

#### 🔍 **Power-Filter System**
- **Stackable Filter Architecture**: Type-safe discriminated union filter system with exhaustive TypeScript checking
- **Real-time Filter Management**: Visual filter chips with one-click removal and comprehensive statistics display
- **Multi-Filter Workflows**: Size, resolution, filename, and cache status filters with seamless filter combination
- **Performance Optimization**: Sub-100ms filtering with pre-computed image metadata caching and memoized filter pipeline

#### 🎯 **Advanced Selection Controls**
- **Smart Selection System**: "Select All Visible" and "Select All in Folder" with intelligent accumulation
- **Additive Selection**: Build complex selections across multiple filter states with persistent selection memory
- **Batch Integration**: Process filtered selections with full OCR pipeline and batch operation support
- **Selection Statistics**: Real-time feedback showing selected counts and filter impact metrics

#### 📊 **Enterprise Pagination**
- **Configurable Page Sizes**: 50, 100, 200, 500 items per page with dynamic page size adjustment
- **Show All Toggle**: Bypass pagination for smaller datasets with instant "Show All" functionality
- **Smart Navigation**: Previous/next/first/last controls with direct page input and keyboard navigation
- **Performance Scaling**: Handle thousands of images with seamless pagination and memory management

#### 🏗️ **Technical Architecture**
- **Component Architecture**: Modular FilterBuilder, SelectionControls, and PaginationControls components
- **Debounced Performance**: Smart input handling with 400ms debouncing prevents excessive API calls
- **Memory Management**: Intelligent caching with cleanup functions to prevent memory leaks
- **Hook System**: useFilterReducer and useDebouncedValue hooks for optimal performance

#### ✨ **New Components**
- **FilterBuilder**: Professional filter interface with input controls and visual chip management  
- **SelectionControls**: Comprehensive selection management with statistics and batch operations
- **PaginationControls**: Advanced pagination with configurable sizing and navigation controls
- **Filter Pipeline**: Memoized sequential filter application with performance monitoring

### v1.1.0 - "Enhanced Model Validation" (2025-01-23)

#### 🚀 **Major Features**
- **AI Model Validation System**: Complete OpenRouter API integration with real-time model availability checking
- **Enhanced OCR Testing Playground**: Multi-model testing interface with session management and data export
- **Auto-Fix Capabilities**: Intelligent model substitution with detailed change tracking
- **Advanced Backend Services**: New model validator service with caching and validation APIs

#### ✨ **New Components**
- **Model Validation Panel**: Real-time model checking with auto-fix suggestions
- **Testing Results Dashboard**: Comprehensive analysis with accuracy metrics and cost breakdowns
- **Session Management**: Persistent test sessions with JSON export capabilities
- **Validation Hooks**: React hooks for model validation state management

#### 🔧 **API Enhancements**
- **Model Validation Endpoints**: `/api/model-validation/*` for real-time model checking
- **Enhanced OCR Routes**: Path-based analysis with improved error handling
- **Caching System**: Intelligent caching for model validation results
- **Better Error Responses**: Structured error handling with detailed context

#### 📚 **ReadySearch v3.0 Integration**
- **Enhanced CLI Tools**: Improved automation and command-line interface
- **GUI Improvements**: Modern interface with better user experience
- **Database Integration**: Advanced Australian DL search capabilities
- **Performance Optimization**: Faster processing with intelligent caching

#### 🏗️ **Technical Improvements**
- **Code Quality**: All ESLint warnings resolved, TypeScript compilation verified
- **Production Build**: Optimized builds with static generation support
- **Directory Cleanup**: Automated cleanup of temporary files and build artifacts
- **Error Handling**: Comprehensive error boundaries and recovery mechanisms

#### 🐛 **Bug Fixes**
- Fixed TypeScript compilation errors in OCR components
- Resolved React hooks dependency warnings
- Fixed function declaration order issues
- Improved error handling in API proxy routes

### v1.1.2 - "UI/UX Enhancement" (Previous Release)

#### 🎨 **UI/UX Improvements**
- Auto-connection status display without manual testing
- Fixed US/AUS mode toggle with proper document type switching
- Professional SVG flag icons replacing emoji
- Enhanced OCR panel layout (420px) with improved spacing
- Smart field display (Card Number for AUS, Issue Date for US)
- Improved name parsing for middle names

#### 🏗️ **Production Readiness**
- Fixed all ESLint warnings for robust code quality
- Clean repository with archived debug files
- GitHub-ready optimization for seamless deployment
- Comprehensive functionality verification

### v1.1.1 - "Mode Management" (Previous Release)

#### ✨ **Mode Management Features**
- Smart USA/AUS country selection with visual indicators
- Intelligent document type defaults based on country mode
- Enhanced template editor with field deletion and restoration
- Real-time connection testing with visual status feedback
- Beautiful gray theme with enhanced accessibility
- Persistent state management across server restarts

#### 🛡️ **Technical Enhancements**
- Enhanced TypeScript support and error handling
- Optimized component performance with reduced re-renders
- Improved accessibility with ARIA labels and keyboard navigation
- Better responsive design and mobile compatibility

---

## 🚀 What's Next?

### Upcoming Features

- 🌐 **Web Deployment**: Cloud-hosted version for team collaboration
- 📱 **Mobile App**: Native mobile application for iOS and Android
- 🔗 **API Integration**: RESTful API for third-party integrations
- 🤖 **Advanced AI**: Custom model training for specific document types
- 📊 **Analytics Dashboard**: Advanced reporting and data visualization
- 🔄 **Sync**: Multi-device synchronization and backup

---

<div align="center">

**Made with ❤️ by the DL Organizer Team**

[⭐ Star this repo](https://github.com/aaronvstory/dl-organizer) • [🐛 Report Bug](https://github.com/aaronvstory/dl-organizer/issues) • [💡 Request Feature](https://github.com/aaronvstory/dl-organizer/issues)

</div>