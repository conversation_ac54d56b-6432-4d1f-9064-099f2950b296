import { test, expect } from '@playwright/test';

// Test the drives and folder picker functionality
test.describe('Drives and Folder Picker Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the app
    await page.goto('http://localhost:3001');
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');
  });

  test('should load drives when creating a new project', async ({ page }) => {
    // Click "New Project" button
    await page.getByRole('button', { name: /new project/i }).click();

    // Wait for the project creation form to appear
    await expect(page.getByText('Create New Project')).toBeVisible();

    // Find the folder picker browse button
    const browseButton = page.getByRole('button', { name: /browse/i });
    await expect(browseButton).toBeVisible();

    // Click the browse button to open the folder picker
    await browseButton.click();

    // Wait for the folder picker dialog to open
    await expect(page.getByRole('dialog')).toBeVisible();
    await expect(page.getByRole('heading', { name: /select folder/i })).toBeVisible();

    // Check if drives are loading (should not show "failed to load drives")
    await page.waitForTimeout(3000); // Wait for drives to load
    
    // Look for drive entries (should see C: Drive, etc.)
    await expect(page.getByText(/C:.*Drive/i)).toBeVisible();
    
    // Should not see any error messages
    await expect(page.getByText(/failed to load drives/i)).not.toBeVisible();
  });

  test('should expand drives to show folders', async ({ page }) => {
    // Click "New Project" button
    await page.getByRole('button', { name: /new project/i }).click();

    // Click the browse button to open the folder picker
    await page.getByRole('button', { name: /browse/i }).click();

    // Wait for the folder picker dialog to open
    await expect(page.getByRole('dialog')).toBeVisible();

    // Wait for drives to load
    await page.waitForTimeout(3000);

    // Find and click on C: Drive to expand it
    const cDrive = page.getByText(/C:.*Drive/i);
    await expect(cDrive).toBeVisible();
    await cDrive.click();

    // Wait for folders to load
    await page.waitForTimeout(3000);

    // Should see folders like Users, Program Files, etc.
    await expect(page.locator('div').getByText(/Users|Program Files|Windows/i).first()).toBeVisible();
  });

  test('should allow selecting a folder path', async ({ page }) => {
    // Click "New Project" button
    await page.getByRole('button', { name: /new project/i }).click();

    // Click the browse button to open the folder picker
    await page.getByRole('button', { name: /browse/i }).click();

    // Wait for the folder picker dialog to open
    await expect(page.getByRole('dialog')).toBeVisible();

    // Wait for drives to load
    await page.waitForTimeout(3000);

    // Click on C: Drive to select it
    const cDrive = page.getByText(/C:.*Drive/i);
    await cDrive.click();

    // Check that selection is shown
    await expect(page.getByText(/Selected.*C:/i)).toBeVisible();

    // Click "Select Folder" button
    await page.getByRole('button', { name: /select folder/i }).click();

    // Dialog should close and path should be filled in the input
    await expect(page.getByRole('dialog')).not.toBeVisible();
    await expect(page.locator('input[placeholder*="folder"]')).toHaveValue(/C:\\/i);
  });

  test('should test backend drives endpoint directly', async ({ page }) => {
    // Test the backend drives endpoint directly
    const response = await page.request.get('http://localhost:3003/api/filesystem/drives');
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.drives).toBeDefined();
    expect(data.drives.length).toBeGreaterThan(0);
    
    // Should have C: drive at minimum
    const cDrive = data.drives.find((drive: any) => drive.path === 'C:\\\\');
    expect(cDrive).toBeDefined();
    expect(cDrive.name).toContain('C:');
    expect(cDrive.type).toBe('drive');
  });

  test('should test backend folders endpoint directly', async ({ page }) => {
    // Test the backend folders endpoint directly
    const response = await page.request.post('http://localhost:3003/api/filesystem/folders', {
      data: {
        path: 'C:/'
      }
    });
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.folders).toBeDefined();
    expect(data.folders.length).toBeGreaterThan(0);
    
    // Should have common folders like Users, Program Files
    const folderNames = data.folders.map((folder: any) => folder.name);
    expect(folderNames).toContain('Users');
  });
});