"""ReadySearch.com.au automation package."""

from .input_loader import Input<PERSON>oader
from .browser_controller import <PERSON><PERSON>er<PERSON>ontroller
from .popup_handler import <PERSON>up<PERSON><PERSON><PERSON>
from .result_parser import ResultParser, NameMatcher
from .enhanced_result_parser import <PERSON>hancedResultParser, EnhancedNameMatcher, SearchStatistics
from .reporter import Reporter

__version__ = "1.0.0"
__author__ = "ReadySearch Automation"

__all__ = [
    'InputLoader',
    'BrowserController', 
    'PopupHandler',
    'ResultParser',
    'NameMatcher',
    'EnhancedResultParser',
    'EnhancedNameMatcher',
    'SearchStatistics',
    'Reporter'
]