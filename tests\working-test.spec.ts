import { test, expect } from '@playwright/test'

test.describe('DL Organizer Application Test', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3000')
    await page.waitForLoadState('networkidle')
  })

  test('should create project and display model information', async ({ page }) => {
    // Step 1: Create a project with real data
    console.log('Step 1: Creating project...')
    
    // Click the Create First Project button
    const createButton = page.locator('button').filter({ hasText: 'Create First Project' }).first()
    await expect(createButton).toBeVisible()
    await createButton.click()
    
    // Wait for the dialog/modal to appear and fill it out
    await page.waitForTimeout(1000)
    
    // Look for input fields in any visible dialog/modal
    const nameInput = page.locator('input[type="text"]').first()
    if (await nameInput.isVisible({ timeout: 5000 })) {
      await nameInput.fill('NSW Test Project')
      
      // Find the folder path input (likely the second input)
      const pathInput = page.locator('input[type="text"]').nth(1)
      if (await pathInput.isVisible()) {
        await pathInput.fill('C:\\claude\\dl-organizer\\files\\9172-NSW-DLs')
      }
      
      // Click create/submit button
      const submitButton = page.locator('button').filter({ hasText: /Create|Submit|Save/ }).first()
      if (await submitButton.isVisible()) {
        await submitButton.click()
        
        // Wait for project to load
        await page.waitForTimeout(5000)
        await page.waitForLoadState('networkidle')
        
        console.log('✅ Project created successfully')
      }
    }
    
    // Step 2: Check if we're now in the project interface
    console.log('Step 2: Checking project interface...')
    
    // Take a screenshot to see what we have
    await page.screenshot({ path: 'test-results/after-project-creation.png', fullPage: true })
    
    // Look for any settings button
    const settingsButton = page.locator('button').filter({ hasText: /Settings|⚙/ })
    if (await settingsButton.first().isVisible({ timeout: 5000 })) {
      console.log('✅ Settings button found')
      
      await settingsButton.first().click()
      await page.waitForTimeout(2000)
      
      // Look for General tab
      const generalTab = page.locator('button', { hasText: 'General' }).or(
        page.locator('[role="tab"]').filter({ hasText: 'General' })
      )
      
      if (await generalTab.first().isVisible({ timeout: 5000 })) {
        console.log('✅ General tab found')
        await generalTab.first().click()
        await page.waitForTimeout(1000)
        
        // Check for Field Extraction Templates section
        const templatesText = page.locator('text=Field Extraction Templates')
        await expect(templatesText).toBeVisible({ timeout: 5000 })
        console.log('✅ Field Extraction Templates section found')
        
        // Check for document type tabs
        const usTabs = page.locator('text=US Driver License')
        const ausTabs = page.locator('text=Australian Driver License')
        
        await expect(usTabs).toBeVisible({ timeout: 3000 })
        await expect(ausTabs).toBeVisible({ timeout: 3000 })
        console.log('✅ Document type tabs found')
        
        // Test Australian tab and card number field
        await ausTabs.click()
        await page.waitForTimeout(1000)
        
        const cardNumberField = page.locator('text=Card Number')
        await expect(cardNumberField).toBeVisible({ timeout: 3000 })
        console.log('✅ Card Number field found in Australian tab')
        
        // Check for Edit Prompt button
        const editPromptButton = page.locator('button').filter({ hasText: 'Edit Prompt' })
        await expect(editPromptButton.first()).toBeVisible({ timeout: 3000 })
        console.log('✅ Edit Prompt button found')
        
        // Test opening prompt editor
        await editPromptButton.first().click()
        await page.waitForTimeout(2000)
        
        // Check if prompt editor opened
        const promptEditor = page.locator('textarea[id="promptEditor"]').or(
          page.locator('textarea').filter({ hasText: /Australian/ })
        )
        
        if (await promptEditor.first().isVisible({ timeout: 3000 })) {
          console.log('✅ Prompt editor opened successfully')
          
          // Check if it contains Australian-specific content
          const editorContent = await promptEditor.first().textContent()
          expect(editorContent).toContain('Australian')
          console.log('✅ Prompt editor contains Australian-specific content')
        }
        
        console.log('🎉 All functionality tests passed!')
      }
    }
    
    // Final screenshot
    await page.screenshot({ path: 'test-results/final-state.png', fullPage: true })
  })
})