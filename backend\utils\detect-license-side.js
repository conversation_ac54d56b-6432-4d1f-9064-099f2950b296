/**
 * License side detection utility
 * Uses OCR service for lightweight side classification
 */

async function detectLicenseSide(imageBuffer, ocrService) {
  if (!imageBuffer || !ocrService) {
    throw new Error('Missing required parameters: imageBuffer and ocrService');
  }

  try {
    // Use a simple, fast model for side detection
    const sideDetectionPrompt = `Analyze this image and determine what type of driver's license image it shows.

Respond with a single word:
- "front" if it shows the front of a driver's license (with photo, name, address)
- "back" if it shows the back of a driver's license (with barcode, restrictions, endorsements)
- "selfie" if it shows someone holding a license in a selfie-style photo
- "unknown" if it's not clearly a driver's license or you can't determine the side

Only respond with one of these four words: front, back, selfie, unknown`;

    // Process with a fast, cheap model
    const result = await ocrService.processImage(imageBuffer, {
      customPrompt: sideDetectionPrompt,
      modelOverride: 'google/gemini-flash-1.5', // Use fastest free model
      extractionType: 'side_detection'
    });

    if (!result.success) {
      console.error('❌ Side detection failed:', result.error);
      return 'unknown';
    }

    // Parse the response to extract just the side
    const responseText = (result.result?.rawText || '').toLowerCase().trim();
    
    // Look for the key words in the response
    if (responseText.includes('front')) return 'front';
    if (responseText.includes('back')) return 'back';
    if (responseText.includes('selfie')) return 'selfie';
    
    // If we can't parse it, return unknown
    console.warn('⚠️ Could not parse side detection response:', responseText);
    return 'unknown';

  } catch (error) {
    console.error('❌ Side detection error:', error);
    return 'unknown';
  }
}

module.exports = { detectLicenseSide };