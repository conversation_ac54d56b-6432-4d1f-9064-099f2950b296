"use client"

import { useState, useEffect, useCallback } from 'react'

export interface ModelValidationResult {
  modelId: string
  valid: boolean
  error: string | null
  lastChecked: string
  modelInfo?: any
}

export interface ValidationSummary {
  totalModels: number
  validModels: number
  invalidModels: number
  results: ModelValidationResult[]
  timestamp: string
}

export interface ModelFixSuggestion {
  invalidModel: string
  error: string
  suggestedAlternatives: Array<{
    id: string
    name: string
    description: string
  }>
  autoFixRecommendation: string | null
}

export interface AutoFixResult {
  success: boolean
  message: string
  originalModels: any[]
  fixedModels: any[]
  changes: Array<{
    action: 'replace' | 'remove'
    original: string
    replacement: string | null
    reason: string
  }>
  validation?: ValidationSummary
}

export interface ValidationStatus {
  initialized: boolean
  cacheSize: number
  lastValidation: ValidationSummary | null
  validationInProgress: boolean
}

export function useModelValidation() {
  const [status, setStatus] = useState<ValidationStatus | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastValidation, setLastValidation] = useState<ValidationSummary | null>(null)

  // Get validation status
  const getStatus = useCallback(async () => {
    try {
      const response = await fetch('/api/model-validation/status')
      const data = await response.json()
      
      if (data.success) {
        setStatus(data.status)
        if (data.status.lastValidation) {
          setLastValidation(data.status.lastValidation)
        }
        return data.status
      } else {
        throw new Error(data.error || 'Failed to get validation status')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('❌ Model validation status error:', errorMessage)
      return null
    }
  }, [])

  // Get available models from OpenRouter
  const getAvailableModels = useCallback(async () => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/model-validation/available-models')
      const data = await response.json()
      
      if (data.success) {
        return data.models
      } else {
        throw new Error(data.error || 'Failed to fetch available models')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('❌ Available models fetch error:', errorMessage)
      return []
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Validate a single model
  const validateModel = useCallback(async (modelId: string): Promise<ModelValidationResult | null> => {
    setError(null)

    try {
      const response = await fetch('/api/model-validation/validate-model', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ modelId }),
      })
      
      const data = await response.json()
      
      if (data.success) {
        return data.validation
      } else {
        throw new Error(data.error || 'Failed to validate model')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error(`❌ Model validation error for ${modelId}:`, errorMessage)
      return null
    }
  }, [])

  // Validate all models in a list
  const validateAllModels = useCallback(async (models: any[]): Promise<ValidationSummary | null> => {
    setIsLoading(true)
    setError(null)

    try {
      console.log(`🔍 Validating ${models.length} models...`)
      
      const response = await fetch('/api/model-validation/validate-all', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ models }),
      })
      
      const data = await response.json()
      
      if (data.success) {
        setLastValidation(data.validation)
        console.log(`✅ Validation complete: ${data.validation.validModels}/${data.validation.totalModels} models valid`)
        return data.validation
      } else {
        throw new Error(data.error || 'Failed to validate models')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('❌ Bulk model validation error:', errorMessage)
      return null
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Get suggestions for fixing invalid models
  const getSuggestions = useCallback(async (invalidModels: ModelValidationResult[]): Promise<ModelFixSuggestion[]> => {
    setError(null)

    try {
      const response = await fetch('/api/model-validation/suggestions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ invalidModels }),
      })
      
      const data = await response.json()
      
      if (data.success) {
        return data.suggestions
      } else {
        throw new Error(data.error || 'Failed to get suggestions')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('❌ Model suggestions error:', errorMessage)
      return []
    }
  }, [])

  // Auto-fix models by replacing invalid ones with valid alternatives
  const autoFixModels = useCallback(async (models: any[]): Promise<AutoFixResult | null> => {
    setIsLoading(true)
    setError(null)

    try {
      console.log(`🔧 Auto-fixing ${models.length} models...`)
      
      const response = await fetch('/api/model-validation/auto-fix', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ models }),
      })
      
      const data = await response.json()
      
      if (data.success) {
        if (data.fixResult.validation) {
          setLastValidation(data.fixResult.validation)
        }
        console.log(`✅ Auto-fix complete: ${data.fixResult.message}`)
        return data.fixResult
      } else {
        throw new Error(data.error || 'Failed to auto-fix models')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('❌ Model auto-fix error:', errorMessage)
      return null
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Clear validation cache
  const clearCache = useCallback(async (): Promise<boolean> => {
    try {
      const response = await fetch('/api/model-validation/clear-cache', {
        method: 'POST',
      })
      
      const data = await response.json()
      
      if (data.success) {
        console.log('🧹 Validation cache cleared')
        await getStatus() // Refresh status
        return true
      } else {
        throw new Error(data.error || 'Failed to clear cache')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('❌ Cache clear error:', errorMessage)
      return false
    }
  }, [getStatus])

  // Reinitialize validator
  const reinitialize = useCallback(async (apiKey?: string): Promise<boolean> => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/api/model-validation/reinitialize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ apiKey }),
      })
      
      const data = await response.json()
      
      if (data.success) {
        setStatus(data.status)
        console.log('✅ Model validator reinitialized')
        return true
      } else {
        throw new Error(data.error || 'Failed to reinitialize validator')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      console.error('❌ Validator reinitialize error:', errorMessage)
      return false
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Load status on mount
  useEffect(() => {
    getStatus()
  }, [getStatus])

  return {
    // State
    status,
    isLoading,
    error,
    lastValidation,
    
    // Actions
    getStatus,
    getAvailableModels,
    validateModel,
    validateAllModels,
    getSuggestions,
    autoFixModels,
    clearCache,
    reinitialize,
    
    // Helper computed properties
    isInitialized: status?.initialized ?? false,
    hasValidationResults: !!lastValidation,
    validationInProgress: status?.validationInProgress ?? false
  }
}