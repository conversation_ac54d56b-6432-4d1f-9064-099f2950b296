"use client"

import * as React from "react"
import { Toggle } from '@/components/ui/toggle'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { cn } from '@/lib/utils'

interface EnhancedCountryModeToggleProps {
  value: 'us' | 'australian'
  onChange: (value: 'us' | 'australian') => void
  variant?: 'toggle-group' | 'switch' | 'buttons'
  size?: 'sm' | 'default' | 'lg'
  className?: string
}

export function EnhancedCountryModeToggle({ 
  value, 
  onChange, 
  variant = 'toggle-group',
  size = 'default',
  className 
}: EnhancedCountryModeToggleProps) {
  
  if (variant === 'switch') {
    return (
      <div className={cn("flex items-center space-x-3", className)}>
        <div className="flex items-center gap-2">
          <span className="text-xl">🇺🇸</span>
          <Label htmlFor="country-switch" className="text-sm font-medium">
            US
          </Label>
        </div>
        <Switch
          id="country-switch"
          checked={value === 'australian'}
          onCheckedChange={(checked: boolean) => onChange(checked ? 'australian' : 'us')}
        />
        <div className="flex items-center gap-2">
          <Label htmlFor="country-switch" className="text-sm font-medium">
            AUS
          </Label>
          <span className="text-xl">🇦🇺</span>
        </div>
      </div>
    )
  }

  if (variant === 'toggle-group') {
    return (
      <div className={cn("flex items-center rounded-lg border p-1 bg-muted/50", className)}>
        <Toggle
          pressed={value === 'us'}
          onPressedChange={(pressed: boolean) => pressed && onChange('us')}
          variant="outline"
          size={size}
          className="flex items-center gap-2 data-[state=on]:bg-background data-[state=on]:shadow-sm"
          aria-label="Select US mode"
        >
          <span className="text-lg">🇺🇸</span>
          <span className="font-medium">US</span>
        </Toggle>
        <Toggle
          pressed={value === 'australian'}
          onPressedChange={(pressed: boolean) => pressed && onChange('australian')}
          variant="outline"
          size={size}
          className="flex items-center gap-2 data-[state=on]:bg-background data-[state=on]:shadow-sm"
          aria-label="Select Australian mode"
        >
          <span className="text-lg">🇦🇺</span>
          <span className="font-medium">AUS</span>
        </Toggle>
      </div>
    )
  }

  // Fallback to original button-based design
  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Toggle
        pressed={value === 'us'}
        onPressedChange={(pressed: boolean) => pressed && onChange('us')}
        variant={value === 'us' ? 'default' : 'outline'}
        size={size}
        className="flex items-center gap-2"
        aria-label="Select US mode"
      >
        <span className="text-xl">🇺🇸</span>
        <span>US</span>
      </Toggle>
      <Toggle
        pressed={value === 'australian'}
        onPressedChange={(pressed: boolean) => pressed && onChange('australian')}
        variant={value === 'australian' ? 'default' : 'outline'}
        size={size}
        className="flex items-center gap-2"
        aria-label="Select Australian mode"
      >
        <span className="text-xl">🇦🇺</span>
        <span>AUS</span>
      </Toggle>
    </div>
  )
}