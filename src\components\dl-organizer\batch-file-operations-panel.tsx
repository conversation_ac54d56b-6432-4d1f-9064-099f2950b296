'use client'

import React, { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Checkbox } from '@/components/ui/checkbox'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  FileText, 
  Edit3, 
  CheckCircle, 
  AlertTriangle, 
  Play, 
  Pause, 
  Square,
  Users,
  Info
} from 'lucide-react'
import { ImageFile } from '@/types'

interface BatchFileOperationsPanelProps {
  images: ImageFile[]
  onOperationComplete?: (operation: string, results: any[]) => void
  className?: string
}

interface BatchOperationResult {
  imageId: string
  imagePath: string
  success: boolean
  error?: string
  newPath?: string
  readySearchStatus?: string
}

export function BatchFileOperationsPanel({ 
  images, 
  onOperationComplete,
  className 
}: BatchFileOperationsPanelProps) {
  const [isProcessing, setIsProcessing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [currentOperation, setCurrentOperation] = useState<string | null>(null)
  const [results, setResults] = useState<BatchOperationResult[]>([])
  const [error, setError] = useState<string | null>(null)
  
  // Options
  const [includeReadySearchIndicator, setIncludeReadySearchIndicator] = useState(true)
  const [selectedOperation, setSelectedOperation] = useState<'save-txt' | 'rename'>('save-txt')

  const processableImages = images.filter(img => img && img.id)
  const canProcess = processableImages.length > 0 && !isProcessing

  const handleBatchSaveTXT = async () => {
    if (!canProcess) return

    setIsProcessing(true)
    setCurrentOperation('Saving TXT files')
    setProgress(0)
    setError(null)
    setResults([])

    try {
      const imageIds = processableImages.map(img => img.id)
      
      const response = await fetch('http://localhost:3003/api/file-operations/batch-save-txt', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ imageIds })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to batch save TXT files')
      }

      const result = await response.json()
      setResults(result.results || [])
      setProgress(100)
      
      onOperationComplete?.('batch-save-txt', result.results)
      
      setCurrentOperation(`Completed: ${result.successCount}/${result.processedCount} files saved`)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Batch TXT saving failed')
    } finally {
      setIsProcessing(false)
    }
  }

  const handleBatchRename = async () => {
    if (!canProcess) return

    setIsProcessing(true)
    setCurrentOperation('Renaming images')
    setProgress(0)
    setError(null)
    setResults([])

    try {
      // Prepare rename data - use existing filename as base
      const renameData = processableImages.map(img => ({
        imageId: img.id,
        newBaseName: img.filename.replace(/\.[^/.]+$/, '').replace(/-(rs-m|rs-nm|no-rs)$/i, '')
      }))
      
      const response = await fetch('http://localhost:3003/api/file-operations/batch-rename-with-rs-indicator', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          renameData,
          includeReadySearchIndicator 
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to batch rename images')
      }

      const result = await response.json()
      setResults(result.results || [])
      setProgress(100)
      
      onOperationComplete?.('batch-rename', result.results)
      
      setCurrentOperation(`Completed: ${result.successCount}/${result.processedCount} files renamed`)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Batch rename failed')
    } finally {
      setIsProcessing(false)
    }
  }

  const getStatusIcon = (success: boolean) => {
    return success 
      ? <CheckCircle className="h-4 w-4 text-green-500" />
      : <AlertTriangle className="h-4 w-4 text-red-500" />
  }

  const getReadySearchBadge = (status?: string) => {
    switch (status) {
      case 'rs-m':
        return <Badge variant="success" className="text-xs">Matches</Badge>
      case 'rs-nm':
        return <Badge variant="warning" className="text-xs">No matches</Badge>
      case 'no-data':
      case 'no-rs':
      default:
        return <Badge variant="outline" className="text-xs">No RS</Badge>
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Batch File Operations
        </CardTitle>
        <CardDescription>
          Process multiple images for TXT saving and renaming with ReadySearch indicators
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* Statistics */}
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-foreground">{processableImages.length}</div>
            <div className="text-sm text-muted-foreground">Selected Images</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{results.filter(r => r.success).length}</div>
            <div className="text-sm text-muted-foreground">Processed</div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Progress Display */}
        {isProcessing && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>{currentOperation}</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="w-full" />
          </div>
        )}

        {/* Operation Selection */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium">Select Operation</h4>
          
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="save-txt"
                checked={selectedOperation === 'save-txt'}
                onCheckedChange={(checked) => checked && setSelectedOperation('save-txt')}
              />
              <Label htmlFor="save-txt" className="flex-1">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  <span>Batch Save TXT Files</span>
                </div>
                <div className="text-xs text-muted-foreground ml-6">
                  Save standardized TXT files with OCR and ReadySearch results
                </div>
              </Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="rename"
                checked={selectedOperation === 'rename'}
                onCheckedChange={(checked) => checked && setSelectedOperation('rename')}
              />
              <Label htmlFor="rename" className="flex-1">
                <div className="flex items-center gap-2">
                  <Edit3 className="h-4 w-4" />
                  <span>Batch Rename with ReadySearch Indicators</span>
                </div>
                <div className="text-xs text-muted-foreground ml-6">
                  Rename files to include rs-m (matches) or rs-nm (no matches) indicators
                </div>
              </Label>
            </div>
          </div>

          {selectedOperation === 'rename' && (
            <div className="ml-6 space-y-2">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="include-rs-indicator"
                  checked={includeReadySearchIndicator}
                  onCheckedChange={(checked) => setIncludeReadySearchIndicator(!!checked)}
                />
                <Label htmlFor="include-rs-indicator" className="text-sm">
                  Include ReadySearch indicators in filenames
                </Label>
              </div>
              
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription className="text-xs">
                  <div><strong>Indicators:</strong></div>
                  <div>• rs-m = ReadySearch matches found</div>
                  <div>• rs-nm = ReadySearch processed, no matches</div>
                  <div>• Files without ReadySearch data remain unchanged</div>
                </AlertDescription>
              </Alert>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button
            onClick={selectedOperation === 'save-txt' ? handleBatchSaveTXT : handleBatchRename}
            disabled={!canProcess}
            className="flex-1"
          >
            {isProcessing ? (
              <>
                <Pause className="h-4 w-4 mr-2" />
                Processing...
              </>
            ) : (
              <>
                <Play className="h-4 w-4 mr-2" />
                {selectedOperation === 'save-txt' ? 'Start Batch TXT Save' : 'Start Batch Rename'}
              </>
            )}
          </Button>
          
          {isProcessing && (
            <Button
              variant="destructive"
              onClick={() => {
                setIsProcessing(false)
                setCurrentOperation(null)
                setProgress(0)
              }}
            >
              <Square className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Results Display */}
        {results.length > 0 && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">Operation Results</h4>
              <div className="flex gap-2">
                <Badge variant="default">{results.filter(r => r.success).length} Success</Badge>
                <Badge variant="destructive">{results.filter(r => !r.success).length} Errors</Badge>
              </div>
            </div>

            <ScrollArea className="max-h-64">
              <div className="space-y-2">
                {results.map((result, index) => (
                  <div 
                    key={result.imageId} 
                    className="flex items-center justify-between p-3 border rounded-lg bg-card"
                  >
                    <div className="flex items-center gap-3">
                      {getStatusIcon(result.success)}
                      <div>
                        <div className="text-sm font-medium">
                          {result.imagePath || `Image ${index + 1}`}
                        </div>
                        {result.error && (
                          <div className="text-xs text-red-600 mt-1">{result.error}</div>
                        )}
                        {result.newPath && (
                          <div className="text-xs text-green-600 mt-1">
                            → {result.newPath}
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {result.readySearchStatus && getReadySearchBadge(result.readySearchStatus)}
                      {result.success ? (
                        <Badge variant="default" className="text-xs">Success</Badge>
                      ) : (
                        <Badge variant="destructive" className="text-xs">Error</Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </div>
        )}

        {/* Summary */}
        {!isProcessing && results.length > 0 && (
          <div className="p-4 bg-muted/50 rounded-lg">
            <h4 className="text-sm font-medium mb-2">Operation Summary</h4>
            <p className="text-sm text-muted-foreground">
              Processed {results.length} images. 
              {results.filter(r => r.success).length > 0 && ` ${results.filter(r => r.success).length} successful operations.`}
              {results.filter(r => !r.success).length > 0 && ` ${results.filter(r => !r.success).length} errors encountered.`}
            </p>
            {selectedOperation === 'save-txt' && (
              <p className="text-xs text-muted-foreground mt-1">
                TXT files include OCR data and ReadySearch results (if available).
              </p>
            )}
            {selectedOperation === 'rename' && (
              <p className="text-xs text-muted-foreground mt-1">
                Files were renamed with ReadySearch indicators based on analysis results.
              </p>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

export default BatchFileOperationsPanel