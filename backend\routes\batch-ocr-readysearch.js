const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');
const OCRService = require('../services/ocr-service');
const { spawn } = require('child_process');
const fsExistsSync = require('fs').existsSync;

// Helper function to resolve ReadySearch directory
function resolveReadySearchDir() {
  const candidates = [
    path.join(__dirname, '..', '..', 'ReadySearch'), // project root/ReadySearch
    path.join(process.cwd(), 'ReadySearch'),         // working dir
    path.join(__dirname, '..', 'ReadySearch')        // backend/ReadySearch (unlikely)
  ];
  for (const dir of candidates) {
    if (fsExistsSync(dir)) return dir;
  }
  return candidates[0];
}

// Execute ReadySearch automation
async function executeReadySearch(searchQuery) {
  return new Promise((resolve, reject) => {
    const readySearchDir = resolveReadySearchDir();
    const scriptPath = path.join(readySearchDir, 'readysearch_cli.py');
    
    console.log(`Executing ReadySearch: ${scriptPath} "${searchQuery}"`);
    
    const pythonProcess = spawn('python', [scriptPath, searchQuery], {
      cwd: readySearchDir,
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    let output = '';
    let errorOutput = '';
    
    pythonProcess.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    pythonProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    pythonProcess.on('close', (code) => {
      if (code === 0) {
        try {
          // Parse the JSON output from ReadySearch
          const result = JSON.parse(output.trim());
          resolve(result);
        } catch (parseError) {
          resolve({
            success: false,
            error: 'Failed to parse ReadySearch output',
            rawOutput: output
          });
        }
      } else {
        reject(new Error(`ReadySearch failed with code ${code}: ${errorOutput}`));
      }
    });
    
    pythonProcess.on('error', (error) => {
      reject(new Error(`Failed to spawn ReadySearch process: ${error.message}`));
    });
  });
}

// Exponential backoff helper for rate limit handling
async function withExponentialBackoff(fn, maxRetries = 3, baseDelay = 1000) {
  let attempt = 0;
  
  while (attempt < maxRetries) {
    try {
      const result = await fn();
      return result;
    } catch (error) {
      // Check if this is a rate limit error
      const isRateLimit = error.status === 429 || 
                         (error.response && error.response.status === 429) ||
                         error.message.includes('rate limit') ||
                         error.message.includes('429');
      
      if (!isRateLimit || attempt === maxRetries - 1) {
        throw error; // Not a rate limit error, or final attempt
      }
      
      // Calculate exponential backoff delay: baseDelay * 2^attempt + jitter
      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
      console.log(`Rate limit hit, retrying in ${Math.round(delay)}ms (attempt ${attempt + 1}/${maxRetries})`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
      attempt++;
    }
  }
}

// Initialize OCR service
const ocrService = new OCRService();

// Sequential batch processing with OCR + ReadySearch integration
router.post('/process-sequential', async (req, res) => {
  try {
    const { 
      images, 
      modelId = 'gpt-4o-mini', 
      extractionType = 'auto_detect', 
      mode = 'us', 
      exportFormats = ['json', 'txt'],
      readySearchOptions = {
        enabled: false,
        useFullGivenNames: false,
        includeBirthYear: true
      }
    } = req.body;
    
    if (!images || !Array.isArray(images) || images.length === 0) {
      return res.status(400).json({ error: 'images array is required' });
    }

    console.log(`Starting sequential OCR + ReadySearch processing for ${images.length} images`);
    
    const results = [];
    const processingStats = {
      totalImages: images.length,
      processedImages: 0,
      successfulExtractions: 0,
      failedExtractions: 0,
      readySearchProcessed: 0,
      readySearchFailed: 0,
      savedFiles: 0
    };

    // Set up SSE for progress tracking
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // Send initial status
    res.write(`data: ${JSON.stringify({
      type: 'progress',
      data: {
        currentStep: 'initializing',
        progress: 0,
        stats: processingStats
      }
    })}\n\n`);

    // Process each image sequentially
    for (let i = 0; i < images.length; i++) {
      const image = images[i];
      
      try {
        processingStats.processedImages++;
        
        // Send progress update
        res.write(`data: ${JSON.stringify({
          type: 'progress',
          data: {
            currentStep: 'ocr',
            currentImage: image.filename,
            progress: Math.round((i / images.length) * 100),
            stats: processingStats
          }
        })}\n\n`);
        
        // Step 1: Perform OCR
        const imagePath = Buffer.from(image.id, 'base64').toString('utf-8');
        const imageBuffer = await fs.readFile(imagePath);
        
        const ocrResult = await withExponentialBackoff(async () => {
          return await ocrService.processImage(imageBuffer, {
            modelId,
            extractionType,
            mode,
            cardSide: null,
            forceRefresh: false
          });
        });
        
        if (ocrResult && ocrResult.success) {
          processingStats.successfulExtractions++;
          const ocrData = ocrResult.result || ocrResult.data;
          
          let readySearchResult = null;
          
          // Step 2: ReadySearch (if enabled and Australian mode)
          if (readySearchOptions.enabled && mode === 'australian' && ocrData.firstName && ocrData.lastName) {
            try {
              res.write(`data: ${JSON.stringify({
                type: 'progress',
                data: {
                  currentStep: 'readysearch',
                  currentImage: image.filename,
                  progress: Math.round(((i + 0.5) / images.length) * 100),
                  stats: processingStats
                }
              })}\n\n`);
              
              // Format search query based on options
              let searchQuery;
              if (readySearchOptions.includeBirthYear && ocrData.dateOfBirth) {
                const year = new Date(ocrData.dateOfBirth).getFullYear();
                searchQuery = `${ocrData.firstName} ${ocrData.lastName},${year}`;
              } else {
                searchQuery = `${ocrData.firstName} ${ocrData.lastName}`;
              }
              
              readySearchResult = await executeReadySearch(searchQuery);
              processingStats.readySearchProcessed++;
              
            } catch (readySearchError) {
              console.error(`ReadySearch failed for ${image.filename}:`, readySearchError);
              processingStats.readySearchFailed++;
              readySearchResult = {
                success: false,
                error: readySearchError.message
              };
            }
          }
          
          // Step 3: Save files
          res.write(`data: ${JSON.stringify({
            type: 'progress',
            data: {
              currentStep: 'saving',
              currentImage: image.filename,
              progress: Math.round(((i + 0.8) / images.length) * 100),
              stats: processingStats
            }
          })}\n\n`);
          
          const basePath = path.dirname(imagePath);
          const basename = path.basename(imagePath, path.extname(imagePath));
          const savedFiles = [];
          
          // Combine OCR and ReadySearch data
          const combinedData = {
            ...ocrData,
            readySearch: readySearchResult,
            processedAt: new Date().toISOString(),
            modelUsed: modelId,
            extractionType,
            mode
          };
          
          if (exportFormats.includes('json')) {
            const jsonPath = path.join(basePath, `${basename}_ocr_results.json`);
            await fs.writeFile(
              jsonPath,
              JSON.stringify(combinedData, null, 2),
              'utf8'
            );
            savedFiles.push(jsonPath);
            processingStats.savedFiles++;
          }
          
          if (exportFormats.includes('txt')) {
            const txtContent = formatCombinedResultAsText(combinedData, extractionType);
            const txtPath = path.join(basePath, `${basename}_ocr_results.txt`);
            await fs.writeFile(
              txtPath,
              txtContent,
              'utf8'
            );
            savedFiles.push(txtPath);
            processingStats.savedFiles++;
          }
          
          results.push({
            success: true,
            imagePath: imagePath,
            imageId: image.id,
            filename: image.filename,
            documentType: ocrData.documentType || extractionType,
            ocrData: ocrData,
            readySearchResult: readySearchResult,
            savedFiles
          });
          
        } else {
          processingStats.failedExtractions++;
          results.push({
            success: false,
            imagePath: imagePath,
            imageId: image.id,
            filename: image.filename,
            error: ocrResult?.error || 'OCR processing failed'
          });
        }
        
      } catch (error) {
        processingStats.failedExtractions++;
        console.error(`Error processing image ${image.filename}:`, error);
        results.push({
          success: false,
          imagePath: Buffer.from(image.id, 'base64').toString('utf-8'),
          imageId: image.id,
          filename: image.filename,
          error: error.message
        });
      }
    }
    
    // Send completion
    res.write(`data: ${JSON.stringify({
      type: 'complete',
      data: {
        results,
        stats: processingStats,
        message: `Processed ${processingStats.processedImages} images with ${processingStats.successfulExtractions} successful extractions and ${processingStats.readySearchProcessed} ReadySearch queries`
      }
    })}\n\n`);
    
    res.end();
    
  } catch (error) {
    console.error('Sequential batch processing error:', error);
    res.write(`data: ${JSON.stringify({
      type: 'error',
      data: {
        error: 'Failed to process sequential batch',
        details: error.message
      }
    })}\n\n`);
    res.end();
  }
});

// Helper function to format combined OCR + ReadySearch result as text
function formatCombinedResultAsText(combinedData, documentType) {
  const lines = [];
  
  lines.push('=== OCR EXTRACTION RESULTS ===');
  lines.push(`Extraction Date: ${combinedData.processedAt}`);
  lines.push(`Document Type: ${documentType}`);
  lines.push(`Model Used: ${combinedData.modelUsed}`);
  lines.push('');
  
  if (documentType.includes('driver_license')) {
    lines.push('Driver License Information:');
    lines.push(`Name: ${combinedData.firstName || ''} ${combinedData.lastName || ''}`);
    if (combinedData.dateOfBirth) lines.push(`Date of Birth: ${combinedData.dateOfBirth}`);
    if (combinedData.licenseNumber) lines.push(`License Number: ${combinedData.licenseNumber}`);
    if (combinedData.cardNumber) lines.push(`Card Number: ${combinedData.cardNumber}`);
    if (combinedData.expirationDate) lines.push(`Expiration Date: ${combinedData.expirationDate}`);
    if (combinedData.address) lines.push(`Address: ${combinedData.address}`);
    if (combinedData.state) lines.push(`State: ${combinedData.state}`);
  }
  
  if (combinedData.confidence) {
    lines.push('');
    lines.push(`Confidence Score: ${(combinedData.confidence * 100).toFixed(1)}%`);
  }
  
  // Add ReadySearch results if available
  if (combinedData.readySearch) {
    lines.push('');
    lines.push('=== READYSEARCH RESULTS ===');
    if (combinedData.readySearch.success) {
      lines.push('Search Status: SUCCESS');
      if (combinedData.readySearch.matches) {
        lines.push(`Found ${combinedData.readySearch.matches.length} matches`);
        combinedData.readySearch.matches.forEach((match, index) => {
          lines.push(`Match ${index + 1}:`);
          Object.entries(match).forEach(([key, value]) => {
            lines.push(`  ${key}: ${value}`);
          });
        });
      }
    } else {
      lines.push('Search Status: FAILED');
      lines.push(`Error: ${combinedData.readySearch.error}`);
    }
  }
  
  if (combinedData.rawText) {
    lines.push('');
    lines.push('=== RAW TEXT ===');
    lines.push(combinedData.rawText);
  }
  
  return lines.join('\n');
}

module.exports = router;