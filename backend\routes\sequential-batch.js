const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');
const EventEmitter = require('events');
const fetch = require('node-fetch'); // For internal API calls
const logger = require('../utils/logger');
const SecurityMiddleware = require('../middleware/security');
const ErrorHandler = require('../middleware/error-handler');
const OCRService = require('../services/ocr-service');

// Job management for sequential batch processing
class SequentialBatchManager extends EventEmitter {
  constructor() {
    super();
    this.jobs = new Map();
    this.activeJobs = 0;
    this.maxConcurrentJobs = 2; // Lower than OCR-only batch to prevent overload
  }

  createJob(options) {
    const job = {
      id: this.generateJobId(),
      ...options,
      status: 'pending',
      progress: {
        phase: 'pending', // pending, ocr, readysearch, saving, completed, failed
        currentStep: 0,
        totalSteps: 0,
        currentImage: '',
        ocrCompleted: 0,
        ocrFailed: 0,
        readySearchCompleted: 0,
        readySearchFailed: 0,
        filesSaved: 0
      },
      startTime: null,
      endTime: null,
      results: [],
      errors: []
    };

    this.jobs.set(job.id, job);
    return job;
  }

  generateJobId() {
    return `seq_batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  getJob(jobId) {
    return this.jobs.get(jobId);
  }

  deleteJob(jobId) {
    const job = this.jobs.get(jobId);
    if (job && job.status !== 'processing') {
      this.jobs.delete(jobId);
      return true;
    }
    return false;
  }

  updateJobProgress(jobId, updates) {
    const job = this.jobs.get(jobId);
    if (job) {
      Object.assign(job.progress, updates);
      this.emit('progress', job);
    }
  }

  async processJob(job) {
    if (this.activeJobs >= this.maxConcurrentJobs) {
      throw new Error('Maximum concurrent jobs reached');
    }

    this.activeJobs++;
    
    try {
      job.status = 'processing';
      job.startTime = new Date().toISOString();
      job.progress.totalSteps = job.images.length * (job.includeReadySearch ? 2 : 1);
      
      this.emit('started', job);
      this.updateJobProgress(job.id, { 
        phase: 'ocr', 
        currentImage: 'Starting OCR processing...' 
      });

      // Initialize OCR service
      const ocrService = new OCRService();

      // Phase 1: OCR Processing
      await this.processOCRPhase(job, ocrService);

      // Phase 2: ReadySearch Processing (if enabled)
      if (job.includeReadySearch && job.results.length > 0) {
        await this.processReadySearchPhase(job);
      }

      // Phase 3: File Saving
      await this.processSavingPhase(job);

      // Complete job
      job.status = 'completed';
      job.endTime = new Date().toISOString();
      job.progress.phase = 'completed';
      job.progress.currentImage = `✅ Sequential processing completed! ${job.results.length} images processed.`;
      
      this.emit('completed', job);

    } catch (error) {
      job.status = 'failed';
      job.endTime = new Date().toISOString();
      job.progress.phase = 'failed';
      job.progress.currentImage = `❌ Processing failed: ${error.message}`;
      job.errors.push({ phase: 'general', error: error.message });
      
      this.emit('failed', job);
      throw error;
    } finally {
      this.activeJobs--;
    }
  }

  async processOCRPhase(job, ocrService) {
    logger.info(`Starting OCR phase for job ${job.id}`, { imageCount: job.images.length });
    
    for (let i = 0; i < job.images.length; i++) {
      const image = job.images[i];
      
      this.updateJobProgress(job.id, {
        currentStep: i + 1,
        currentImage: `🔍 OCR Processing: ${image.filename || 'Unknown'}`
      });

      try {
        // Read image file
        const imagePath = Buffer.from(image.id, 'base64').toString('utf-8');
        const imageBuffer = await fs.readFile(imagePath);

        // Process with OCR
        const ocrResult = await ocrService.processImage(imageBuffer, {
          modelId: job.modelId || 'gpt-4o-mini',
          extractionType: job.extractionType || 'aus_driver_license',
          mode: job.mode || 'australian'
        });

        if (ocrResult && ocrResult.success) {
          job.results.push({
            imageId: image.id,
            imagePath: imagePath,
            filename: image.filename,
            ocrData: ocrResult.result || ocrResult.data,
            ocrSuccess: true,
            phase: 'ocr_completed'
          });
          
          job.progress.ocrCompleted++;
          logger.info(`OCR success for ${image.filename}`, { jobId: job.id });
        } else {
          job.errors.push({
            imageId: image.id,
            filename: image.filename,
            phase: 'ocr',
            error: ocrResult?.error || 'OCR processing failed'
          });
          job.progress.ocrFailed++;
          logger.warn(`OCR failed for ${image.filename}`, { jobId: job.id, error: ocrResult?.error });
        }

        // Brief pause between images for UX
        await new Promise(resolve => setTimeout(resolve, 300));

      } catch (error) {
        job.errors.push({
          imageId: image.id,
          filename: image.filename,
          phase: 'ocr',
          error: error.message
        });
        job.progress.ocrFailed++;
        logger.error(`OCR error for ${image.filename}`, { jobId: job.id, error: error.message });
      }
    }

    logger.info(`OCR phase completed for job ${job.id}`, { 
      successful: job.progress.ocrCompleted, 
      failed: job.progress.ocrFailed 
    });
  }

  async processReadySearchPhase(job) {
    logger.info(`Starting ReadySearch phase for job ${job.id}`);
    
    this.updateJobProgress(job.id, {
      phase: 'readysearch',
      currentImage: '🔍 Preparing ReadySearch processing...'
    });

    // Filter results that are eligible for ReadySearch
    const eligibleResults = job.results.filter(result => 
      result.ocrSuccess && 
      result.ocrData.firstName && 
      result.ocrData.lastName && 
      result.ocrData.dateOfBirth
    );

    if (eligibleResults.length === 0) {
      this.updateJobProgress(job.id, {
        currentImage: '⚠️ No eligible OCR results for ReadySearch processing'
      });
      return;
    }

    // Prepare search data for ReadySearch batch API
    const searchData = eligibleResults.map(result => {
      const yearMatch = result.ocrData.dateOfBirth?.match(/\d{4}/);
      const yearOfBirth = yearMatch ? yearMatch[0] : '';

      let firstName = result.ocrData.firstName;
      let lastName = result.ocrData.lastName;

      // Apply ReadySearch options
      if (!job.readySearchOptions?.useFullGivenNames) {
        firstName = firstName.split(' ')[0];
      }

      return {
        firstName,
        lastName,
        yearOfBirth: job.readySearchOptions?.includeBirthYear ? yearOfBirth : '',
        imageId: result.imageId,
        cardSide: result.ocrData.cardSide || 'front'
      };
    });

    this.updateJobProgress(job.id, {
      currentImage: `🔍 ReadySearch: Processing ${eligibleResults.length} eligible images...`
    });

    try {
      // Call ReadySearch batch API
      const readySearchResponse = await this.callReadySearchBatch(searchData);
      
      if (readySearchResponse.success) {
        // Update results with ReadySearch data
        for (let i = 0; i < eligibleResults.length; i++) {
          const result = eligibleResults[i];
          const readySearchResult = readySearchResponse.data.results.find(r => r.imageId === result.imageId);
          
          if (readySearchResult && readySearchResult.success) {
            result.readySearchData = readySearchResult.results;
            result.readySearchSuccess = true;
            job.progress.readySearchCompleted++;
          } else {
            result.readySearchSuccess = false;
            job.progress.readySearchFailed++;
            job.errors.push({
              imageId: result.imageId,
              filename: result.filename,
              phase: 'readysearch',
              error: readySearchResult?.error || 'ReadySearch processing failed'
            });
          }
        }

        this.updateJobProgress(job.id, {
          currentImage: `✅ ReadySearch: ${job.progress.readySearchCompleted} completed, ${job.progress.readySearchFailed} failed`
        });

      } else {
        throw new Error(readySearchResponse.error || 'ReadySearch batch processing failed');
      }

    } catch (error) {
      // Mark all eligible results as ReadySearch failed
      eligibleResults.forEach(result => {
        result.readySearchSuccess = false;
        job.progress.readySearchFailed++;
      });

      job.errors.push({
        phase: 'readysearch',
        error: error.message
      });

      this.updateJobProgress(job.id, {
        currentImage: `❌ ReadySearch failed: ${error.message}`
      });

      logger.error(`ReadySearch phase failed for job ${job.id}`, { error: error.message });
    }
  }

  async processSavingPhase(job) {
    logger.info(`Starting file saving phase for job ${job.id}`);
    
    this.updateJobProgress(job.id, {
      phase: 'saving',
      currentImage: '💾 Saving results to files...'
    });

    for (const result of job.results) {
      try {
        const basePath = path.dirname(result.imagePath);
        const basename = path.basename(result.imagePath, path.extname(result.imagePath));
        let savedCount = 0;

        // Save OCR results if successful
        if (result.ocrSuccess && result.ocrData) {
          if (job.exportFormats.includes('json')) {
            const jsonPath = path.join(basePath, `${basename}_ocr_results.json`);
            const jsonData = {
              ...result.ocrData,
              imagePath: result.imagePath,
              processedAt: new Date().toISOString(),
              modelUsed: job.modelId,
              extractionType: job.extractionType,
              mode: job.mode
            };

            // Add ReadySearch results if available
            if (result.readySearchSuccess && result.readySearchData) {
              jsonData.readySearchResults = {
                ...result.readySearchData,
                timestamp: new Date().toISOString(),
                imageId: result.imageId
              };
            }

            await fs.writeFile(jsonPath, JSON.stringify(jsonData, null, 2));
            savedCount++;
          }

          if (job.exportFormats.includes('txt')) {
            const txtPath = path.join(basePath, `${basename}_ocr_results.txt`);
            const txtContent = this.generateTextReport(result, job);
            await fs.writeFile(txtPath, txtContent);
            savedCount++;
          }
        }

        job.progress.filesSaved += savedCount;
        
      } catch (error) {
        job.errors.push({
          imageId: result.imageId,
          filename: result.filename,
          phase: 'saving',
          error: error.message
        });
        logger.error(`File saving failed for ${result.filename}`, { jobId: job.id, error: error.message });
      }
    }

    this.updateJobProgress(job.id, {
      currentImage: `💾 Saved ${job.progress.filesSaved} files`
    });
  }

  generateTextReport(result, job) {
    const lines = [];
    lines.push('=== SEQUENTIAL BATCH OCR + READYSEARCH RESULTS ===');
    lines.push(`Processing Date: ${new Date().toISOString()}`);
    lines.push(`Image: ${result.filename}`);
    lines.push('');

    // OCR Results
    if (result.ocrSuccess && result.ocrData) {
      lines.push('OCR EXTRACTION:');
      lines.push(`Name: ${result.ocrData.firstName || ''} ${result.ocrData.lastName || ''}`);
      if (result.ocrData.dateOfBirth) lines.push(`Date of Birth: ${result.ocrData.dateOfBirth}`);
      if (result.ocrData.licenseNumber) lines.push(`License Number: ${result.ocrData.licenseNumber}`);
      if (result.ocrData.expirationDate) lines.push(`Expiration Date: ${result.ocrData.expirationDate}`);
      if (result.ocrData.address) lines.push(`Address: ${result.ocrData.address}`);
      if (result.ocrData.state) lines.push(`State: ${result.ocrData.state}`);
      
      if (result.ocrData.confidence) {
        lines.push(`OCR Confidence: ${(result.ocrData.confidence * 100).toFixed(1)}%`);
      }
    } else {
      lines.push('OCR EXTRACTION: FAILED');
    }

    lines.push('');

    // ReadySearch Results
    if (result.readySearchSuccess && result.readySearchData) {
      lines.push('READYSEARCH RESULTS:');
      lines.push(`Search Status: SUCCESS`);
      lines.push(`Matches Found: ${result.readySearchData.matches?.length || 0}`);
      
      if (result.readySearchData.matches && result.readySearchData.matches.length > 0) {
        lines.push('');
        lines.push('MATCHES:');
        result.readySearchData.matches.forEach((match, index) => {
          lines.push(`${index + 1}. ${match.name} (${match.matchType})`);
        });
      }
      
      if (result.readySearchData.summary) {
        lines.push('');
        lines.push(`Summary: ${result.readySearchData.summary}`);
      }
    } else if (job.includeReadySearch) {
      lines.push('READYSEARCH RESULTS: FAILED OR NOT ELIGIBLE');
    }

    lines.push('');
    lines.push('='.repeat(50));

    if (result.ocrData?.rawText) {
      lines.push('');
      lines.push('RAW OCR TEXT:');
      lines.push(result.ocrData.rawText);
    }

    return lines.join('\n');
  }

  async callReadySearchBatch(searchData) {
    try {
      logger.info('Calling ReadySearch batch API internally', { searchDataCount: searchData.length });

      // Make HTTP request to our own ReadySearch batch endpoint
      const { getBackendPort } = require('../../scripts/port-config.js');
      const backendPort = getBackendPort();
      const backendUrl = `http://localhost:${backendPort}`;
      
      const response = await fetch(`${backendUrl}/api/readysearch/batch`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'DL-Organizer-Sequential-Batch/1.0'
        },
        body: JSON.stringify({ searchData })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`ReadySearch API responded with ${response.status}: ${errorText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        logger.info('ReadySearch batch completed successfully', { 
          processedCount: result.data.processedCount,
          skippedCount: result.data.skippedCount 
        });
        
        return {
          success: true,
          data: result.data
        };
      } else {
        throw new Error(result.error || 'ReadySearch batch processing failed');
      }

    } catch (error) {
      logger.error('ReadySearch batch call failed', { 
        error: error.message,
        searchDataCount: searchData.length 
      });
      
      return {
        success: false,
        error: error.message,
        data: {
          processedCount: 0,
          skippedCount: searchData.length,
          results: searchData.map(item => ({
            imageId: item.imageId,
            success: false,
            error: 'ReadySearch processing failed',
            results: {
              matches: [],
              summary: 'ReadySearch failed due to internal error',
              hasMatches: false
            }
          }))
        }
      };
    }
  }
}

// Create singleton manager
const batchManager = new SequentialBatchManager();

// Sequential batch processing endpoint
router.post('/process', SecurityMiddleware.validateRequestBody(['images']), ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { 
      images, 
      modelId = 'gpt-4o-mini', 
      extractionType = 'aus_driver_license',
      mode = 'australian',
      includeReadySearch = false,
      readySearchOptions = {},
      exportFormats = ['json', 'txt']
    } = req.body;

    if (!images || !Array.isArray(images) || images.length === 0) {
      return res.status(400).json({ 
        success: false, 
        error: 'images array is required' 
      });
    }

    logger.info(`Starting sequential batch processing`, { 
      imageCount: images.length,
      includeReadySearch,
      modelId 
    });

    // Create job
    const job = batchManager.createJob({
      images,
      modelId,
      extractionType,
      mode,
      includeReadySearch,
      readySearchOptions,
      exportFormats
    });

    // Start processing asynchronously
    batchManager.processJob(job).catch(error => {
      logger.error(`Sequential batch job ${job.id} failed`, { error: error.message });
    });

    res.json({
      success: true,
      jobId: job.id,
      message: `Sequential batch processing started for ${images.length} images`
    });

  } catch (error) {
    logger.error('Sequential batch processing error', { error: error.message });
    res.status(500).json({
      error: 'Failed to start sequential batch processing',
      details: error.message
    });
  }
}));

// Get job status
router.get('/status/:jobId', ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { jobId } = req.params;
    const job = batchManager.getJob(jobId);

    if (!job) {
      return res.status(404).json({
        success: false,
        error: 'Job not found'
      });
    }

    res.json({
      success: true,
      job: {
        id: job.id,
        status: job.status,
        progress: job.progress,
        startTime: job.startTime,
        endTime: job.endTime,
        resultCount: job.results.length,
        errorCount: job.errors.length
      }
    });

  } catch (error) {
    logger.error('Error getting job status', { error: error.message });
    res.status(500).json({
      error: 'Failed to get job status',
      details: error.message
    });
  }
}));

// Get job results
router.get('/results/:jobId', ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { jobId } = req.params;
    const job = batchManager.getJob(jobId);

    if (!job) {
      return res.status(404).json({
        success: false,
        error: 'Job not found'
      });
    }

    res.json({
      success: true,
      job: {
        id: job.id,
        status: job.status,
        progress: job.progress,
        startTime: job.startTime,
        endTime: job.endTime,
        results: job.results,
        errors: job.errors,
        summary: {
          totalImages: job.images.length,
          ocrCompleted: job.progress.ocrCompleted,
          ocrFailed: job.progress.ocrFailed,
          readySearchCompleted: job.progress.readySearchCompleted,
          readySearchFailed: job.progress.readySearchFailed,
          filesSaved: job.progress.filesSaved
        }
      }
    });

  } catch (error) {
    logger.error('Error getting job results', { error: error.message });
    res.status(500).json({
      error: 'Failed to get job results',
      details: error.message
    });
  }
}));

// Server-Sent Events for real-time progress
router.get('/stream/:jobId', (req, res) => {
  try {
    const { jobId } = req.params;
    const job = batchManager.getJob(jobId);

    if (!job) {
      return res.status(404).json({
        error: 'Job not found'
      });
    }

    // Set up SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control'
    });

    // Send initial status
    res.write(`data: ${JSON.stringify({
      type: 'status',
      job: {
        id: job.id,
        status: job.status,
        progress: job.progress
      }
    })}\n\n`);

    // Listen for progress updates
    const onProgress = (updatedJob) => {
      if (updatedJob.id === jobId) {
        res.write(`data: ${JSON.stringify({
          type: 'progress',
          job: {
            id: updatedJob.id,
            status: updatedJob.status,
            progress: updatedJob.progress
          }
        })}\n\n`);
      }
    };

    const onCompleted = (updatedJob) => {
      if (updatedJob.id === jobId) {
        res.write(`data: ${JSON.stringify({
          type: 'completed',
          job: {
            id: updatedJob.id,
            status: updatedJob.status,
            progress: updatedJob.progress,
            summary: {
              totalImages: updatedJob.images.length,
              ocrCompleted: updatedJob.progress.ocrCompleted,
              ocrFailed: updatedJob.progress.ocrFailed,
              readySearchCompleted: updatedJob.progress.readySearchCompleted,
              readySearchFailed: updatedJob.progress.readySearchFailed,
              filesSaved: updatedJob.progress.filesSaved
            }
          }
        })}\n\n`);
        res.end();
      }
    };

    const onFailed = (updatedJob) => {
      if (updatedJob.id === jobId) {
        res.write(`data: ${JSON.stringify({
          type: 'failed',
          job: {
            id: updatedJob.id,
            status: updatedJob.status,
            progress: updatedJob.progress,
            errors: updatedJob.errors
          }
        })}\n\n`);
        res.end();
      }
    };

    batchManager.on('progress', onProgress);
    batchManager.on('completed', onCompleted);
    batchManager.on('failed', onFailed);

    // Clean up on client disconnect
    req.on('close', () => {
      batchManager.removeListener('progress', onProgress);
      batchManager.removeListener('completed', onCompleted);
      batchManager.removeListener('failed', onFailed);
    });

  } catch (error) {
    logger.error('Error setting up SSE stream', { error: error.message });
    res.status(500).json({
      error: 'Failed to set up progress stream',
      details: error.message
    });
  }
});

// Cancel job
router.post('/cancel/:jobId', ErrorHandler.asyncHandler(async (req, res) => {
  try {
    const { jobId } = req.params;
    const job = batchManager.getJob(jobId);

    if (!job) {
      return res.status(404).json({
        success: false,
        error: 'Job not found'
      });
    }

    if (job.status === 'processing') {
      job.status = 'cancelled';
      job.endTime = new Date().toISOString();
      job.progress.phase = 'cancelled';
      job.progress.currentImage = 'Processing cancelled by user';
      
      batchManager.emit('cancelled', job);
    }

    res.json({
      success: true,
      message: 'Job cancelled successfully'
    });

  } catch (error) {
    logger.error('Error cancelling job', { error: error.message });
    res.status(500).json({
      error: 'Failed to cancel job',
      details: error.message
    });
  }
}));

module.exports = router;