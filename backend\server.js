const express = require("express");
const cors = require("cors");
const path = require("path");
const fs = require("fs");
const fsPromises = fs.promises;
const multer = require("multer");
const sharp = require("sharp");
const sqlite3 = require("sqlite3").verbose();
const { promisify } = require("util");
const helmet = require("helmet");
const compression = require("compression");
const morgan = require("morgan");

// Configure Sharp for better memory management and crash prevention
sharp.cache({ memory: 512, files: 20 }); // Limit memory cache to 512MB, max 20 files
sharp.concurrency(1); // Process images one at a time to prevent memory spikes

// Temporary file cleanup function
const cleanupTmpFiles = async () => {
  try {
    const tmpDir = path.join(__dirname, "../data/tmp");
    const files = await fsPromises.readdir(tmpDir).catch(() => []);
    const now = Date.now();
    const maxAge = 60 * 60 * 1000; // 1 hour

    for (const file of files) {
      try {
        const filePath = path.join(tmpDir, file);
        const stats = await fsPromises.stat(filePath);
        if (now - stats.mtime.getTime() > maxAge) {
          await fsPromises.unlink(filePath);
          console.log(`🧹 Cleaned up old temp file: ${file}`);
        }
      } catch (err) {
        // Ignore errors for individual files
      }
    }
  } catch (error) {
    console.error("Error cleaning tmp files:", error.message);
  }
};

// Run cleanup every 30 minutes
setInterval(cleanupTmpFiles, 30 * 60 * 1000);

// Import middleware and utilities
const logger = require("./utils/logger");
const ErrorHandler = require("./middleware/error-handler");
const SecurityMiddleware = require("./middleware/security");
const healthCheck = require("./middleware/health-check");
const DatabaseManager = require("./config/database");
const ImageProcessor = require("./utils/image-processor");
const WindowsFileSystemUtils = require("./utils/windows-fs");

// Import our services
const OCRService = require("./services/ocr-service");
const LocalModelService = require("./services/local-model-service");
const CostTracker = require("./services/cost-tracker");
const BatchProcessor = require("./services/batch-processor");

const app = express();
// ---------------------------------------------------------------------------
// Note: Enhanced error handlers are implemented at the bottom of this file
// to prevent server crashes and provide better stability monitoring.
// ---------------------------------------------------------------------------
// Dynamic port configuration - Load from port-config.json if available

// SINGLE SOURCE OF TRUTH - Import from scripts/port-config.js
const { getBackendPort } = require("../scripts/port-config.js");

const PORT = getBackendPort();

// Security middleware
app.use(
  helmet({
    contentSecurityPolicy: false, // Disable for local development
    crossOriginEmbedderPolicy: false,
  })
);
app.use(compression());
app.use(SecurityMiddleware.securityHeaders);

// Logging middleware
if (process.env.NODE_ENV === "production") {
  app.use(
    morgan("combined", {
      stream: { write: (message) => logger.info(message.trim()) },
    })
  );
} else {
  app.use(morgan("dev"));
}

// Enable CORS **before** any rate-limters so that OPTIONS pre-flight requests
// get the proper headers and are not blocked.
app.use(cors(SecurityMiddleware.corsOptions));

// Rate limiting – remain generous but apply *after* CORS.
if (process.env.NODE_ENV === "production") {
  // Production: allow a much higher request volume to accommodate parallel
  // thumbnail generation, large folder scans, etc.
  app.use("/api/", SecurityMiddleware.createRateLimit(15 * 60 * 10000, 50000));

  // OCR routes remain more restrictive to protect costly external calls
  app.use("/api/ocr/", SecurityMiddleware.ocrRateLimit);

  // File-system heavy operations (scan, images) are extremely chatty, so we
  // attach an even more generous limiter here to avoid accidental throttling
  // while still keeping abuse protection in place.
  app.use(
    "/api/filesystem/",
    SecurityMiddleware.createRateLimit(60 * 1000, 100000)
  );
} else {
  // Development mode: ULTRA-PERMISSIVE rate limiting (effectively disabled for localhost)
  // Handles React Strict Mode double-rendering and bulk operations
  app.use("/api/", SecurityMiddleware.createRateLimit(15 * 60 * 1000, 100000)); // 100K requests per 15 minutes
  app.use(
    "/api/filesystem/",
    SecurityMiddleware.createRateLimit(60 * 1000, 100000)
  ); // 100K requests per minute for filesystem operations
  // Use the updated ocrRateLimit from SecurityMiddleware which has skip logic for development
  app.use("/api/ocr/", SecurityMiddleware.ocrRateLimit);
}

// Body-parsers and other middleware (after CORS & rate-limiter)
// Middleware
app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ extended: true, limit: "50mb" }));

// Static file serving for thumbnails and previews with CORS headers
app.use(
  "/thumbnails",
  (req, res, next) => {
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Methods", "GET");
    res.header(
      "Access-Control-Allow-Headers",
      "Origin, X-Requested-With, Content-Type, Accept"
    );
    res.header("Cross-Origin-Resource-Policy", "cross-origin");
    next();
  },
  express.static(path.join(__dirname, "../data/thumbnails"))
);

app.use(
  "/previews",
  (req, res, next) => {
    res.header("Access-Control-Allow-Origin", "*");
    res.header("Access-Control-Allow-Methods", "GET");
    res.header(
      "Access-Control-Allow-Headers",
      "Origin, X-Requested-With, Content-Type, Accept"
    );
    res.header("Cross-Origin-Resource-Policy", "cross-origin");
    next();
  },
  express.static(path.join(__dirname, "../data/previews"))
);

// Initialize services
const ocrService = new OCRService();
const localModelService = new LocalModelService();
const costTracker = new CostTracker();
const batchProcessor = new BatchProcessor(
  ocrService,
  localModelService,
  costTracker
);
const imageProcessor = new ImageProcessor();

// Database setup
const dbManager = new DatabaseManager();

// File upload configuration - Use disk storage to prevent memory crashes
const upload = multer({
  storage: multer.diskStorage({
    destination: function (req, file, cb) {
      const tmpDir = path.join(__dirname, "../data/tmp");
      // Ensure tmp directory exists
      require("fs").mkdirSync(tmpDir, { recursive: true });
      cb(null, tmpDir);
    },
    filename: function (req, file, cb) {
      // Generate unique filename with timestamp
      const uniqueSuffix = Date.now() + "-" + Math.round(Math.random() * 1e9);
      cb(null, "upload-" + uniqueSuffix + path.extname(file.originalname));
    },
  }),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 5, // Limit concurrent uploads
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|webp|tiff|bmp/;
    const extname = allowedTypes.test(
      path.extname(file.originalname).toLowerCase()
    );
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error("Only image files are allowed"));
    }
  },
});

// Ensure data directories exist
async function ensureDirectories() {
  const dirs = [
    path.join(__dirname, "../data"),
    path.join(__dirname, "../data/thumbnails"),
    path.join(__dirname, "../data/previews"),
    path.join(__dirname, "../data/temp"),
  ];

  for (const dir of dirs) {
    try {
      await fsPromises.mkdir(dir, { recursive: true });
    } catch (error) {
      if (error.code !== "EEXIST") {
        console.error(`Error creating directory ${dir}:`, error);
      }
    }
  }
}

// API Routes

// Projects
app.get(
  "/api/projects",
  ErrorHandler.asyncHandler(async (req, res) => {
    try {
      const projects = await dbManager.all(
        "SELECT * FROM projects ORDER BY updated_at DESC"
      );
      res.json({ success: true, data: projects });
    } catch (error) {
      logger.error("Error fetching projects", { error: error.message });
      throw error;
    }
  })
);

app.post(
  "/api/projects",
  SecurityMiddleware.validateRequestBody(["name", "rootPath"]),
  ErrorHandler.asyncHandler(async (req, res) => {
    try {
      const { name, rootPath, description = "", settings = {} } = req.body;

      // Validate and sanitize inputs
      const sanitizedName = SecurityMiddleware.sanitizeText(name);
      const validatedPath = SecurityMiddleware.validatePath(rootPath);

      const id = require("crypto").randomUUID();

      await dbManager.run(
        "INSERT INTO projects (id, name, root_path, description, settings) VALUES (?, ?, ?, ?, ?)",
        [
          id,
          sanitizedName,
          validatedPath,
          description,
          JSON.stringify(settings),
        ]
      );

      const project = await dbManager.get(
        "SELECT * FROM projects WHERE id = ?",
        [id]
      );
      logger.info("Project created", { projectId: id, name: sanitizedName });
      res.json({ success: true, data: project });
    } catch (error) {
      logger.error("Error creating project", { error: error.message });
      throw error;
    }
  })
);

// Folder scanning
app.post(
  "/api/folders/scan",
  SecurityMiddleware.validateRequestBody(["rootPath"]),
  ErrorHandler.asyncHandler(async (req, res) => {
    try {
      const { rootPath, projectId } = req.body;

      const validatedPath = SecurityMiddleware.validatePath(rootPath);

      // Check if path exists
      try {
        await fsPromises.access(validatedPath);
      } catch (error) {
        return res
          .status(400)
          .json({
            success: false,
            error: "Path does not exist or is not accessible",
          });
      }

      const folders = await WindowsFileSystemUtils.scanDirectoryOptimized(
        validatedPath
      );

      // Store folders in database if projectId provided
      if (projectId) {
        for (const folder of folders) {
          await dbManager.run(
            `INSERT OR REPLACE INTO folders
           (id, project_id, name, path, image_count, has_text_file, tags)
           VALUES (?, ?, ?, ?, ?, ?, ?)`,
            [
              folder.id,
              projectId,
              folder.name,
              folder.path,
              folder.imageCount,
              folder.hasTextFile,
              JSON.stringify(folder.tags),
            ]
          );
        }
      }

      logger.info("Folder scan completed", {
        rootPath: validatedPath,
        folderCount: folders.length,
      });
      res.json({ success: true, data: folders });
    } catch (error) {
      logger.error("Error scanning folders", { error: error.message });
      throw error;
    }
  })
);

// Images
app.get(
  "/api/folders/:folderId/images",
  ErrorHandler.asyncHandler(async (req, res) => {
    try {
      const { folderId } = req.params;

      // Get folder path from database
      const folder = await dbManager.get("SELECT * FROM folders WHERE id = ?", [
        folderId,
      ]);
      if (!folder) {
        return res
          .status(404)
          .json({ success: false, error: "Folder not found" });
      }

      const images = await WindowsFileSystemUtils.getImageFiles(folder.path, {
        includeMetadata: true,
      });

      // Generate thumbnails and previews for new images
      const processedImages = [];
      for (const image of images) {
        const thumbnail = await imageProcessor.generateThumbnail(image.path);
        const preview = await imageProcessor.generatePreview(image.path);

        processedImages.push({
          ...image,
          thumbnailUrl: thumbnail.url,
          previewUrl: preview.url,
        });
      }

      res.json({ success: true, data: processedImages });
    } catch (error) {
      logger.error("Error fetching images", {
        error: error.message,
        folderId: req.params.folderId,
      });
      throw error;
    }
  })
);

// Text files
app.get("/api/folders/:folderId/text", async (req, res) => {
  try {
    const { folderId } = req.params;

    const folder = await dbManager.get("SELECT * FROM folders WHERE id = ?", [
      folderId,
    ]);
    if (!folder) {
      return res
        .status(404)
        .json({ success: false, error: "Folder not found" });
    }

    // Look for text files in the folder
    const textFiles = ["ssn.txt", "SSN.txt", "data.txt", "info.txt"];
    let content = "";
    let filePath = "";

    for (const fileName of textFiles) {
      const fullPath = path.join(folder.path, fileName);
      try {
        content = await fs.readFile(fullPath, "utf8");
        filePath = fullPath;
        break;
      } catch (error) {
        // File doesn't exist, continue to next
      }
    }

    res.json({
      success: true,
      data: {
        content,
        path: filePath,
        exists: !!filePath,
      },
    });
  } catch (error) {
    console.error("Error reading text file:", error);
    res.status(500).json({ success: false, error: "Failed to read text file" });
  }
});

app.post("/api/folders/:folderId/text", async (req, res) => {
  try {
    const { folderId } = req.params;
    const { content, filename = "ocr_results.txt" } = req.body;

    const folder = await dbManager.get("SELECT * FROM folders WHERE id = ?", [
      folderId,
    ]);
    if (!folder) {
      return res
        .status(404)
        .json({ success: false, error: "Folder not found" });
    }

    const filePath = path.join(folder.path, filename);
    await fs.writeFile(filePath, content, "utf8");

    // Update folder has_text_file flag
    await dbManager.run("UPDATE folders SET has_text_file = 1 WHERE id = ?", [
      folderId,
    ]);

    res.json({ success: true, message: "Text file saved successfully" });
  } catch (error) {
    console.error("Error saving text file:", error);
    res.status(500).json({ success: false, error: "Failed to save text file" });
  }
});

// OCR routes
app.use("/api/ocr", require("./routes/ocr"));
app.use("/api/ocr", require("./routes/batch-ocr"));

// Sequential Batch Processing (OCR + ReadySearch)
app.use("/api/sequential-batch", require("./routes/sequential-batch"));

// Image operations routes
app.use("/api/images", require("./routes/image-operations"));

// Batch organization routes
app.use("/api/batch-organization", require("./routes/batch-organization"));

// Filesystem routes
app.use("/api/filesystem", require("./routes/filesystem"));
app.use("/api/filesystem", require("./routes/filesystem-debug"));

// Settings routes
app.use("/api/settings", require("./routes/settings"));

// Model validation routes
app.use("/api/model-validation", require("./routes/model-validation"));

// Smart Filter Analyzer routes
app.use("/api/smart-analyzer", require("./routes/smart-analyzer"));

// ReadySearch routes (Australian mode only)
app.use("/api/readysearch", require("./routes/readysearch"));

// File operations routes (saving, renaming with ReadySearch indicators)
app.use("/api/file-operations", require("./routes/file-operations"));

// Health check
app.get("/api/health", healthCheck.middleware());

// Additional health endpoints
app.get(
  "/api/health/database",
  ErrorHandler.asyncHandler(async (req, res) => {
    const health = await dbManager.healthCheck();
    res.json(health);
  })
);

app.get(
  "/api/health/stats",
  ErrorHandler.asyncHandler(async (req, res) => {
    const stats = await dbManager.getStats();
    const cacheStats = await imageProcessor.getCacheStats();

    res.json({
      database: stats,
      cache: cacheStats,
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString(),
    });
  })
);

// Root route handler - friendly message for users who navigate to backend directly
app.get("/", (req, res) => {
  res.json({
    name: "DL Organizer Backend API",
    version: "1.1.0",
    status: "running",
    message:
      "This is the backend API server. Please access the frontend at http://localhost:3032",
    endpoints: {
      health: "/api/health",
      api_docs: "All API endpoints are under /api/*",
      frontend_url: "http://localhost:3032",
    },
    timestamp: new Date().toISOString(),
  });
});

// Favicon handler - return empty 204 response to avoid 404 errors
app.get("/favicon.ico", (req, res) => {
  res.status(204).end(); // No content response
});

// Error handling middleware
app.use(ErrorHandler.notFound);
app.use(ErrorHandler.handle);

// Initialize and start server
async function startServer() {
  try {
    await ensureDirectories();
    await dbManager.initialize();

    // Use the configured port directly

    logger.info("Starting DL Organizer Backend", {
      port: PORT,
      nodeEnv: process.env.NODE_ENV || "development",
    });

    app.listen(PORT, () => {
      logger.info("DL Organizer Backend started successfully", {
        port: PORT,
        dataDir: path.join(__dirname, "../data"),
        healthUrl: `http://localhost:${PORT}/api/health`,
      });

      console.log(`🚀 DL Organizer Backend running on port ${PORT}`);
      console.log(`📁 Data directory: ${path.join(__dirname, "../data")}`);
      console.log(`🔍 Health check: http://localhost:${PORT}/api/health`);
    });
  } catch (error) {
    logger.error("Failed to start server", { error: error.message });
    console.error("Failed to start server:", error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on("SIGINT", () => {
  logger.info("Received SIGINT, shutting down gracefully");
  console.log("\n🛑 Shutting down DL Organizer Backend...");

  dbManager
    .close()
    .then(() => {
      logger.info("Database connection closed");
      console.log("Database connection closed");
      process.exit(0);
    })
    .catch((err) => {
      logger.error("Error closing database", { error: err.message });
      console.error("Error closing database:", err);
      process.exit(1);
    });
});

// Enhanced error handlers - log but don't crash for better stability
process.on("uncaughtException", (error) => {
  logger.error("Uncaught Exception - Server Continuing", {
    error: error.message,
    stack: error.stack,
    timestamp: new Date().toISOString(),
  });
  console.error("⚠️ Uncaught Exception (handled):", error.message);

  // Log memory usage to detect potential memory issues
  const memUsage = process.memoryUsage();
  logger.warn("Memory usage at exception", {
    rss: Math.round(memUsage.rss / 1024 / 1024) + "MB",
    heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024) + "MB",
    external: Math.round(memUsage.external / 1024 / 1024) + "MB",
  });

  // Only exit if memory usage is critically high (>1GB)
  if (memUsage.rss > 1024 * 1024 * 1024) {
    logger.error("Critical memory usage - forcing restart");
    process.exit(1);
  }
});

process.on("unhandledRejection", (reason, promise) => {
  logger.error("Unhandled Rejection - Server Continuing", {
    reason: reason?.message || reason,
    stack: reason?.stack,
    promise: promise.toString(),
    timestamp: new Date().toISOString(),
  });
  console.error("⚠️ Unhandled Rejection (handled):", reason?.message || reason);
});

startServer(); // Force restart
