# Batch Processing Feature Enhancement - Core Files Export

This folder contains all the core files involved in the current batch processing, OCR, and image display functionality. These files are the foundation for implementing the enhanced "recursive display with selective selection" batch processing feature.

## Feature Requirements Summary

### Enhanced Batch Mode Goals:
1. **Deep Folder Traversal**: Display all images up to 3 levels deep when a parent folder is selected
2. **Unified Image View**: Show all nested images in one grid view instead of requiring navigation to leaf folders
3. **Intelligent Filtering**: Dynamic text-based filtering with persistent selection state
4. **Enhanced Selection**: Select All, Add All, Clear All functionality with filter memory
5. **Auto-Detect OCR**: Batch processing with auto-detect mode and files saved to original locations
6. **File Size Filtering**: Filter images by minimum file size thresholds

## Core Files Included

### Frontend Components (`src/components/dl-organizer/`)
- **folder-tree.tsx** - Current folder navigation tree component
- **image-grid.tsx** - Image thumbnail grid display component  
- **ocr-panel.tsx** - OCR processing interface and mode selection
- **batch-organization-panel.tsx** - Current batch processing panel

### Backend Services (`backend/services/`)
- **ocr-service.js** - Multi-provider OCR processing with cost tracking
- **batch-processor.js** - Current bulk folder processing logic

### Backend Routes (`backend/routes/`)
- **batch-ocr.js** - Batch OCR processing endpoints
- **batch-organization.js** - Batch organization endpoints
- **image-operations.js** - Image manipulation and metadata operations
- **ocr.js** - Individual OCR processing endpoints
- **filesystem.js** - File system operations and folder scanning

### Backend Utilities (`backend/utils/`)
- **image-processor.js** - Image processing with Sharp (thumbnails, rotation)
- **ocr-cache.js** - OCR result caching system

### Type Definitions (`src/types/`)
- Complete TypeScript type definitions for OCR results, folder structures, and API interfaces

## Current Architecture Overview

### Folder Navigation Flow:
1. `folder-tree.tsx` displays hierarchical folder structure
2. User clicks on folder → `filesystem.js` scans for images
3. Only immediate folder contents shown (no deep traversal)
4. `image-grid.tsx` displays images from current folder only

### OCR Processing Flow:
1. `ocr-panel.tsx` handles mode selection (US/AUS/Auto-detect)
2. Individual images: `ocr.js` → `ocr-service.js` 
3. Batch processing: `batch-ocr.js` → `batch-processor.js`
4. Results saved as .json files in processed folder

### Current Limitations:
- No deep folder traversal (must navigate to leaf folders)
- No unified view of nested images
- Limited filtering capabilities
- Batch mode only processes current folder level
- No persistent selection across filter changes

## Implementation Strategy

### Phase 1: Backend API Enhancement
1. Modify `filesystem.js` to support deep traversal (3 levels)
2. Update folder scanning to return flattened image list with source paths
3. Enhance batch processing to save files in original image locations

### Phase 2: Frontend UI Enhancement  
1. Update `folder-tree.tsx` to trigger deep traversal mode
2. Enhance `image-grid.tsx` with filtering and persistent selection
3. Add filter controls with Select All/Add All/Clear All buttons
4. Implement file size and text-based filtering

### Phase 3: Enhanced Batch Processing
1. Update `ocr-panel.tsx` with "Batch Mode - Folders" option
2. Integrate auto-detect mode as default for batch processing
3. Ensure .txt and .json files save to original image directories
4. Add progress tracking for large batch operations

## Key Technical Considerations

### Performance:
- Large folder structures (1000+ images) need efficient loading
- Implement lazy loading and virtualization for image grids
- Consider image thumbnail caching for frequently accessed folders

### User Experience:
- Persistent selection state across filter changes
- Clear visual feedback for selected images during filtering
- Progress indicators for large batch operations
- Error handling for inaccessible or corrupted images

### File System:
- Handle Windows file system permissions and network drives
- Support long file paths (>260 characters)
- Manage concurrent file access during batch processing

## Next Steps

1. Analyze current implementation patterns in these files
2. Design API modifications for deep traversal
3. Create enhanced UI mockups for filtering interface
4. Implement backend changes first, then frontend updates
5. Test with large folder structures and various image types

This export provides the complete foundation for implementing the enhanced batch processing feature with recursive display and intelligent selection capabilities.