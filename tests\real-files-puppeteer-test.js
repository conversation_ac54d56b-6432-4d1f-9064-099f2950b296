const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

/**
 * Comprehensive Puppeteer Test for Real NSW DL Files
 * This test complements the Playwright test with additional API testing and screenshots
 */

const config = {
  projectId: '87cd7031-1acd-482e-b4c2-057f8f1f96ed',
  testDataFolder: 'C:\\claude\\dl-organizer\\files\\9172-NSW-DLs',
  frontendUrl: 'http://localhost:3001',
  backendUrl: 'http://localhost:3003',
  screenshotDir: 'tests/screenshots/puppeteer',
  timeout: 30000
};

class RealFilesTestSuite {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = {
      apiTests: [],
      uiTests: [],
      fixes: {
        rotation: false,
        ocrAnalysis: false,
        cachedResults: false,
        readySearch: false
      }
    };
  }

  async initialize() {
    console.log('🚀 Initializing Puppeteer test suite...');
    
    // Ensure screenshot directory exists
    await fs.mkdir(config.screenshotDir, { recursive: true });
    
    // Launch browser
    this.browser = await puppeteer.launch({
      headless: false, // Run in headed mode for visual debugging
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    
    // Enable console logging
    this.page.on('console', msg => console.log(`Browser: ${msg.text()}`));
    this.page.on('pageerror', error => console.error(`Page Error: ${error.message}`));
    
    console.log('✅ Puppeteer initialized');
  }

  async testBackendAPI() {
    console.log('🔧 Testing Backend API with real files...');
    
    try {
      // Test 1: Get project details
      const projectResponse = await fetch(`${config.backendUrl}/api/projects`);
      const projects = await projectResponse.json();
      const nswProject = projects.data.find(p => p.id === config.projectId);
      
      if (nswProject) {
        console.log('✅ API: NSW DL Test Project found');
        this.testResults.apiTests.push({ test: 'project-lookup', status: 'passed' });
      }

      // Test 2: Scan folder for images
      const scanResponse = await fetch(`${config.backendUrl}/api/folders/scan`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          rootPath: 'C:/claude/dl-organizer/files/9172-NSW-DLs',
          projectId: config.projectId
        })
      });
      
      const scanResult = await scanResponse.json();
      if (scanResult.success) {
        console.log(`✅ API: Folder scan successful - found ${scanResult.data.length} folders`);
        this.testResults.apiTests.push({ test: 'folder-scan', status: 'passed', data: scanResult.data.length });
      }

      // Test 3: Test with a specific real file (base64 encode a known file path)
      const testImagePath = 'C:/claude/dl-organizer/files/9172-NSW-DLs/alextelferdl.jpg';
      const encodedPath = Buffer.from(testImagePath).toString('base64');
      
      // Test OCR cache check
      const cacheResponse = await fetch(`${config.backendUrl}/api/ocr/cache/check`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ imagePath: encodedPath })
      });
      
      const cacheResult = await cacheResponse.json();
      console.log(`✅ API: Cache check for real file - exists: ${cacheResult.exists}`);
      this.testResults.apiTests.push({ test: 'cache-check', status: 'passed', cached: cacheResult.exists });

      // Test 4: Test image rotation API (Fix #1)
      const rotateResponse = await fetch(`${config.backendUrl}/api/images/${encodedPath}/rotate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ degrees: 90 })
      });
      
      if (rotateResponse.status === 200) {
        console.log('✅ API: Image rotation endpoint working (no failed to fetch)');
        this.testResults.fixes.rotation = true;
        this.testResults.apiTests.push({ test: 'image-rotation', status: 'passed' });
      } else if (rotateResponse.status === 404) {
        console.log('ℹ️ API: Image rotation test - file not found (expected for some test files)');
        this.testResults.apiTests.push({ test: 'image-rotation', status: 'file-not-found' });
      }

      // Test 5: Test ReadySearch API
      const readySearchResponse = await fetch(`${config.backendUrl}/api/readysearch/search`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          firstName: 'John',
          lastName: 'Doe', 
          yearOfBirth: '1990',
          imageId: encodedPath
        })
      });
      
      if (readySearchResponse.status === 200) {
        const readySearchResult = await readySearchResponse.json();
        if (readySearchResult.success) {
          console.log('✅ API: ReadySearch integration working');
          this.testResults.fixes.readySearch = true;
          this.testResults.apiTests.push({ test: 'readysearch', status: 'passed' });
        }
      }

    } catch (error) {
      console.error(`❌ API Test Error: ${error.message}`);
      this.testResults.apiTests.push({ test: 'api-error', status: 'failed', error: error.message });
    }
  }

  async loadProjectView() {
    console.log('🏗️ Loading NSW DL Test Project...');
    
    await this.page.goto(config.frontendUrl, { waitUntil: 'networkidle0' });
    
    // Take initial screenshot
    await this.page.screenshot({ 
      path: `${config.screenshotDir}/01-homepage.png`,
      fullPage: true 
    });
    
    // Look for project and click it
    const projectSelector = await this.page.$('text/NSW DL Test Project');
    if (projectSelector) {
      await projectSelector.click();
      await this.page.waitForNavigation({ waitUntil: 'networkidle0' });
      console.log('✅ NSW DL Test Project loaded');
    } else {
      console.log('ℹ️ Project card not found, may already be in project view');
    }
    
    // Wait for images to load
    await this.page.waitForSelector('img[src*="thumbnail"], [data-testid="image"]', { timeout: 10000 });
    
    // Take screenshot of project loaded
    await this.page.screenshot({ 
      path: `${config.screenshotDir}/02-project-loaded.png`,
      fullPage: true 
    });
    
    console.log('✅ Project view loaded with real NSW DL images');
  }

  async testImageInteractions() {
    console.log('🖼️ Testing image interactions with real files...');
    
    // Get all images
    const imageElements = await this.page.$$('img[src*="thumbnail"], [data-testid="image"]');
    console.log(`Found ${imageElements.length} images`);
    
    if (imageElements.length === 0) {
      throw new Error('No images found in project view');
    }
    
    // Test image selection
    for (let i = 0; i < Math.min(3, imageElements.length); i++) {
      await imageElements[i].click();
      await this.page.waitForTimeout(2000);
      
      // Check if OCR panel appears
      const ocrPanel = await this.page.$('text/OCR, text/Analyze');
      if (ocrPanel) {
        console.log(`✅ Image ${i + 1}: Selection working, OCR panel visible`);
      }
      
      // Take screenshot of selection
      await this.page.screenshot({ 
        path: `${config.screenshotDir}/03-image-${i + 1}-selected.png`,
        fullPage: true 
      });
    }
    
    this.testResults.uiTests.push({ test: 'image-selection', status: 'passed' });
  }

  async testRotationFix() {
    console.log('🔄 Testing image rotation fix...');
    
    try {
      // Select an image
      const imageElements = await this.page.$$('img[src*="thumbnail"], [data-testid="image"]');
      if (imageElements.length > 0) {
        await imageElements[0].click();
        await this.page.waitForTimeout(1000);
        
        // Try to find rotation controls
        await imageElements[0].hover();
        await this.page.waitForTimeout(500);
        
        const rotateButton = await this.page.$('button[title*="Rotate"], svg[data-testid="rotate"]');
        if (rotateButton) {
          await rotateButton.click();
          await this.page.waitForTimeout(3000);
          
          // Check for "failed to fetch" errors
          const errorElements = await this.page.$$('text/failed to fetch');
          if (errorElements.length === 0) {
            console.log('✅ Image rotation working (no "failed to fetch" errors)');
            this.testResults.fixes.rotation = true;
          }
          
          // Take screenshot after rotation
          await this.page.screenshot({ 
            path: `${config.screenshotDir}/04-after-rotation.png`,
            fullPage: true 
          });
        } else {
          console.log('ℹ️ Rotation controls not found in current UI state');
        }
      }
    } catch (error) {
      console.error(`⚠️ Rotation test error: ${error.message}`);
    }
  }

  async testOCRAnalysisFix() {
    console.log('🤖 Testing OCR analysis fix...');
    
    try {
      // Select an image
      const imageElements = await this.page.$$('img[src*="thumbnail"], [data-testid="image"]');
      if (imageElements.length > 1) {
        await imageElements[1].click();
        await this.page.waitForTimeout(2000);
        
        // Look for analyze button
        const analyzeButton = await this.page.$('button:contains("Analyze"), button:contains("Start OCR")');
        if (analyzeButton) {
          await analyzeButton.click();
          await this.page.waitForTimeout(3000);
          
          // Check for "failed to fetch" errors
          const errorElements = await this.page.$$('text/failed to fetch');
          if (errorElements.length === 0) {
            console.log('✅ OCR analysis working (no "failed to fetch" errors)');
            this.testResults.fixes.ocrAnalysis = true;
          }
          
          // Take screenshot of OCR analysis
          await this.page.screenshot({ 
            path: `${config.screenshotDir}/05-ocr-analysis.png`,
            fullPage: true 
          });
        }
      }
    } catch (error) {
      console.error(`⚠️ OCR analysis test error: ${error.message}`);
    }
  }

  async testCachedResultsFix() {
    console.log('💾 Testing cached results fix...');
    
    try {
      // Test with multiple images to find cached results
      const imageElements = await this.page.$$('img[src*="thumbnail"], [data-testid="image"]');
      
      for (let i = 0; i < Math.min(5, imageElements.length); i++) {
        await imageElements[i].click();
        await this.page.waitForTimeout(2000);
        
        // Look for cached results indicator
        const cachedIndicator = await this.page.$('text/Cached, text/loaded from cache');
        if (cachedIndicator) {
          console.log(`✅ Image ${i + 1}: Cached results loaded immediately`);
          this.testResults.fixes.cachedResults = true;
          
          // Take screenshot of cached results
          await this.page.screenshot({ 
            path: `${config.screenshotDir}/06-cached-results-${i + 1}.png`,
            fullPage: true 
          });
          break;
        }
      }
    } catch (error) {
      console.error(`⚠️ Cached results test error: ${error.message}`);
    }
  }

  async testReadySearchIntegration() {
    console.log('🔍 Testing ReadySearch integration...');
    
    try {
      // Make sure in Australian mode
      const australianToggle = await this.page.$('text/Australian, button:contains("AUS")');
      if (australianToggle) {
        await australianToggle.click();
        await this.page.waitForTimeout(1000);
      }
      
      // Test with images that might have OCR results
      const imageElements = await this.page.$$('img[src*="thumbnail"], [data-testid="image"]');
      
      for (let i = 0; i < Math.min(5, imageElements.length); i++) {
        await imageElements[i].click();
        await this.page.waitForTimeout(2000);
        
        // Look for ReadySearch panel
        const readySearchPanel = await this.page.$('text/ReadySearch');
        if (readySearchPanel) {
          console.log(`✅ ReadySearch panel visible for image ${i + 1}`);
          
          const searchButton = await this.page.$('button:contains("Search ReadySearch Database")');
          if (searchButton) {
            // Take screenshot before search
            await this.page.screenshot({ 
              path: `${config.screenshotDir}/07-readysearch-available-${i + 1}.png`,
              fullPage: true 
            });
            
            console.log('✅ ReadySearch integration working');
            this.testResults.fixes.readySearch = true;
            break;
          }
        }
      }
    } catch (error) {
      console.error(`⚠️ ReadySearch test error: ${error.message}`);
    }
  }

  async generateTestReport() {
    console.log('📊 Generating comprehensive test report...');
    
    // Take final screenshot
    await this.page.screenshot({ 
      path: `${config.screenshotDir}/08-final-state.png`,
      fullPage: true 
    });
    
    // Check for any remaining "failed to fetch" errors
    const fetchErrors = await this.page.$$('text/failed to fetch');
    const errorCount = fetchErrors.length;
    
    const report = {
      timestamp: new Date().toISOString(),
      testEnvironment: {
        projectId: config.projectId,
        folderPath: config.testDataFolder,
        frontendUrl: config.frontendUrl,
        backendUrl: config.backendUrl
      },
      fixes: this.testResults.fixes,
      apiTests: this.testResults.apiTests,
      uiTests: this.testResults.uiTests,
      finalErrorCount: errorCount,
      summary: {
        allFixesWorking: Object.values(this.testResults.fixes).every(fix => fix),
        totalApiTests: this.testResults.apiTests.length,
        totalUiTests: this.testResults.uiTests.length,
        noFetchErrors: errorCount === 0
      }
    };
    
    // Save report
    await fs.writeFile(
      `${config.screenshotDir}/test-report.json`, 
      JSON.stringify(report, null, 2)
    );
    
    // Print summary
    console.log('\n🎉 PUPPETEER TEST SUMMARY:');
    console.log('=================================');
    console.log(`✅ Image Rotation Fix: ${this.testResults.fixes.rotation ? 'WORKING' : 'NOT VERIFIED'}`);
    console.log(`✅ OCR Analysis Fix: ${this.testResults.fixes.ocrAnalysis ? 'WORKING' : 'NOT VERIFIED'}`);
    console.log(`✅ Cached Results Fix: ${this.testResults.fixes.cachedResults ? 'WORKING' : 'NOT VERIFIED'}`);
    console.log(`✅ ReadySearch Integration: ${this.testResults.fixes.readySearch ? 'WORKING' : 'NOT VERIFIED'}`);
    console.log(`📊 API Tests: ${this.testResults.apiTests.length} completed`);
    console.log(`🖼️ UI Tests: ${this.testResults.uiTests.length} completed`);
    console.log(`❌ "Failed to Fetch" Errors: ${errorCount}`);
    console.log('=================================');
    
    return report;
  }

  async cleanup() {
    if (this.browser) {
      await this.browser.close();
      console.log('✅ Browser closed');
    }
  }

  async runFullTestSuite() {
    try {
      await this.initialize();
      await this.testBackendAPI();
      await this.loadProjectView();
      await this.testImageInteractions();
      await this.testRotationFix();
      await this.testOCRAnalysisFix();
      await this.testCachedResultsFix();
      await this.testReadySearchIntegration();
      
      const report = await this.generateTestReport();
      return report;
      
    } catch (error) {
      console.error(`❌ Test suite error: ${error.message}`);
      throw error;
    } finally {
      await this.cleanup();
    }
  }
}

// Export for use as module or run directly
if (require.main === module) {
  (async () => {
    const testSuite = new RealFilesTestSuite();
    try {
      const report = await testSuite.runFullTestSuite();
      console.log('🎉 Test suite completed successfully!');
      process.exit(0);
    } catch (error) {
      console.error('❌ Test suite failed:', error);
      process.exit(1);
    }
  })();
}

module.exports = RealFilesTestSuite;