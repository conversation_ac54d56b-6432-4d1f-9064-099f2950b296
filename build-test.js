const { spawn } = require('child_process');

console.log('Starting Next.js build process...\n');

const buildProcess = spawn('npm', ['run', 'build'], {
  cwd: process.cwd(),
  stdio: 'inherit',
  shell: true
});

buildProcess.on('error', (error) => {
  console.error('Build process error:', error);
  process.exit(1);
});

buildProcess.on('close', (code) => {
  console.log(`\nBuild process exited with code ${code}`);
  process.exit(code);
});