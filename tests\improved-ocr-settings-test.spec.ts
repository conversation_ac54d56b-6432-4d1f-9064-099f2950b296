import { test, expect } from '@playwright/test';

test.describe('Improved OCR Settings', () => {
  test('should display dropdown with improved visibility', async ({ page }) => {
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    console.log('Testing improved OCR Settings dropdown...');
    
    // Look for settings button
    const settingsButton = page.locator('button:has-text("Settings")');
    if (await settingsButton.isVisible()) {
      await settingsButton.click();
      await page.waitForTimeout(1000);
      
      // Check if model dropdown is visible
      const modelDropdown = page.locator('[role="combobox"]').first();
      if (await modelDropdown.isVisible()) {
        console.log('Found model dropdown');
        
        // Click to open dropdown
        await modelDropdown.click();
        await page.waitForTimeout(500);
        
        // Check if dropdown content is visible
        const dropdownContent = page.locator('[role="listbox"]');
        await expect(dropdownContent).toBeVisible();
        
        console.log('OCR Settings dropdown improvements verified');
      }
    }
  });
  
  test('should test deep folder scanning API', async ({ page }) => {
    console.log('Testing deep folder scanning...');
    
    // Test the deep scanning API endpoint
    const response = await page.request.post('http://localhost:3003/api/filesystem/scan', {
      data: {
        rootPath: 'C:/Users/<USER>/Downloads/Telegram Desktop/100x us dl w selfie/100x us dl w selfie/combined_to_do',
        maxDepth: 10,
        includeStats: true,
        deepImageScan: true
      }
    });
    
    if (response.ok()) {
      const scanResult = await response.json();
      console.log(`Scanned ${scanResult.stats?.totalFolders || 0} folders`);
      console.log(`Found ${scanResult.stats?.totalImages || 0} images`);
      
      if (scanResult.stats?.totalImages > 0) {
        console.log('Deep folder scanning working - found images');
      }
    }
  });
});