const fs = require('fs').promises;
const path = require('path');
const sharp = require('sharp'); // Only for metadata extraction, not rotation
const { createCanvas, loadImage } = require('canvas');

/**
 * Canvas-based image processor that avoids Sharp rotation and EXIF issues
 * Uses HTML5 Canvas API for rotation to avoid EXIF metadata problems
 */
class CanvasImageProcessor {
  constructor(options = {}) {
    this.quality = options.quality || 0.85;
  }

  /**
   * Rotate image using Canvas API instead of Sharp to avoid EXIF issues
   * @param {string} imagePath - Path to the image file
   * @param {number} degrees - Degrees to rotate (90, -90, 180, etc.)
   * @returns {Promise<Object>} - Result with success status
   */
  async rotateImageCanvas(imagePath, degrees) {
    try {
      console.log(`Canvas rotation: ${imagePath} by ${degrees} degrees`);
      
      // Normalize degrees to 0-360 range
      const normalizedDegrees = ((degrees % 360) + 360) % 360;
      
      // If no rotation needed, return success immediately
      if (normalizedDegrees === 0) {
        return { success: true, message: 'No rotation needed' };
      }

      // Load image using canvas (avoids EXIF auto-rotation)
      const image = await loadImage(imagePath);
      
      // Calculate new dimensions based on rotation
      const radians = (normalizedDegrees * Math.PI) / 180;
      const cos = Math.abs(Math.cos(radians));
      const sin = Math.abs(Math.sin(radians));
      
      const newWidth = Math.floor(image.width * cos + image.height * sin);
      const newHeight = Math.floor(image.width * sin + image.height * cos);
      
      // Create canvas with new dimensions
      const canvas = createCanvas(newWidth, newHeight);
      const ctx = canvas.getContext('2d');
      
      // Set high quality rendering
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';
      
      // Fill with white background
      ctx.fillStyle = '#FFFFFF';
      ctx.fillRect(0, 0, newWidth, newHeight);
      
      // Move to center and rotate
      ctx.translate(newWidth / 2, newHeight / 2);
      ctx.rotate(radians);
      
      // Draw image centered
      ctx.drawImage(image, -image.width / 2, -image.height / 2);
      
      // Convert to buffer
      const buffer = canvas.toBuffer('image/jpeg', { quality: this.quality });
      
      // Write back to original file with atomic operation
      const tempPath = imagePath + '.tmp';
      await fs.writeFile(tempPath, buffer);
      
      // Ensure the temp file is fully written
      const fd = await fs.open(tempPath, 'r+');
      try {
        await fd.sync();
      } finally {
        await fd.close();
      }
      
      // Atomic rename
      await fs.rename(tempPath, imagePath);
      
      console.log(`Canvas rotation completed successfully: ${imagePath}`);
      return {
        success: true,
        rotated: degrees,
        message: `Image rotated ${degrees} degrees successfully using Canvas`
      };
      
    } catch (error) {
      console.error(`Canvas rotation failed for ${imagePath}:`, error);
      
      // Clean up temp file if it exists
      try {
        await fs.unlink(imagePath + '.tmp');
      } catch (cleanupError) {
        // Ignore cleanup errors
      }
      
      throw error;
    }
  }

  /**
   * Get basic image metadata using Sharp (safe for metadata extraction)
   * @param {string} imagePath - Path to image
   * @returns {Promise<Object>} - Metadata object
   */
  async getImageMetadata(imagePath) {
    try {
      const metadata = await sharp(imagePath).metadata();
      const stats = await fs.stat(imagePath);
      
      return {
        width: metadata.width,
        height: metadata.height,
        format: metadata.format,
        fileSize: stats.size,
        lastModified: stats.mtime
      };
    } catch (error) {
      console.error(`Error getting metadata for ${imagePath}:`, error);
      throw error;
    }
  }
}

module.exports = CanvasImageProcessor;