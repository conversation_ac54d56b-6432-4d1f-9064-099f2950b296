# DL Organizer Agents - Optimized for Speed

**Project:** DL Organizer v1.3.1  
**Optimized:** 2025-07-26  
**Total Agents:** 5 (streamlined from 15)

## 🎯 Current Active Agents

| Agent | Focus | Max Tools | Priority |
|-------|-------|-----------|----------|
| **port-manager** | Windows infrastructure, bulletproof startup | 5 | High |
| **frontend-guardian** | Next.js 14.2+, React, TypeScript 5.8+ | 4 | High |
| **api-specialist** | Express.js, SQLite, SSE streaming | 4 | Medium |
| **ocr-specialist** | OpenAI GPT-4o, OpenRouter, Smart Analyzer | 5 | Medium |
| **qa-specialist** | Jest, Playwright, testing coverage | 5 | Medium |

## 🚀 Performance Optimizations

- **67% reduction** in agent count (15 → 5)
- **Serial tool execution** (maxConcurrentTools: 1)
- **Reduced token limits** (8000 → 6000)
- **Fast error recovery** (maxRetries: 2 → 1)
- **Smart throttling** enabled

## 📁 Project Structure

```
.claude/
├── agents-config.json          # Main agent configuration
├── claude-code-config.json     # Claude Code settings  
├── AGENTS.md                   # Agent documentation
└── agents/                     # Individual agent definitions
    ├── port-manager.md
    ├── frontend-guardian.md
    ├── api-specialist.md
    ├── ocr-specialist.md
    ├── qa-specialist.md
    └── README.md              # This file
```

## 🔄 Agent Selection Logic

**Automatic Triggers:**
- Infrastructure issues → **port-manager**
- UI/Frontend work → **frontend-guardian**  
- Backend/API work → **api-specialist**
- OCR/AI processing → **ocr-specialist**
- Testing/QA → **qa-specialist**

**Manual Selection:**
Use agent names directly in commands or let auto-trigger handle selection based on keywords.

## 📊 Tool Access Matrix

| Agent | Tools Available |
|-------|----------------|
| port-manager | Read, Write, Bash, Grep, Glob |
| frontend-guardian | Read, Write, Bash, browser, shadcn-ui |
| api-specialist | Read, Write, Bash, Grep, Glob |
| ocr-specialist | context7, Read, Write, Bash, Grep |
| qa-specialist | browser, Read, Write, Bash |

## 🎨 Current Project Technologies

**Frontend:** Next.js 14.2+ with App Router, TypeScript 5.8+, Tailwind CSS + Radix UI  
**Backend:** Express.js with SQLite, Sharp image processing, Winston logging  
**AI/OCR:** OpenAI GPT-4o, OpenRouter models, Smart Analyzer, ReadySearch v3.0  
**Testing:** Jest (unit), Playwright (E2E), SuperTest (integration)  
**Platform:** Windows with bulletproof startup system  

## 🔧 Key Features Supported

- Smart Filter Analyzer with AI-powered license side detection
- Power-Filter Workbench with sub-100ms filtering
- Multi-provider OCR with real-time model validation  
- ReadySearch Australian DL database integration
- Advanced port management and conflict resolution

Each agent is specialized and optimized for their specific domain while maintaining comprehensive coverage of the entire application stack.