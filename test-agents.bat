@echo off
title DL Organizer - Agent System Test

echo.
echo DL Organizer Agent System Test
echo ===============================
echo.

echo Checking agent files:
dir /b "C:\claude\dl-organizer\.claude\agents\*.md"

echo.
echo Checking configuration:
if exist "C:\claude\dl-organizer\.claude\claude-code-config.json" (
    echo    claude-code-config.json EXISTS
) else (
    echo    claude-code-config.json MISSING
)

echo.
echo Checking enhanced runner:
if exist "C:\Users\<USER>\.mcp-modules\node_modules\win-claude-code\runner.js" (
    findstr "CLAUDE CODE ENHANCED STARTUP" "C:\Users\<USER>\.mcp-modules\node_modules\win-claude-code\runner.js" >nul
    if %errorlevel% equ 0 (
        echo    Enhanced runner active
    ) else (
        echo    Enhanced runner missing
    )
) else (
    echo    runner.js missing
)

echo.
echo OPTIMIZED USAGE COMMANDS:
echo.
echo Instead of slow /task commands, use these efficient agent commands:
echo.
echo For TypeScript/UI issues:
echo   win-claude-code "ui-design-specialist: Fix TypeScript compilation errors"
echo.
echo For infrastructure/startup issues:  
echo   win-claude-code "infrastructure-specialist: Fix npm run dev startup problems"
echo.
echo For database/API issues:
echo   win-claude-code "data-flow-specialist: Add database indexes and optimize queries"
echo.
echo For OCR/AI optimization:
echo   win-claude-code "ocr-ai-specialist: Optimize model selection and reduce costs"
echo.
echo For testing issues:
echo   win-claude-code "qa-testing-specialist: Fix failing tests and add coverage"
echo.
echo ===============================
echo Ready to test! Try one of the commands above.
pause
