"use client"

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { AlertCircle, CheckCircle, AlertTriangle, RefreshCw, Settings, Zap, Shield, Clock } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { useModelValidation, ValidationSummary, ModelValidationResult, AutoFixResult } from '@/hooks/use-model-validation'

interface ModelValidationPanelProps {
  models: any[]
  onModelsFixed?: (fixedModels: any[]) => void
  showTitle?: boolean
  autoValidateOnMount?: boolean
}

export function ModelValidationPanel({ 
  models, 
  onModelsFixed, 
  showTitle = true,
  autoValidateOnMount = false 
}: ModelValidationPanelProps) {
  const {
    status,
    isLoading,
    error,
    lastValidation,
    validateAllModels,
    autoFixModels,
    getAvailableModels,
    clearCache,
    reinitialize,
    isInitialized
  } = useModelValidation()

  const [showDetails, setShowDetails] = useState(false)
  const [autoFixResult, setAutoFixResult] = useState<AutoFixResult | null>(null)

  // Auto-validate on mount if requested
  useState(() => {
    if (autoValidateOnMount && models.length > 0 && isInitialized) {
      handleValidateAll()
    }
  })

  const handleValidateAll = async () => {
    setAutoFixResult(null)
    await validateAllModels(models)
  }

  const handleAutoFix = async () => {
    const result = await autoFixModels(models)
    if (result) {
      setAutoFixResult(result)
      if (onModelsFixed && result.fixedModels) {
        onModelsFixed(result.fixedModels)
      }
    }
  }

  const handleClearCache = async () => {
    await clearCache()
    setAutoFixResult(null)
  }

  const handleReinitialize = async () => {
    await reinitialize()
    setAutoFixResult(null)
  }

  const renderValidationStatus = () => {
    if (!isInitialized) {
      return (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Model validator not initialized. Check OpenRouter API key configuration.
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleReinitialize}
              className="ml-2"
            >
              <Settings className="h-3 w-3 mr-1" />
              Reinitialize
            </Button>
          </AlertDescription>
        </Alert>
      )
    }

    if (error) {
      return (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Validation error: {error}
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => window.location.reload()}
              className="ml-2"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )
    }

    return null
  }

  const renderValidationSummary = (validation: ValidationSummary) => {
    const validPercentage = (validation.validModels / validation.totalModels) * 100
    const hasInvalidModels = validation.invalidModels > 0

    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {hasInvalidModels ? (
              <AlertTriangle className="h-5 w-5 text-yellow-500" />
            ) : (
              <CheckCircle className="h-5 w-5 text-green-500" />
            )}
            <span className="font-medium">
              {validation.validModels}/{validation.totalModels} models valid
            </span>
          </div>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            {new Date(validation.timestamp).toLocaleString()}
          </div>
        </div>

        <Progress value={validPercentage} className="h-2" />

        <div className="flex gap-2">
          <Badge variant="default" className="bg-green-100 text-green-700">
            {validation.validModels} Valid
          </Badge>
          {validation.invalidModels > 0 && (
            <Badge variant="destructive">
              {validation.invalidModels} Invalid
            </Badge>
          )}
        </div>

        {hasInvalidModels && (
          <div className="flex gap-2">
            <Button 
              onClick={handleAutoFix}
              disabled={isLoading}
              size="sm"
              className="flex items-center gap-1"
            >
              <Zap className="h-3 w-3" />
              Auto-Fix Models
            </Button>
            <Button 
              variant="outline"
              onClick={() => setShowDetails(!showDetails)}
              size="sm"
            >
              {showDetails ? 'Hide' : 'Show'} Details
            </Button>
          </div>
        )}
      </div>
    )
  }

  const renderValidationDetails = (validation: ValidationSummary) => {
    if (!showDetails) return null

    const invalidModels = validation.results.filter(r => !r.valid)

    return (
      <div className="mt-4 space-y-2">
        <h4 className="font-medium text-sm">Invalid Models:</h4>
        {invalidModels.map((model) => (
          <div key={model.modelId} className="p-2 bg-red-50 rounded border border-red-200">
            <div className="flex justify-between items-start">
              <div>
                <code className="text-sm font-mono bg-red-100 px-1 rounded">
                  {model.modelId}
                </code>
                <p className="text-xs text-red-600 mt-1">{model.error}</p>
              </div>
              <Badge variant="destructive" className="text-xs">
                Invalid
              </Badge>
            </div>
          </div>
        ))}
      </div>
    )
  }

  const renderAutoFixResult = (result: AutoFixResult) => {
    return (
      <div className="mt-4 p-4 bg-blue-50 rounded border border-blue-200">
        <div className="flex items-center gap-2 mb-2">
          <Shield className="h-4 w-4 text-blue-600" />
          <span className="font-medium text-blue-800">Auto-Fix Results</span>
        </div>
        
        <p className="text-sm text-blue-700 mb-3">{result.message}</p>
        
        {result.changes.length > 0 && (
          <div className="space-y-2">
            <h5 className="font-medium text-sm">Changes made:</h5>
            {result.changes.map((change, index) => (
              <div key={index} className="text-xs bg-white p-2 rounded border">
                <div className="flex items-center gap-2">
                  <Badge 
                    variant={change.action === 'replace' ? 'default' : 'secondary'}
                    className="text-xs"
                  >
                    {change.action}
                  </Badge>
                  <code className="bg-gray-100 px-1 rounded">{change.original}</code>
                  {change.replacement && (
                    <>
                      <span>→</span>
                      <code className="bg-green-100 text-green-700 px-1 rounded">
                        {change.replacement}
                      </code>
                    </>
                  )}
                </div>
                <p className="text-muted-foreground mt-1">{change.reason}</p>
              </div>
            ))}
          </div>
        )}
      </div>
    )
  }

  return (
    <Card>
      {showTitle && (
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Model Validation
          </CardTitle>
        </CardHeader>
      )}
      
      <CardContent>
        <div className="space-y-4">
          {renderValidationStatus()}
          
          {isInitialized && (
            <div className="flex gap-2 pt-2">
              <Button 
                onClick={handleValidateAll}
                disabled={isLoading || models.length === 0}
                size="sm"
                className="flex items-center gap-1 px-3 py-2"
              >
                <RefreshCw className={`h-3 w-3 ${isLoading ? 'animate-spin' : ''}`} />
                Validate Models ({models.length})
              </Button>
              
              <Button 
                variant="outline"
                onClick={handleClearCache}
                size="sm"
                className="flex items-center gap-1 px-3 py-2"
              >
                Clear Cache
              </Button>
            </div>
          )}

          {lastValidation && renderValidationSummary(lastValidation)}
          {lastValidation && renderValidationDetails(lastValidation)}
          {autoFixResult && renderAutoFixResult(autoFixResult)}
        </div>
      </CardContent>
    </Card>
  )
}