import { test, expect } from '@playwright/test';

test.describe('Model Selection and Memory Retention', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
  });

  test('should save and remember model selection', async ({ page }) => {
    console.log('Testing model selection and memory retention...');
    
    // Open settings
    const settingsButton = page.locator('button:has-text("Settings")');
    if (await settingsButton.isVisible()) {
      await settingsButton.click();
      await page.waitForTimeout(1000);
      
      // Look for model selection dropdown
      const modelSelect = page.locator('text="Vision Model"').locator('..');
      if (await modelSelect.isVisible()) {
        console.log('Vision Model selection found');
        
        // Click the model select trigger
        const selectTrigger = modelSelect.locator('[role="combobox"]');
        if (await selectTrigger.isVisible()) {
          await selectTrigger.click();
          await page.waitForTimeout(500);
          
          // Look for available models
          const freeModels = page.locator('text="🆓 Free Models"');
          if (await freeModels.isVisible()) {
            console.log('Free models section found');
            
            // Select a specific model (Gemini Flash 1.5)
            const geminiModel = page.locator('text="Gemini Flash 1.5"');
            if (await geminiModel.isVisible()) {
              await geminiModel.click();
              await page.waitForTimeout(500);
              
              // Save the configuration
              const saveButton = page.locator('button:has-text("Save Configuration")');
              if (await saveButton.isVisible()) {
                await saveButton.click();
                await page.waitForTimeout(2000);
                
                console.log('Model selection saved: Gemini Flash 1.5');
              }
            }
          }
        }
      }
      
      // Close settings
      const closeButton = page.locator('button:has-text("Close")');
      if (await closeButton.isVisible()) {
        await closeButton.click();
        await page.waitForTimeout(500);
      }
    }
    
    // Refresh the page to test memory retention
    await page.reload();
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Open settings again to verify model selection is retained
    const settingsButton2 = page.locator('button:has-text("Settings")');
    if (await settingsButton2.isVisible()) {
      await settingsButton2.click();
      await page.waitForTimeout(1000);
      
      // Check if the previously selected model is still selected
      const selectedModelInfo = page.locator('text="Gemini Flash 1.5"');
      if (await selectedModelInfo.isVisible()) {
        console.log('✓ Model selection memory retention verified: Gemini Flash 1.5');
      } else {
        console.log('✗ Model selection not retained after page refresh');
      }
      
      // Close settings
      const closeButton2 = page.locator('button:has-text("Close")');
      if (await closeButton2.isVisible()) {
        await closeButton2.click();
      }
    }
  });

  test('should test different model types and their availability', async ({ page }) => {
    console.log('Testing different model types...');
    
    // Open settings
    const settingsButton = page.locator('button:has-text("Settings")');
    if (await settingsButton.isVisible()) {
      await settingsButton.click();
      await page.waitForTimeout(1000);
      
      // Test model selection dropdown
      const modelSelect = page.locator('text="Vision Model"').locator('..');
      if (await modelSelect.isVisible()) {
        const selectTrigger = modelSelect.locator('[role="combobox"]');
        if (await selectTrigger.isVisible()) {
          await selectTrigger.click();
          await page.waitForTimeout(500);
          
          // Check for free models
          const freeModels = page.locator('text="🆓 Free Models"');
          if (await freeModels.isVisible()) {
            console.log('✓ Free models section found');
            
            // Check for specific free models
            const expectedFreeModels = [
              'Gemini Flash 1.5',
              'Qwen2-VL 7B',
              'Llama 3.2 11B Vision',
              'Phi-3.5 Vision'
            ];
            
            for (const modelName of expectedFreeModels) {
              const modelOption = page.locator(`text="${modelName}"`);
              if (await modelOption.isVisible()) {
                console.log(`✓ Found free model: ${modelName}`);
              } else {
                console.log(`✗ Missing free model: ${modelName}`);
              }
            }
          }
          
          // Check for paid models
          const paidModels = page.locator('text="💰 Paid Models"');
          if (await paidModels.isVisible()) {
            console.log('✓ Paid models section found');
            
            // Check for specific paid models
            const expectedPaidModels = [
              'GPT-4o',
              'GPT-4o Mini',
              'Claude 3.5 Sonnet',
              'Gemini Pro 1.5'
            ];
            
            for (const modelName of expectedPaidModels) {
              const modelOption = page.locator(`text="${modelName}"`);
              if (await modelOption.isVisible()) {
                console.log(`✓ Found paid model: ${modelName}`);
              } else {
                console.log(`✗ Missing paid model: ${modelName}`);
              }
            }
          }
          
          // Close dropdown
          await page.keyboard.press('Escape');
        }
      }
      
      // Close settings
      const closeButton = page.locator('button:has-text("Close")');
      if (await closeButton.isVisible()) {
        await closeButton.click();
      }
    }
  });

  test('should test model selection persistence across sessions', async ({ page }) => {
    console.log('Testing model selection persistence...');
    
    // Open settings
    const settingsButton = page.locator('button:has-text("Settings")');
    if (await settingsButton.isVisible()) {
      await settingsButton.click();
      await page.waitForTimeout(1000);
      
      // Select a different model
      const modelSelect = page.locator('text="Vision Model"').locator('..');
      if (await modelSelect.isVisible()) {
        const selectTrigger = modelSelect.locator('[role="combobox"]');
        if (await selectTrigger.isVisible()) {
          await selectTrigger.click();
          await page.waitForTimeout(500);
          
          // Select GPT-4o Mini
          const gptMiniModel = page.locator('text="GPT-4o Mini"');
          if (await gptMiniModel.isVisible()) {
            await gptMiniModel.click();
            await page.waitForTimeout(500);
            
            // Save the configuration
            const saveButton = page.locator('button:has-text("Save Configuration")');
            if (await saveButton.isVisible()) {
              await saveButton.click();
              await page.waitForTimeout(2000);
              
              console.log('Model selection saved: GPT-4o Mini');
            }
          }
        }
      }
      
      // Close settings
      const closeButton = page.locator('button:has-text("Close")');
      if (await closeButton.isVisible()) {
        await closeButton.click();
      }
    }
  });

  test('should test model configuration API endpoints', async ({ page }) => {
    console.log('Testing model configuration API endpoints...');
    
    // Test getting configuration
    const getConfigResponse = await page.request.get('http://localhost:3003/api/settings/openrouter');
    console.log(`Get config API response status: ${getConfigResponse.status()}`);
    
    if (getConfigResponse.status() === 200) {
      const config = await getConfigResponse.json();
      console.log('Current configuration:', config);
      
      // Test saving configuration
      const saveConfigResponse = await page.request.post('http://localhost:3003/api/settings/openrouter', {
        data: {
          apiKey: 'test-key',
          selectedModel: 'google/gemini-flash-1.5',
          baseUrl: 'https://openrouter.ai/api/v1',
          isEnabled: true,
          ocrMode: 'auto-detect'
        }
      });
      
      console.log(`Save config API response status: ${saveConfigResponse.status()}`);
      
      if (saveConfigResponse.status() === 200) {
        console.log('✓ Model configuration API endpoints working');
      } else {
        console.log('✗ Model configuration save failed');
      }
    } else {
      console.log('✗ Model configuration get failed');
    }
  });

  test('should test model search functionality', async ({ page }) => {
    console.log('Testing model search functionality...');
    
    // Open settings
    const settingsButton = page.locator('button:has-text("Settings")');
    if (await settingsButton.isVisible()) {
      await settingsButton.click();
      await page.waitForTimeout(1000);
      
      // Look for search input
      const searchInput = page.locator('input[placeholder*="Search models"]');
      if (await searchInput.isVisible()) {
        console.log('Search input found');
        
        // Test searching for specific models
        const searchTerms = ['GPT', 'Gemini', 'Claude', 'Llama'];
        
        for (const term of searchTerms) {
          await searchInput.fill(term);
          await page.waitForTimeout(300);
          
          // Click to open dropdown
          const selectTrigger = page.locator('text="Vision Model"').locator('..').locator('[role="combobox"]');
          if (await selectTrigger.isVisible()) {
            await selectTrigger.click();
            await page.waitForTimeout(500);
            
            // Check if filtered results are shown
            const modelOptions = page.locator('[role="option"]');
            const optionCount = await modelOptions.count();
            
            if (optionCount > 0) {
              console.log(`✓ Search for "${term}" returned ${optionCount} results`);
            } else {
              console.log(`✗ Search for "${term}" returned no results`);
            }
            
            // Close dropdown
            await page.keyboard.press('Escape');
          }
        }
        
        // Clear search
        await searchInput.fill('');
      } else {
        console.log('Search input not found');
      }
      
      // Close settings
      const closeButton = page.locator('button:has-text("Close")');
      if (await closeButton.isVisible()) {
        await closeButton.click();
      }
    }
  });

  test('should test custom model input functionality', async ({ page }) => {
    console.log('Testing custom model input...');
    
    // Open settings
    const settingsButton = page.locator('button:has-text("Settings")');
    if (await settingsButton.isVisible()) {
      await settingsButton.click();
      await page.waitForTimeout(1000);
      
      // Look for custom model button
      const customModelButton = page.locator('button:has-text("Custom Model")');
      if (await customModelButton.isVisible()) {
        await customModelButton.click();
        await page.waitForTimeout(500);
        
        // Look for custom model input
        const customModelInput = page.locator('input[placeholder*="custom model"]');
        if (await customModelInput.isVisible()) {
          console.log('Custom model input found');
          
          // Test entering a custom model
          await customModelInput.fill('openai/gpt-4o-custom');
          await page.waitForTimeout(500);
          
          // Save the configuration
          const saveButton = page.locator('button:has-text("Save Configuration")');
          if (await saveButton.isVisible()) {
            await saveButton.click();
            await page.waitForTimeout(2000);
            
            console.log('✓ Custom model functionality working');
          }
        } else {
          console.log('Custom model input not found');
        }
      } else {
        console.log('Custom model button not found');
      }
      
      // Close settings
      const closeButton = page.locator('button:has-text("Close")');
      if (await closeButton.isVisible()) {
        await closeButton.click();
      }
    }
  });
});