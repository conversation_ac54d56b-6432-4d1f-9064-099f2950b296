#!/usr/bin/env node

/**
 * PORT MEMORY SYSTEM - BULLETPROOF SOLUTION
 *
 * This system implements persistent memory to remember successful port configurations
 * across sessions and prevents port hunting loops FOREVER.
 *
 * Key Features:
 * - Persistent state tracking across sessions
 * - Success pattern memory and learning
 * - Automatic recovery from ANY conflict scenario
 * - Warning systems to prevent manual changes
 * - Bulletproof startup that NEVER fails
 * - Historical analysis and conflict prediction
 *
 * NO MORE PORT HUNTING LOOPS!
 */

const fs = require("fs");
const path = require("path");
const net = require("net");
const { exec } = require("child_process");
const util = require("util");

const execAsync = util.promisify(exec);

class PortMemorySystem {
  constructor() {
    this.projectRoot = path.resolve(__dirname, "..");
    this.dataDir = path.join(this.projectRoot, "data");
    this.memoryFile = path.join(this.dataDir, "port-memory.json");
    this.successFile = path.join(this.dataDir, "port-success-patterns.json");
    this.warningsFile = path.join(this.dataDir, "port-warnings.json");
    this.lockFile = path.join(this.dataDir, ".port-memory-lock");

    // Ensure data directory exists
    if (!fs.existsSync(this.dataDir)) {
      fs.mkdirSync(this.dataDir, { recursive: true });
    }

    this.memory = this.loadMemory();
    this.successPatterns = this.loadSuccessPatterns();
    this.warnings = this.loadWarnings();
  }

  /**
   * Load persistent memory from disk
   */
  loadMemory() {
    try {
      if (fs.existsSync(this.memoryFile)) {
        const data = JSON.parse(fs.readFileSync(this.memoryFile, "utf8"));
        console.log(
          "🧠 Loaded port memory with",
          Object.keys(data.sessions || {}).length,
          "remembered sessions"
        );
        return data;
      }
    } catch (error) {
      console.warn("⚠️ Failed to load port memory:", error.message);
    }

    return {
      sessions: {},
      lastSuccessful: null,
      conflictHistory: [],
      bootHistory: [],
      metadata: {
        created: new Date().toISOString(),
        version: "1.0.0",
      },
    };
  }

  /**
   * Load success patterns from disk
   */
  loadSuccessPatterns() {
    try {
      if (fs.existsSync(this.successFile)) {
        return JSON.parse(fs.readFileSync(this.successFile, "utf8"));
      }
    } catch (error) {
      console.warn("⚠️ Failed to load success patterns:", error.message);
    }

    return {
      patterns: [],
      bestConfiguration: null,
      reliability: {},
      statistics: {
        totalAttempts: 0,
        successfulBoots: 0,
        conflictResolutions: 0,
      },
    };
  }

  /**
   * Load warnings and notes from disk
   */
  loadWarnings() {
    try {
      if (fs.existsSync(this.warningsFile)) {
        return JSON.parse(fs.readFileSync(this.warningsFile, "utf8"));
      }
    } catch (error) {
      console.warn("⚠️ Failed to load warnings:", error.message);
    }

    return {
      active: [],
      dismissed: [],
      critical: [],
    };
  }

  /**
   * Save all memory data to disk
   */
  async saveMemory() {
    try {
      // Create lock to prevent concurrent writes
      fs.writeFileSync(this.lockFile, process.pid.toString());

      // Save memory
      fs.writeFileSync(this.memoryFile, JSON.stringify(this.memory, null, 2));
      fs.writeFileSync(
        this.successFile,
        JSON.stringify(this.successPatterns, null, 2)
      );
      fs.writeFileSync(
        this.warningsFile,
        JSON.stringify(this.warnings, null, 2)
      );

      // Remove lock
      if (fs.existsSync(this.lockFile)) {
        fs.unlinkSync(this.lockFile);
      }

      console.log("💾 Port memory saved successfully");
    } catch (error) {
      console.error("❌ Failed to save port memory:", error.message);

      // Clean up lock on error
      if (fs.existsSync(this.lockFile)) {
        fs.unlinkSync(this.lockFile);
      }
    }
  }

  /**
   * Record a new session in memory
   */
  recordSession(ports, success = true, conflicts = []) {
    const sessionId = this.generateSessionId();
    const session = {
      id: sessionId,
      timestamp: new Date().toISOString(),
      ports: { ...ports },
      success,
      conflicts: [...conflicts],
      processInfo: {
        pid: process.pid,
        nodeVersion: process.version,
        platform: process.platform,
      },
      environment: {
        cwd: process.cwd(),
        username: process.env.USERNAME || process.env.USER,
      },
    };

    this.memory.sessions[sessionId] = session;

    if (success) {
      this.memory.lastSuccessful = session;
      this.recordSuccessPattern(ports);
      console.log("✅ Recorded successful session:", sessionId);
    } else {
      this.memory.conflictHistory.push({
        timestamp: session.timestamp,
        conflicts,
        attemptedPorts: ports,
      });
      console.log("⚠️ Recorded failed session:", sessionId);
    }

    // Keep only last 50 sessions to prevent file bloat
    const sessionKeys = Object.keys(this.memory.sessions);
    if (sessionKeys.length > 50) {
      const oldest = sessionKeys
        .sort(
          (a, b) =>
            new Date(this.memory.sessions[a].timestamp) -
            new Date(this.memory.sessions[b].timestamp)
        )
        .slice(0, sessionKeys.length - 50);

      oldest.forEach((key) => delete this.memory.sessions[key]);
    }

    this.saveMemory();
  }

  /**
   * Record a successful port pattern
   */
  recordSuccessPattern(ports) {
    const pattern = {
      ports: { ...ports },
      timestamp: new Date().toISOString(),
      count: 1,
    };

    // Check if pattern already exists
    const existing = this.successPatterns.patterns.find(
      (p) =>
        p.ports.frontend === ports.frontend &&
        p.ports.backend === ports.backend &&
        p.ports.ngrok === ports.ngrok
    );

    if (existing) {
      existing.count++;
      existing.lastUsed = pattern.timestamp;
    } else {
      this.successPatterns.patterns.push(pattern);
    }

    // Update statistics
    this.successPatterns.statistics.successfulBoots++;

    // Determine best configuration
    this.successPatterns.bestConfiguration = this.successPatterns.patterns.sort(
      (a, b) => b.count - a.count
    )[0];

    // Update reliability scores
    const key = `${ports.frontend}-${ports.backend}-${ports.ngrok}`;
    this.successPatterns.reliability[key] =
      (this.successPatterns.reliability[key] || 0) + 1;
  }

  /**
   * Get the most reliable port configuration
   */
  getBestConfiguration() {
    if (this.successPatterns.bestConfiguration) {
      console.log("🏆 Using most reliable configuration from memory");
      return this.successPatterns.bestConfiguration.ports;
    }

    if (this.memory.lastSuccessful) {
      console.log("🔄 Using last successful configuration from memory");
      return this.memory.lastSuccessful.ports;
    }

    console.log("🆕 No successful patterns in memory, using defaults");
    return {
      frontend: 8030,
      backend: 8003,
      ngrok: 8040,
    };
  }

  /**
   * Predict and prevent port conflicts
   */
  async predictConflicts(proposedPorts) {
    const conflicts = [];
    const warnings = [];

    // Check against historical conflicts
    for (const conflict of this.memory.conflictHistory) {
      for (const historical of conflict.conflicts) {
        if (
          proposedPorts.frontend === historical.port ||
          proposedPorts.backend === historical.port ||
          proposedPorts.ngrok === historical.port
        ) {
          warnings.push({
            type: "historical-conflict",
            message: `Port ${historical.port} had conflicts before with ${historical.processInfo?.name}`,
            severity: "medium",
          });
        }
      }
    }

    // Check current port availability
    for (const [service, port] of Object.entries(proposedPorts)) {
      if (await this.isPortInUse(port)) {
        const processInfo = await this.getPortProcessInfo(port);
        conflicts.push({
          service,
          port,
          processInfo,
          prediction: "conflict-detected",
        });
      }
    }

    return { conflicts, warnings };
  }

  /**
   * Bulletproof port resolution that NEVER fails
   */
  async bulletproofResolve() {
    console.log("🛡️ BULLETPROOF PORT RESOLUTION - This WILL succeed!");

    const maxAttempts = 10;
    const strategies = [
      "use-best-configuration",
      "use-last-successful",
      "use-safe-defaults",
      "use-high-ports",
      "use-random-ports",
      "use-emergency-ports",
      "use-any-available",
      "force-kill-conflicts",
      "use-localhost-alternatives",
      "final-fallback",
    ];

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const strategy = strategies[attempt] || "final-fallback";
      console.log(`🎯 Attempt ${attempt + 1}: Strategy "${strategy}"`);

      try {
        const result = await this.executeStrategy(strategy);
        if (result.success) {
          console.log(`✅ SUCCESS! Strategy "${strategy}" worked!`);
          this.recordSession(result.ports, true, []);
          return result;
        }
      } catch (error) {
        console.warn(`⚠️ Strategy "${strategy}" failed:`, error.message);
      }
    }

    // This should NEVER happen, but if it does, use absolute fallback
    console.log("🆘 EMERGENCY FALLBACK - Using system assigned ports");
    return await this.emergencyFallback();
  }

  /**
   * Execute a specific resolution strategy
   */
  async executeStrategy(strategy) {
    let ports;

    switch (strategy) {
      case "use-best-configuration":
        ports = this.getBestConfiguration();
        break;

      case "use-last-successful":
        ports = this.memory.lastSuccessful?.ports || {
          frontend: 8030,
          backend: 8003,
          ngrok: 8040,
        };
        break;

      case "use-safe-defaults":
        ports = { frontend: 8030, backend: 8003, ngrok: 8040 };
        break;

      case "use-high-ports":
        ports = { frontend: 8030, backend: 8003, ngrok: 8040 };
        break;

      case "use-random-ports":
        ports = {
          frontend: 3000 + Math.floor(Math.random() * 1000),
          backend: 3000 + Math.floor(Math.random() * 1000),
          ngrok: 4000 + Math.floor(Math.random() * 1000),
        };
        break;

      case "use-emergency-ports":
        ports = { frontend: 9030, backend: 9003, ngrok: 9040 };
        break;

      case "use-any-available":
        ports = {
          frontend: await this.findAvailablePort(3000),
          backend: await this.findAvailablePort(3000),
          ngrok: await this.findAvailablePort(4000),
        };
        break;

      case "force-kill-conflicts":
        ports = this.getBestConfiguration();
        await this.forceKillConflicts(ports);
        break;

      case "use-localhost-alternatives":
        ports = { frontend: 3333, backend: 3334, ngrok: 3335 };
        break;

      case "final-fallback":
      default:
        return await this.emergencyFallback();
    }

    // Validate the strategy result
    const validation = await this.validatePorts(ports);
    return {
      success: validation.success,
      ports,
      strategy,
      validation,
    };
  }

  /**
   * Emergency fallback that uses system-assigned ports
   */
  async emergencyFallback() {
    console.log("🚨 EMERGENCY FALLBACK: Using system-assigned ports");

    const ports = {
      frontend: await this.getSystemAssignedPort(),
      backend: await this.getSystemAssignedPort(),
      ngrok: await this.getSystemAssignedPort(),
    };

    return {
      success: true,
      ports,
      strategy: "emergency-system-assigned",
      validation: {
        success: true,
        message: "System assigned ports are guaranteed available",
      },
    };
  }

  /**
   * Get a system-assigned available port
   */
  async getSystemAssignedPort() {
    return new Promise((resolve, reject) => {
      const server = net.createServer();
      server.listen(0, () => {
        const port = server.address().port;
        server.close(() => resolve(port));
      });
      server.on("error", reject);
    });
  }

  /**
   * Force kill processes that conflict with our desired ports
   */
  async forceKillConflicts(ports) {
    console.log("⚔️ Force killing conflicting processes...");

    for (const [service, port] of Object.entries(ports)) {
      try {
        const processInfo = await this.getPortProcessInfo(port);
        if (processInfo && !this.isOurProcess(processInfo)) {
          console.log(
            `🗡️ Killing ${processInfo.name} (PID: ${processInfo.pid}) on port ${port}`
          );
          await execAsync(`taskkill /F /PID ${processInfo.pid}`, {
            timeout: 5000,
          });
          await new Promise((resolve) => setTimeout(resolve, 1000)); // Wait for process to die
        }
      } catch (error) {
        console.warn(
          `⚠️ Could not kill process on port ${port}:`,
          error.message
        );
      }
    }
  }

  /**
   * Add critical warning to prevent manual changes
   */
  addCriticalWarning(message, preventAction = null) {
    const warning = {
      id: Date.now().toString(),
      message,
      timestamp: new Date().toISOString(),
      preventAction,
      severity: "critical",
      acknowledged: false,
    };

    this.warnings.critical.push(warning);
    this.saveMemory();

    console.log("🚨 CRITICAL WARNING ADDED:", message);
    return warning.id;
  }

  /**
   * Display all active warnings (FIXED VERSION)
   */
  displayWarnings() {
    const allWarnings = [
      ...(this.warnings.critical || []),
      ...(this.warnings.active || []),
    ].filter((w) => w && !w.acknowledged && w.message); // Filter out undefined and invalid warnings

    if (allWarnings.length > 0) {
      console.log("\n🚨 ACTIVE WARNINGS:");
      allWarnings.forEach((warning) => {
        if (warning && warning.message) {
          // Double-check warning is valid
          const prefix =
            warning.severity === "critical" ? "🚨 CRITICAL" : "⚠️ WARNING";
          console.log(`${prefix}: ${warning.message}`);
        }
      });
      console.log("");
    }
  }

  /**
   * Check if a port is in use
   */
  async isPortInUse(port) {
    return new Promise((resolve) => {
      const server = net.createServer();
      server.listen(port, () => {
        server.once("close", () => resolve(false));
        server.close();
      });
      server.on("error", () => resolve(true));
    });
  }

  /**
   * Find an available port starting from base
   */
  async findAvailablePort(basePort, maxTries = 100) {
    for (let port = basePort; port < basePort + maxTries; port++) {
      if (!(await this.isPortInUse(port))) {
        return port;
      }
    }
    throw new Error(
      `No available port found in range ${basePort}-${basePort + maxTries}`
    );
  }

  /**
   * Validate that ports are actually usable
   */
  async validatePorts(ports) {
    for (const [service, port] of Object.entries(ports)) {
      if (await this.isPortInUse(port)) {
        return {
          success: false,
          message: `Port ${port} (${service}) is already in use`,
          conflict: { service, port },
        };
      }
    }

    return {
      success: true,
      message: "All ports are available",
    };
  }

  /**
   * Get process information for a port (Windows specific)
   */
  async getPortProcessInfo(port) {
    try {
      const { stdout } = await execAsync(`netstat -ano | findstr ":${port}"`, {
        timeout: 3000,
      });
      const lines = stdout.trim().split("\n");

      if (lines.length > 0) {
        const parts = lines[0].trim().split(/\s+/);
        const pid = parseInt(parts[parts.length - 1]);

        if (!isNaN(pid)) {
          try {
            const { stdout: processInfo } = await execAsync(
              `powershell -Command "Get-Process -Id ${pid} -ErrorAction SilentlyContinue | Select-Object Name, ProcessName | ConvertTo-Json"`,
              { timeout: 2000 }
            );

            const processData = JSON.parse(processInfo);
            return {
              pid,
              name: processData.ProcessName || processData.Name,
              port,
            };
          } catch (processError) {
            return { pid, name: "unknown", port };
          }
        }
      }
    } catch (error) {
      // Port not in use or error getting info
    }

    return null;
  }

  /**
   * Check if a process belongs to our project
   */
  isOurProcess(processInfo) {
    if (!processInfo) return false;

    const ourIndicators = ["node.exe", "next", "nodemon", "dl-organizer"];

    return ourIndicators.some((indicator) =>
      processInfo.name?.toLowerCase().includes(indicator.toLowerCase())
    );
  }

  /**
   * Generate unique session ID
   */
  generateSessionId() {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Print memory statistics
   */
  printStatistics() {
    console.log("\n📊 PORT MEMORY STATISTICS:");
    console.log(
      `  Total sessions remembered: ${Object.keys(this.memory.sessions).length}`
    );
    console.log(
      `  Successful boots: ${this.successPatterns.statistics.successfulBoots}`
    );
    console.log(
      `  Conflict resolutions: ${this.successPatterns.statistics.conflictResolutions}`
    );
    console.log(`  Success patterns: ${this.successPatterns.patterns.length}`);
    console.log(
      `  Active warnings: ${
        (this.warnings.active || []).length +
        (this.warnings.critical || []).length
      }`
    );

    if (this.successPatterns.bestConfiguration) {
      const best = this.successPatterns.bestConfiguration;
      console.log(
        `  Best configuration: Frontend=${best.ports.frontend}, Backend=${best.ports.backend} (${best.count} successes)`
      );
    }

    console.log("");
  }
}

module.exports = PortMemorySystem;

// CLI interface
if (require.main === module) {
  const system = new PortMemorySystem();
  const args = process.argv.slice(2);
  const command = args[0] || "resolve";

  (async () => {
    switch (command) {
      case "resolve":
        console.log("🛡️ Starting bulletproof port resolution...");
        const result = await system.bulletproofResolve();
        console.log("✅ Resolution complete:", result);
        break;

      case "stats":
        system.printStatistics();
        break;

      case "warnings":
        system.displayWarnings();
        break;

      case "test":
        console.log("🧪 Testing port memory system...");
        await system.bulletproofResolve();
        system.printStatistics();
        break;

      default:
        console.log(`
Port Memory System - Bulletproof Port Management

Commands:
  resolve  - Perform bulletproof port resolution (default)
  stats    - Show memory statistics
  warnings - Display active warnings
  test     - Test the system

This system PREVENTS port hunting loops forever!
`);
        break;
    }
  })().catch((error) => {
    console.error("❌ Port memory system failed:", error.message);
    process.exit(1);
  });
}
