const express = require('express');
const ModelValidator = require('../services/model-validator');
const router = express.Router();

// Initialize the model validator
const modelValidator = new ModelValidator();

// Initialize the validator with OpenRouter API key
const initializeValidator = () => {
  // Try to get API key from environment or config
  const apiKey = process.env.OPENROUTER_API_KEY || 
                 process.env.OPENAI_API_KEY || 
                 'sk-or-v1-1786c4a6335f5f6c59e2c5d8c2c5b8a5f6c59e2c5d8c2c5b8a5f6c59e2ca47';
  
  if (apiKey && apiKey.startsWith('sk-or-v1-')) {
    return modelValidator.initialize(apiKey);
  } else {
    console.warn('⚠️ Model Validation: No valid OpenRouter API key found');
    return false;
  }
};

// Initialize on module load
initializeValidator();

/**
 * GET /api/model-validation/status
 * Get the current status of the model validator
 */
router.get('/status', (req, res) => {
  try {
    const status = modelValidator.getStatus();
    res.json({
      success: true,
      status: status
    });
  } catch (error) {
    console.error('Model validation status error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get validation status',
      details: error.message
    });
  }
});

/**
 * GET /api/model-validation/available-models
 * Get all available models from OpenRouter
 */
router.get('/available-models', async (req, res) => {
  try {
    if (!modelValidator.getStatus().initialized) {
      return res.status(503).json({
        success: false,
        error: 'Model validator not initialized',
        details: 'OpenRouter API key not configured'
      });
    }

    const models = await modelValidator.getOpenRouterModels();
    res.json({
      success: true,
      models: models,
      count: models.length,
      lastUpdated: new Date().toISOString()
    });
  } catch (error) {
    console.error('Available models fetch error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch available models',
      details: error.message
    });
  }
});

/**
 * POST /api/model-validation/validate-model
 * Validate a specific model
 */
router.post('/validate-model', async (req, res) => {
  try {
    const { modelId } = req.body;
    
    if (!modelId) {
      return res.status(400).json({
        success: false,
        error: 'Model ID is required'
      });
    }

    if (!modelValidator.getStatus().initialized) {
      return res.status(503).json({
        success: false,
        error: 'Model validator not initialized',
        details: 'OpenRouter API key not configured'
      });
    }

    const validation = await modelValidator.validateModel(modelId);
    res.json({
      success: true,
      validation: validation
    });
  } catch (error) {
    console.error('Model validation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate model',
      details: error.message
    });
  }
});

/**
 * POST /api/model-validation/validate-all
 * Validate all models in a provided list
 */
router.post('/validate-all', async (req, res) => {
  try {
    const { models } = req.body;
    
    if (!models || !Array.isArray(models)) {
      return res.status(400).json({
        success: false,
        error: 'Models array is required'
      });
    }

    if (!modelValidator.getStatus().initialized) {
      return res.status(503).json({
        success: false,
        error: 'Model validator not initialized',
        details: 'OpenRouter API key not configured'
      });
    }

    console.log(`🔍 Model Validation API: Validating ${models.length} models`);
    const validation = await modelValidator.validateAllModels(models);
    
    res.json({
      success: true,
      validation: validation
    });
  } catch (error) {
    console.error('Bulk model validation error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to validate models',
      details: error.message
    });
  }
});

/**
 * POST /api/model-validation/auto-fix
 * Auto-fix model configuration by replacing invalid models
 */
router.post('/auto-fix', async (req, res) => {
  try {
    const { models } = req.body;
    
    if (!models || !Array.isArray(models)) {
      return res.status(400).json({
        success: false,
        error: 'Models array is required'
      });
    }

    if (!modelValidator.getStatus().initialized) {
      return res.status(503).json({
        success: false,
        error: 'Model validator not initialized',
        details: 'OpenRouter API key not configured'
      });
    }

    console.log(`🔧 Model Validation API: Auto-fixing ${models.length} models`);
    const fixResult = await modelValidator.autoFixModels(models);
    
    res.json({
      success: true,
      fixResult: fixResult
    });
  } catch (error) {
    console.error('Model auto-fix error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to auto-fix models',
      details: error.message
    });
  }
});

/**
 * POST /api/model-validation/suggestions
 * Get suggestions for fixing invalid models
 */
router.post('/suggestions', async (req, res) => {
  try {
    const { invalidModels } = req.body;
    
    if (!invalidModels || !Array.isArray(invalidModels)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid models array is required'
      });
    }

    if (!modelValidator.getStatus().initialized) {
      return res.status(503).json({
        success: false,
        error: 'Model validator not initialized',
        details: 'OpenRouter API key not configured'
      });
    }

    const suggestions = await modelValidator.getSuggestedFixes(invalidModels);
    
    res.json({
      success: true,
      suggestions: suggestions
    });
  } catch (error) {
    console.error('Model suggestions error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get model suggestions',
      details: error.message
    });
  }
});

/**
 * POST /api/model-validation/clear-cache
 * Clear the validation cache
 */
router.post('/clear-cache', (req, res) => {
  try {
    modelValidator.clearCache();
    res.json({
      success: true,
      message: 'Validation cache cleared'
    });
  } catch (error) {
    console.error('Cache clear error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear cache',
      details: error.message
    });
  }
});

/**
 * POST /api/model-validation/reinitialize
 * Reinitialize the validator with new API key
 */
router.post('/reinitialize', (req, res) => {
  try {
    const { apiKey } = req.body;
    
    let success = false;
    if (apiKey) {
      success = modelValidator.initialize(apiKey);
    } else {
      success = initializeValidator();
    }
    
    res.json({
      success: success,
      message: success ? 'Validator reinitialized successfully' : 'Failed to initialize validator',
      status: modelValidator.getStatus()
    });
  } catch (error) {
    console.error('Validator reinitialize error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to reinitialize validator',
      details: error.message
    });
  }
});

module.exports = router;