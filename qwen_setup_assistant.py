#!/usr/bin/env python3
"""
Qwen Code Setup Assistant
Helps configure Qwen Code CLI properly with working API endpoints
"""

import os
import sys
import subprocess
from pathlib import Path

def check_qwen_installation():
    """Check if Qwen Code is properly installed"""
    try:
        result = subprocess.run(['qwen', '--version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ Qwen Code is installed: v{result.stdout.strip()}")
            return True
        else:
            print(f"❌ Qwen Code installation issue: {result.stderr}")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError) as e:
        print(f"❌ Qwen Code not found or not working: {e}")
        return False

def show_config_options():
    """Display configuration options for Qwen Code"""
    print("\n🔧 Qwen Code Configuration Options:")
    print("=" * 50)
    
    print("\n1. 🌍 Alibaba Cloud (Recommended)")
    print("   For China mainland users:")
    print("   • Go to: https://bailian.console.aliyun.com/")
    print("   • Get your API key")
    print("   • Set: QWEN_API_KEY=your_key")
    print("   • Set: QWEN_BASE_URL=https://bailian.aliyuncs.com/v1/chat/completions")
    
    print("\n   For international users:")
    print("   • Go to: https://modelstudio.console.alibabacloud.com/")
    print("   • Get your API key")
    print("   • Set: QWEN_API_KEY=your_key")
    print("   • Set: QWEN_BASE_URL=https://modelstudio.aliyuncs.com/v1/chat/completions")
    
    print("\n2. 🤖 OpenAI API (Alternative)")
    print("   • Get API key from: https://platform.openai.com/")
    print("   • Set: OPENAI_API_KEY=your_key")
    print("   • Use model: gpt-4 or gpt-3.5-turbo")
    
    print("\n3. 🏠 Local Model Server")
    print("   • Install Ollama: https://ollama.com/")
    print("   • Run: ollama pull qwen2.5-coder")
    print("   • Set: QWEN_BASE_URL=http://localhost:11434/v1")
    
    print("\n❌ What WON'T work:")
    print("   • OpenRouter free tier (no tool calling support)")
    print("   • Models without function calling capabilities")

def test_qwen_with_simple_prompt():
    """Test Qwen Code with a simple non-tool prompt"""
    print("\n🧪 Testing Qwen Code with simple prompt...")
    try:
        # Test with a simple prompt that doesn't require tools
        result = subprocess.run([
            'qwen', 
            '--prompt', 
            'Hello! Can you say hello back? This is just a simple test.'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ Basic Qwen Code functionality works!")
            print(f"Response: {result.stdout[:200]}...")
            return True
        else:
            print(f"❌ Qwen Code test failed: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("⏰ Qwen Code test timed out (this might indicate API issues)")
        return False
    except Exception as e:
        print(f"❌ Qwen Code test error: {e}")
        return False

def suggest_working_alternatives():
    """Suggest working alternatives to Qwen Code"""
    print("\n🔄 Alternative Solutions:")
    print("=" * 30)
    
    print("\n1. 📝 Use Claude Desktop (you're already here!)")
    print("   • You have full shell access through desktop-commander")
    print("   • I can help you with coding tasks directly")
    print("   • No additional setup needed")
    
    print("\n2. 🛠️ Continue.dev with VS Code")
    print("   • Install Continue extension in VS Code")
    print("   • Configure with working API (OpenAI, Anthropic, etc.)")
    print("   • Similar functionality to Qwen Code")
    
    print("\n3. 🔧 Cursor IDE")
    print("   • Built-in AI coding assistant")
    print("   • Works with multiple providers")
    print("   • No CLI setup required")

def main():
    """Main setup assistant"""
    print("🚀 Qwen Code Setup Assistant")
    print("=" * 40)
    
    # Check installation
    if not check_qwen_installation():
        print("\n❌ Please install Qwen Code first:")
        print("npm install -g @qwen-code/qwen-code")
        return
    
    # Show current issue
    print("\n🐛 Current Issue:")
    print("Your Qwen Code is trying to use OpenRouter's free tier,")
    print("but free models don't support tool calling (function calling),")
    print("which is required for Qwen Code to work properly.")
    
    # Show configuration options
    show_config_options()
    
    # Test current setup
    print("\n" + "="*50)
    basic_works = test_qwen_with_simple_prompt()
    
    if not basic_works:
        print("\n❌ Qwen Code is not working with current configuration.")
        suggest_working_alternatives()
    
    print("\n💡 Quick Fix Recommendations:")
    print("1. Get a proper API key from Alibaba Cloud")
    print("2. Or use OpenAI API if you have credits")
    print("3. Or use me (Claude Desktop) for your coding needs!")
    
    print("\n📝 I can help you with coding tasks right now using desktop-commander.")
    print("What would you like to work on?")

if __name__ == "__main__":
    main()
