"use client"

import React, { useState, useEffect, useC<PERSON>back, createContext, useContext } from 'react'
import { dedupeAndThrottleRequest } from '../utils/request-deduper'

export interface VisionModel {
  id: string
  name: string
  provider: string
  cost: string
  description: string
  tier: 'free' | 'paid'
  contextWindow: number
  maxOutputTokens: number
}

export interface OpenRouterConfig {
  apiKey: string
  selectedModel: string
  baseUrl: string
  isEnabled: boolean
  customModel?: string
  ocrMode: 'us' | 'australian' | 'auto-detect'
}

export interface OCRSettings {
  extractionType: 'driver_license' | 'passport' | 'id_card' | 'general' | 'auto-detect'
  processingOptions: {
    autoSave: boolean
    showConfidence: boolean
    batchMode: boolean
    highAccuracy: boolean
  }
  qualitySettings: {
    minConfidence: number
    retryFailures: boolean
    enhanceImages: boolean
  }
}

export interface SettingsState {
  openRouterConfig: OpenRouterConfig
  ocrSettings: OCRSettings
  isLoading: boolean
  error: string | null
  connectionStatus: 'unknown' | 'connected' | 'failed'
  lastUpdated: Date | null
}

interface SettingsActions {
  loadSettings: () => Promise<void>
  saveOpenRouterConfig: (config: OpenRouterConfig) => Promise<void>
  saveOCRSettings: (settings: OCRSettings) => Promise<void>
  testConnection: () => Promise<boolean>
  updateModel: (modelId: string) => void
  updateOCRMode: (mode: 'us' | 'australian' | 'auto-detect') => void
  clearError: () => void
  isSettingsLoaded: boolean
}

export type SettingsContextType = SettingsState & SettingsActions

// Default settings
const defaultOpenRouterConfig: OpenRouterConfig = {
  apiKey: '',
  selectedModel: 'openai/gpt-4o-mini',
  baseUrl: 'https://openrouter.ai/api/v1',
  isEnabled: false,
  customModel: '',
  ocrMode: 'auto-detect'
}

const defaultOCRSettings: OCRSettings = {
  extractionType: 'auto-detect',
  processingOptions: {
    autoSave: true,
    showConfidence: true,
    batchMode: false,
    highAccuracy: false
  },
  qualitySettings: {
    minConfidence: 0.7,
    retryFailures: true,
    enhanceImages: false
  }
}

// Create context for settings synchronization
const SettingsContext = createContext<SettingsContextType | undefined>(undefined)

export function useSettingsSync(): SettingsContextType {
  const [state, setState] = useState<SettingsState>({
    openRouterConfig: defaultOpenRouterConfig,
    ocrSettings: defaultOCRSettings,
    isLoading: false,
    error: null,
    connectionStatus: 'unknown',
    lastUpdated: null
  })

  // Load settings from API
  const loadSettings = useCallback(async () => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      // Load OpenRouter config with deduplication to prevent React Strict Mode double-calling
      const openRouterConfig = await dedupeAndThrottleRequest(
        'settings-openrouter',
        async () => {
          const response = await fetch('/api/settings/openrouter')
          if (response.ok) {
            const data = await response.json()
            return { ...defaultOpenRouterConfig, ...data }
          }
          return defaultOpenRouterConfig
        },
        500 // 500ms throttle for settings
      )

      // Load OCR settings with deduplication
      const ocrSettings = await dedupeAndThrottleRequest(
        'settings-ocr',
        async () => {
          const response = await fetch('/api/settings/ocr')
          if (response.ok) {
            const data = await response.json()
            return { ...defaultOCRSettings, ...data }
          }
          return defaultOCRSettings
        },
        500 // 500ms throttle for settings
      )

      setState(prev => ({
        ...prev,
        openRouterConfig,
        ocrSettings,
        isLoading: false,
        lastUpdated: new Date()
      }))

    } catch (error) {
      console.error('Settings load error:', error)
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load settings'
      }))
    }
  }, [])

  // Save OpenRouter configuration
  const saveOpenRouterConfig = useCallback(async (config: OpenRouterConfig) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      const response = await fetch('/api/settings/openrouter', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(config)
      })

      if (!response.ok) {
        throw new Error('Failed to save OpenRouter configuration')
      }

      setState(prev => ({
        ...prev,
        openRouterConfig: config,
        isLoading: false,
        lastUpdated: new Date()
      }))

      // Emit event for other components to listen to
      window.dispatchEvent(new CustomEvent('settings-updated', { 
        detail: { type: 'openrouter', config } 
      }))

    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to save configuration'
      }))
      throw error
    }
  }, [])

  // Save OCR settings
  const saveOCRSettings = useCallback(async (settings: OCRSettings) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      const response = await fetch('/api/settings/ocr', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(settings)
      })

      if (!response.ok) {
        throw new Error('Failed to save OCR settings')
      }

      setState(prev => ({
        ...prev,
        ocrSettings: settings,
        isLoading: false,
        lastUpdated: new Date()
      }))

      // Emit event for other components to listen to
      window.dispatchEvent(new CustomEvent('settings-updated', { 
        detail: { type: 'ocr', settings } 
      }))

    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to save OCR settings'
      }))
      throw error
    }
  }, [])

  // Test API connection
  const testConnection = useCallback(async (): Promise<boolean> => {
    setState(prev => ({ ...prev, isLoading: true, error: null }))
    
    try {
      const response = await fetch('/api/settings/openrouter/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          apiKey: state.openRouterConfig.apiKey,
          model: state.openRouterConfig.selectedModel,
          baseUrl: state.openRouterConfig.baseUrl
        })
      })

      const success = response.ok
      setState(prev => ({
        ...prev,
        connectionStatus: success ? 'connected' : 'failed',
        isLoading: false,
        error: success ? null : 'Connection test failed'
      }))

      return success

    } catch (error) {
      setState(prev => ({
        ...prev,
        connectionStatus: 'failed',
        isLoading: false,
        error: error instanceof Error ? error.message : 'Connection test failed'
      }))
      return false
    }
  }, [state.openRouterConfig])

  // Quick update methods for common operations
  const updateModel = useCallback((modelId: string) => {
    setState(prev => ({
      ...prev,
      openRouterConfig: {
        ...prev.openRouterConfig,
        selectedModel: modelId
      }
    }))
  }, [])

  const updateOCRMode = useCallback((mode: 'us' | 'australian' | 'auto-detect') => {
    setState(prev => ({
      ...prev,
      openRouterConfig: {
        ...prev.openRouterConfig,
        ocrMode: mode
      }
    }))
  }, [])

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  // Listen for settings updates from other components
  useEffect(() => {
    const handleSettingsUpdate = (event: CustomEvent) => {
      const { type, config, settings } = event.detail
      
      setState(prev => ({
        ...prev,
        ...(type === 'openrouter' && { openRouterConfig: config }),
        ...(type === 'ocr' && { ocrSettings: settings }),
        lastUpdated: new Date()
      }))
    }

    window.addEventListener('settings-updated', handleSettingsUpdate as EventListener)
    
    return () => {
      window.removeEventListener('settings-updated', handleSettingsUpdate as EventListener)
    }
  }, [])

  // Add a helper to determine if settings are fully loaded
  const isSettingsLoaded = Boolean(
    state.lastUpdated !== null && 
    (state.openRouterConfig.apiKey || state.connectionStatus !== 'unknown')
  )

  // Load settings on mount only (no dependencies to avoid infinite loops)
  useEffect(() => {
    const initializeSettings = async () => {
      await loadSettings()
    }
    initializeSettings()
  }, [loadSettings]) // Include loadSettings dependency

  // Auto-test connection when API key becomes available (after initial load)
  useEffect(() => {
    if (state.openRouterConfig.apiKey && state.connectionStatus === 'unknown' && !state.isLoading) {
      testConnection()
    }
  }, [state.openRouterConfig.apiKey, state.connectionStatus, state.isLoading, testConnection])

  return {
    ...state,
    loadSettings,
    saveOpenRouterConfig,
    saveOCRSettings,
    testConnection,
    updateModel,
    updateOCRMode,
    clearError,
    isSettingsLoaded
  }
}

// Context provider for sharing settings across components
export function SettingsProvider({ children }: { children: React.ReactNode }) {
  const settings = useSettingsSync()
  
  return (
    <SettingsContext.Provider value={settings}>
      {children}
    </SettingsContext.Provider>
  )
}

// Hook to use settings context
export function useSettings(): SettingsContextType {
  const context = useContext(SettingsContext)
  if (!context) {
    throw new Error('useSettings must be used within a SettingsProvider')
  }
  return context
}

// Standalone hook for components that don't need context
export { useSettingsSync as useSettingsStandalone }