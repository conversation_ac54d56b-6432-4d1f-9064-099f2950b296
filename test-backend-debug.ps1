# Test backend debug route directly
$body = @{
    folderPath = "C:\claude\dl-organizer\test-samples\i18"
    recursive = $false
} | ConvertTo-Json

Write-Host "Testing backend debug route with body: $body"

try {
    $response = Invoke-RestMethod -Uri "http://127.0.0.1:3574/api/filesystem/images-debug" -Method POST -Body $body -ContentType "application/json"
    Write-Host "Backend Debug API Response:"
    $response | ConvertTo-Json -Depth 5
} catch {
    Write-Host "Backend Debug API Error: $_"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody"
    }
}