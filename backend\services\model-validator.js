const OpenAI = require('openai');

class ModelValidator {
  constructor() {
    this.openRouterClient = null;
    this.lastValidation = null;
    this.validationCache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    this.validationInProgress = false;
  }

  /**
   * Initialize the OpenRouter client for model validation
   */
  initialize(openRouterApiKey) {
    if (!openRouterApiKey) {
      console.warn('⚠️ Model Validator: No OpenRouter API key provided');
      return false;
    }

    try {
      this.openRouterClient = new OpenAI({
        baseURL: "https://openrouter.ai/api/v1",
        apiKey: openRouterApiKey,
      });
      console.log('✅ Model Validator: OpenRouter client initialized');
      return true;
    } catch (error) {
      console.error('❌ Model Validator: Failed to initialize OpenRouter client:', error.message);
      return false;
    }
  }

  /**
   * Get all available models from OpenRouter API
   */
  async getOpenRouterModels() {
    if (!this.openRouterClient) {
      throw new Error('OpenRouter client not initialized');
    }

    try {
      console.log('🔍 Model Validator: Fetching models from OpenRouter API...');
      const response = await this.openRouterClient.models.list();
      
      // Filter for vision-capable models
      const visionModels = response.data.filter(model => {
        const modelName = model.id.toLowerCase();
        return (
          modelName.includes('vision') ||
          modelName.includes('gpt-4o') ||
          modelName.includes('claude') ||
          modelName.includes('gemini') ||
          modelName.includes('llama-3.2') ||
          modelName.includes('qwen') ||
          modelName.includes('phi-3')
        );
      });

      console.log(`📋 Model Validator: Found ${visionModels.length} vision-capable models from OpenRouter`);
      return visionModels;
    } catch (error) {
      console.error('❌ Model Validator: Failed to fetch OpenRouter models:', error.message);
      throw error;
    }
  }

  /**
   * Validate a specific model against OpenRouter API
   */
  async validateModel(modelId) {
    if (!this.openRouterClient) {
      return {
        modelId,
        valid: false,
        error: 'OpenRouter client not initialized',
        lastChecked: new Date().toISOString()
      };
    }

    // Check cache first
    const cacheKey = `validate_${modelId}`;
    const cached = this.validationCache.get(cacheKey);
    if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
      console.log(`📋 Model Validator: Using cached validation for ${modelId}`);
      return cached.result;
    }

    try {
      console.log(`🧪 Model Validator: Testing model ${modelId}...`);
      
      // Get all available models
      const availableModels = await this.getOpenRouterModels();
      const modelExists = availableModels.some(model => model.id === modelId);
      
      const result = {
        modelId,
        valid: modelExists,
        error: modelExists ? null : 'Model not found in OpenRouter API',
        lastChecked: new Date().toISOString(),
        modelInfo: modelExists ? availableModels.find(m => m.id === modelId) : null
      };

      // Cache the result
      this.validationCache.set(cacheKey, {
        result,
        timestamp: Date.now()
      });

      console.log(`${result.valid ? '✅' : '❌'} Model Validator: ${modelId} is ${result.valid ? 'valid' : 'invalid'}`);
      return result;
    } catch (error) {
      const result = {
        modelId,
        valid: false,
        error: error.message,
        lastChecked: new Date().toISOString()
      };

      // Cache error result for shorter time
      this.validationCache.set(cacheKey, {
        result,
        timestamp: Date.now() - (this.cacheTimeout * 0.5) // Cache errors for half the time
      });

      console.error(`❌ Model Validator: Error validating ${modelId}:`, error.message);
      return result;
    }
  }

  /**
   * Validate all models in a given list
   */
  async validateAllModels(modelList) {
    if (this.validationInProgress) {
      console.log('⏳ Model Validator: Validation already in progress, waiting...');
      // Wait for current validation to complete
      while (this.validationInProgress) {
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      return this.lastValidation;
    }

    this.validationInProgress = true;
    console.log(`🚀 Model Validator: Starting validation of ${modelList.length} models...`);

    try {
      const results = [];
      const batchSize = 3; // Process in small batches to avoid rate limits
      
      for (let i = 0; i < modelList.length; i += batchSize) {
        const batch = modelList.slice(i, i + batchSize);
        console.log(`📦 Model Validator: Processing batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(modelList.length/batchSize)}`);
        
        const batchPromises = batch.map(model => this.validateModel(model.id || model));
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
        
        // Small delay between batches to be respectful to the API
        if (i + batchSize < modelList.length) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }

      const validationSummary = {
        totalModels: results.length,
        validModels: results.filter(r => r.valid).length,
        invalidModels: results.filter(r => !r.valid).length,
        results: results,
        timestamp: new Date().toISOString()
      };

      this.lastValidation = validationSummary;
      console.log(`🎯 Model Validator: Validation complete - ${validationSummary.validModels}/${validationSummary.totalModels} models are valid`);
      
      // Log invalid models for attention
      const invalidModels = results.filter(r => !r.valid);
      if (invalidModels.length > 0) {
        console.warn('⚠️ Model Validator: Invalid models found:');
        invalidModels.forEach(model => {
          console.warn(`   - ${model.modelId}: ${model.error}`);
        });
      }

      return validationSummary;
    } catch (error) {
      console.error('❌ Model Validator: Failed to validate models:', error.message);
      throw error;
    } finally {
      this.validationInProgress = false;
    }
  }

  /**
   * Get suggested fixes for invalid models
   */
  async getSuggestedFixes(invalidModels) {
    if (!this.openRouterClient) {
      return [];
    }

    try {
      const availableModels = await this.getOpenRouterModels();
      const suggestions = [];

      for (const invalidModel of invalidModels) {
        const modelId = invalidModel.modelId;
        
        // Find similar models
        const similarModels = availableModels.filter(available => {
          const availableId = available.id.toLowerCase();
          const invalidId = modelId.toLowerCase();
          
          // Check for similar provider and model type
          const providerMatch = availableId.split('/')[0] === invalidId.split('/')[0];
          const nameMatch = availableId.includes(invalidId.split('/')[1]?.split('-')[0] || '') ||
                           invalidId.includes(availableId.split('/')[1]?.split('-')[0] || '');
          
          return providerMatch || nameMatch;
        });

        suggestions.push({
          invalidModel: modelId,
          error: invalidModel.error,
          suggestedAlternatives: similarModels.slice(0, 3).map(model => ({
            id: model.id,
            name: model.name,
            description: model.description
          })),
          autoFixRecommendation: similarModels.length > 0 ? similarModels[0].id : null
        });
      }

      return suggestions;
    } catch (error) {
      console.error('❌ Model Validator: Failed to generate suggestions:', error.message);
      return [];
    }
  }

  /**
   * Auto-fix configuration by replacing invalid models with valid alternatives
   */
  async autoFixModels(modelList) {
    console.log('🔧 Model Validator: Starting auto-fix process...');
    
    const validation = await this.validateAllModels(modelList);
    const invalidModels = validation.results.filter(r => !r.valid);
    
    if (invalidModels.length === 0) {
      console.log('✅ Model Validator: All models are valid, no fixes needed');
      return {
        success: true,
        message: 'All models are valid',
        originalModels: modelList,
        fixedModels: modelList,
        changes: []
      };
    }

    const suggestions = await this.getSuggestedFixes(invalidModels);
    const changes = [];
    const fixedModels = [...modelList];

    for (const suggestion of suggestions) {
      if (suggestion.autoFixRecommendation) {
        const originalIndex = fixedModels.findIndex(model => 
          (model.id || model) === suggestion.invalidModel
        );
        
        if (originalIndex !== -1) {
          const oldModel = fixedModels[originalIndex];
          const newModelId = suggestion.autoFixRecommendation;
          
          if (typeof oldModel === 'string') {
            fixedModels[originalIndex] = newModelId;
          } else {
            fixedModels[originalIndex] = {
              ...oldModel,
              id: newModelId
            };
          }
          
          changes.push({
            action: 'replace',
            original: suggestion.invalidModel,
            replacement: newModelId,
            reason: `Original model not available: ${suggestion.error}`
          });
          
          console.log(`🔄 Model Validator: Fixed ${suggestion.invalidModel} → ${newModelId}`);
        }
      } else {
        changes.push({
          action: 'remove',
          original: suggestion.invalidModel,
          replacement: null,
          reason: `No suitable replacement found: ${suggestion.error}`
        });
        
        // Remove invalid model with no replacement
        const removeIndex = fixedModels.findIndex(model => 
          (model.id || model) === suggestion.invalidModel
        );
        if (removeIndex !== -1) {
          fixedModels.splice(removeIndex, 1);
          console.log(`🗑️ Model Validator: Removed ${suggestion.invalidModel} (no replacement available)`);
        }
      }
    }

    return {
      success: true,
      message: `Fixed ${changes.length} model issues`,
      originalModels: modelList,
      fixedModels: fixedModels,
      changes: changes,
      validation: validation
    };
  }

  /**
   * Clear validation cache
   */
  clearCache() {
    this.validationCache.clear();
    console.log('🧹 Model Validator: Cache cleared');
  }

  /**
   * Get validation status
   */
  getStatus() {
    return {
      initialized: !!this.openRouterClient,
      cacheSize: this.validationCache.size,
      lastValidation: this.lastValidation,
      validationInProgress: this.validationInProgress
    };
  }
}

module.exports = ModelValidator;