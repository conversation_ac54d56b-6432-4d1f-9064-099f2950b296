import { NextRequest, NextResponse } from "next/server";
import fs from "fs";
import path from "path";

interface PortConfig {
  ports: {
    frontend: number;
    backend: number;
    ngrok: number;
  };
  environment: {
    FRONTEND_PORT: string;
    BACKEND_PORT: string;
    NGROK_PORT: string;
    PORT: string;
  };
  timestamp: string;
  processInfo?: any;
}

/**
 * Get port configuration from the SINGLE SOURCE OF TRUTH
 */
function getPortConfigFromDataFile(): PortConfig | null {
  try {
    // Try to read from data/port-config.json first (most current)
    const configPath = path.join(process.cwd(), "data", "port-config.json");

    if (fs.existsSync(configPath)) {
      const config: PortConfig = JSON.parse(
        fs.readFileSync(configPath, "utf8")
      );

      // Check if configuration is not too old (10 minutes)
      const configAge = Date.now() - new Date(config.timestamp).getTime();
      if (configAge < 10 * 60 * 1000) {
        console.log('📡 Port config API: Serving fresh configuration from data file');
        return config;
      } else {
        console.warn("⚠️ Port configuration is stale, using fallback");
      }
    } else {
      console.warn("⚠️ Port configuration file not found");
    }
  } catch (error) {
    console.warn("❌ Failed to load port configuration from JSON:", error);
  }

  return null;
}

/**
 * GET /api/port-config
 * Returns the current port configuration with intelligent fallbacks
 */
export async function GET(request: NextRequest) {
  try {
    // Try to get the current configuration from data file
    const config = getPortConfigFromDataFile();

    if (config) {
      return NextResponse.json(config, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
    } else {
      // Return fallback configuration based on current session
      const fallbackConfig: PortConfig = {
        ports: {
          frontend: 3110,
          backend: 3574,
          ngrok: 4040,
        },
        environment: {
          FRONTEND_PORT: "3110",
          BACKEND_PORT: "3574",
          NGROK_PORT: "4040",
          PORT: "3110",
        },
        timestamp: new Date().toISOString(),
        processInfo: {
          source: "api-fallback",
          note: "Using current session defaults - configuration file not available"
        },
      };

      console.log('📡 Port config API: Serving fallback configuration');
      
      return NextResponse.json(fallbackConfig, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });
    }
  } catch (error) {
    console.error("❌ Error getting port configuration:", error);

    // Return error response with safe fallback
    const fallbackConfig: PortConfig = {
      ports: {
        frontend: 3110,
        backend: 3574,
        ngrok: 4040,
      },
      environment: {
        FRONTEND_PORT: "3110",
        BACKEND_PORT: "3574",
        NGROK_PORT: "4040",
        PORT: "3110",
      },
      timestamp: new Date().toISOString(),
      processInfo: {
        source: "api-error-fallback",
        error: error instanceof Error ? error.message : "Unknown error",
      },
    };

    return NextResponse.json(fallbackConfig, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  }
}

/**
 * POST /api/port-config
 * Future endpoint for updating port configuration
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // For now, just log the request and return not implemented
    console.log('📡 Port config API: POST request received:', body);
    
    return NextResponse.json(
      { 
        error: 'POST method not yet implemented for port configuration updates',
        note: 'This endpoint is reserved for future port configuration management'
      },
      { status: 501 }
    );
  } catch (error) {
    return NextResponse.json(
      { error: 'Invalid request body' },
      { status: 400 }
    );
  }
}