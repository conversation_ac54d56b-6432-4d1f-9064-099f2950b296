// Australian OCR Field Merging Service
// Handles intelligent merging of front and back card data for Australian driver licenses

import { OCRResult, AustralianOCRResult } from '@/types'

export interface CardSideData {
  side: 'front' | 'back'
  data: Partial<OCRResult>
  confidence: number
  timestamp: Date
}

export interface MergeResult {
  mergedData: AustralianOCRResult
  completeness: number // 0-1 score of how complete the data is
  missingFields: string[]
  fieldSources: Record<string, 'front' | 'back' | 'merged'>
  suggestions: string[]
}

/**
 * Australian Driver License Field Priority Matrix
 * Defines which card side typically contains which fields
 */
const FIELD_PRIORITY_MATRIX: Record<string, { primary: 'front' | 'back', secondary?: 'front' | 'back' }> = {
  // Front side primary fields
  surname: { primary: 'front' },
  givenNames: { primary: 'front' },
  firstName: { primary: 'front' },
  middleName: { primary: 'front' },
  lastName: { primary: 'front' },
  licenseNumber: { primary: 'front' },
  state: { primary: 'front' },
  expirationDate: { primary: 'front' },
  
  // Back side primary fields (address often on back in some states)
  address: { primary: 'back', secondary: 'front' },
  
  // Fields that can appear on either side (state-dependent)
  dateOfBirth: { primary: 'front', secondary: 'back' },
  cardNumber: { primary: 'front', secondary: 'back' }
}

/**
 * State-specific field location mapping
 * Some Australian states have different layouts
 */
const STATE_SPECIFIC_LAYOUTS: Record<string, Partial<typeof FIELD_PRIORITY_MATRIX>> = {
  'NSW': {
    address: { primary: 'back' },
    cardNumber: { primary: 'front', secondary: 'back' }
  },
  'VIC': {
    address: { primary: 'front' },
    cardNumber: { primary: 'back' }
  },
  'QLD': {
    address: { primary: 'back' },
    cardNumber: { primary: 'front' }
  },
  'WA': {
    address: { primary: 'back' },
    cardNumber: { primary: 'front' }
  },
  'SA': {
    address: { primary: 'back' },
    cardNumber: { primary: 'front' }
  },
  'TAS': {
    address: { primary: 'front' },
    cardNumber: { primary: 'front' }
  },
  'NT': {
    address: { primary: 'back' },
    cardNumber: { primary: 'front' }
  },
  'ACT': {
    address: { primary: 'back' },
    cardNumber: { primary: 'front' }
  }
}

/**
 * Intelligent field merger for Australian driver licenses
 */
export class AustralianOCRMerger {
  private frontData: CardSideData | null = null
  private backData: CardSideData | null = null

  /**
   * Add data from a card side
   */
  addCardData(sideData: CardSideData): void {
    if (sideData.side === 'front') {
      this.frontData = sideData
    } else {
      this.backData = sideData
    }
  }

  /**
   * Clear all stored data
   */
  clear(): void {
    this.frontData = null
    this.backData = null
  }

  /**
   * Check if we have data from both sides
   */
  hasBothSides(): boolean {
    return this.frontData !== null && this.backData !== null
  }

  /**
   * Get field value using priority matrix and state-specific rules
   */
  private getFieldValue(field: string, state?: string): {
    value: string | undefined
    source: 'front' | 'back' | 'merged'
    confidence: number
  } {
    // Get state-specific layout or fall back to default
    const stateLayout = state ? STATE_SPECIFIC_LAYOUTS[state.toUpperCase()] : {}
    const fieldPriority = stateLayout[field] || FIELD_PRIORITY_MATRIX[field] || { primary: 'front' }

    const frontValue = this.frontData?.data[field as keyof OCRResult] as string
    const backValue = this.backData?.data[field as keyof OCRResult] as string
    const frontConfidence = this.frontData?.confidence || 0
    const backConfidence = this.backData?.confidence || 0

    // If only one side has the value, use it
    if (frontValue && !backValue) {
      return { value: frontValue, source: 'front', confidence: frontConfidence }
    }
    if (backValue && !frontValue) {
      return { value: backValue, source: 'back', confidence: backConfidence }
    }

    // If both sides have values, use priority and confidence
    if (frontValue && backValue) {
      const primarySide = fieldPriority.primary
      const isPrimaryFront = primarySide === 'front'
      const primaryValue = isPrimaryFront ? frontValue : backValue
      const primaryConfidence = isPrimaryFront ? frontConfidence : backConfidence
      const secondaryValue = isPrimaryFront ? backValue : frontValue
      const secondaryConfidence = isPrimaryFront ? backConfidence : frontConfidence

      // Use primary side unless secondary has significantly higher confidence
      if (primaryConfidence >= secondaryConfidence - 0.2) {
        return { 
          value: primaryValue, 
          source: primarySide, 
          confidence: primaryConfidence 
        }
      } else {
        return { 
          value: secondaryValue, 
          source: primarySide === 'front' ? 'back' : 'front', 
          confidence: secondaryConfidence 
        }
      }
    }

    // No value found
    return { value: undefined, source: 'front', confidence: 0 }
  }

  /**
   * Parse given names into first and middle names
   */
  private parseGivenNames(givenNames: string): { firstName: string, middleName: string } {
    const names = givenNames.trim().split(/\s+/)
    return {
      firstName: names[0] || '',
      middleName: names.slice(1).join(' ')
    }
  }

  /**
   * Merge card data intelligently
   */
  merge(): MergeResult {
    if (!this.frontData && !this.backData) {
      throw new Error('No card data available for merging')
    }

    const fieldSources: Record<string, 'front' | 'back' | 'merged'> = {}
    const missingFields: string[] = []
    const suggestions: string[] = []

    // Determine state for state-specific processing
    const stateResult = this.getFieldValue('state')
    const state = stateResult.value

    // Core fields extraction
    const surname = this.getFieldValue('lastName', state) // Map lastName to surname for Australian format
    const givenNames = this.getFieldValue('firstName', state) // This will be the combined given names
    const address = this.getFieldValue('address', state)
    const dateOfBirth = this.getFieldValue('dateOfBirth', state)
    const licenseNumber = this.getFieldValue('licenseNumber', state)
    const cardNumber = this.getFieldValue('cardNumber', state)
    const expirationDate = this.getFieldValue('expirationDate', state)

    // Parse given names into components
    const parsedNames = this.parseGivenNames(givenNames.value || '')

    // Build field sources map
    fieldSources.surname = surname.source
    fieldSources.givenNames = givenNames.source
    fieldSources.address = address.source
    fieldSources.dateOfBirth = dateOfBirth.source
    fieldSources.licenseNumber = licenseNumber.source
    fieldSources.expirationDate = expirationDate.source
    fieldSources.state = stateResult.source
    if (cardNumber.value) fieldSources.cardNumber = cardNumber.source

    // Check for missing critical fields (relaxed for Australian documents)
    const criticalFields = ['surname', 'givenNames']
    criticalFields.forEach(field => {
      const fieldMap: Record<string, any> = {
        surname: surname,
        givenNames: givenNames
      }
      
      if (!fieldMap[field]?.value) {
        missingFields.push(field)
      }
    })
    
    // Check for document number (either license number or card number)
    if (!licenseNumber.value && !cardNumber.value) {
      missingFields.push('document_number')
    }
    
    // Check for optional but important fields
    const importantFields = ['state', 'expirationDate']
    importantFields.forEach(field => {
      const fieldMap: Record<string, any> = {
        state: stateResult,
        expirationDate: expirationDate
      }
      
      if (!fieldMap[field]?.value) {
        suggestions.push(`Consider adding ${field} for complete record`)
      }
    })

    // Generate suggestions based on missing data and state
    if (!address.value && state && STATE_SPECIFIC_LAYOUTS[state]?.address?.primary === 'back' && !this.backData) {
      suggestions.push('Scan the back of the license to get the address information')
    }

    if (!cardNumber.value && state && ['NSW', 'QLD'].includes(state)) {
      suggestions.push('Check for card number on both front and back sides')
    }

    if (missingFields.length > 0) {
      suggestions.push(`Missing required fields: ${missingFields.join(', ')}`)
    }

    // Calculate completeness score
    const totalFields = 8 // Total Australian fields
    const completedFields = totalFields - missingFields.length + (address.value ? 1 : 0) + (cardNumber.value ? 1 : 0)
    const completeness = Math.min(completedFields / totalFields, 1)

    // Build merged result
    const mergedData: AustralianOCRResult = {
      id: this.frontData?.data.id || this.backData?.data.id || '',
      imageId: this.frontData?.data.imageId || this.backData?.data.imageId || '',
      folderId: this.frontData?.data.folderId || this.backData?.data.folderId || '',
      surname: surname.value || '',
      givenNames: givenNames.value || '',
      firstName: parsedNames.firstName,
      middleName: parsedNames.middleName,
      lastName: surname.value || '', // Map surname to lastName for compatibility
      dateOfBirth: dateOfBirth.value || '',
      address: address.value || '',
      licenseNumber: licenseNumber.value || '',
      cardNumber: cardNumber.value,
      expirationDate: expirationDate.value || '',
      state: stateResult.value || '',
      confidence: Math.max(
        this.frontData?.confidence || 0,
        this.backData?.confidence || 0
      ),
      rawText: [
        this.frontData?.data.rawText || '',
        this.backData?.data.rawText || ''
      ].filter(Boolean).join('\n--- BACK SIDE ---\n'),
      processedAt: new Date().toISOString(),
      mode: 'australian',
      cardSide: this.hasBothSides() ? undefined : (this.frontData ? 'front' : 'back')
    }

    return {
      mergedData,
      completeness,
      missingFields,
      fieldSources,
      suggestions
    }
  }

  /**
   * Static method to merge two OCR results
   */
  static mergeTwoResults(frontResult: OCRResult, backResult: OCRResult): MergeResult {
    const merger = new AustralianOCRMerger()
    
    merger.addCardData({
      side: 'front',
      data: frontResult,
      confidence: frontResult.confidence,
      timestamp: new Date(frontResult.processedAt)
    })
    
    merger.addCardData({
      side: 'back',
      data: backResult,
      confidence: backResult.confidence,
      timestamp: new Date(backResult.processedAt)
    })
    
    return merger.merge()
  }

  /**
   * Validate merged data for Australian format
   */
  static validateAustralianData(data: AustralianOCRResult): {
    isValid: boolean
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []

    // Essential field validation for Australian documents (relaxed)
    if (!data.surname?.trim()) errors.push('Surname is required')
    if (!data.givenNames?.trim()) errors.push('Given names are required')
    
    // Australian documents use cardNumber, not licenseNumber - require either
    if (!data.cardNumber?.trim() && !data.licenseNumber?.trim()) {
      errors.push('Card number or license number is required')
    }
    
    // Optional fields that improve data quality but aren't blocking
    if (!data.state?.trim()) warnings.push('State is missing')
    if (!data.expirationDate?.trim()) warnings.push('Expiration date is missing')

    // State validation
    const validStates = ['NSW', 'VIC', 'QLD', 'WA', 'SA', 'TAS', 'NT', 'ACT']
    if (data.state && !validStates.includes(data.state.toUpperCase())) {
      errors.push(`Invalid state: ${data.state}. Must be one of: ${validStates.join(', ')}`)
    }

    // Date validation
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/
    if (data.dateOfBirth && !dateRegex.test(data.dateOfBirth)) {
      errors.push('Date of birth must be in YYYY-MM-DD format')
    }
    if (data.expirationDate && !dateRegex.test(data.expirationDate)) {
      errors.push('Expiration date must be in YYYY-MM-DD format')
    }

    // Data quality warnings
    if (!data.address?.trim()) warnings.push('Address is missing - may be on back of license')
    if (!data.dateOfBirth?.trim()) warnings.push('Date of birth is missing')
    if (data.confidence < 0.7) warnings.push('Low confidence score - please verify data accuracy')

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
}

// Export convenience functions
export const mergeAustralianCards = AustralianOCRMerger.mergeTwoResults
export const validateAustralianOCR = AustralianOCRMerger.validateAustralianData