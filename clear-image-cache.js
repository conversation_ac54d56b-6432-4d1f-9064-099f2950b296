const fs = require('fs');
const path = require('path');

// Clear image cache to force regeneration with fixed Sharp pipeline
async function clearImageCache() {
  const cacheDirs = [
    path.join(__dirname, 'data', 'thumbnails'),
    path.join(__dirname, 'data', 'previews'),
    path.join(__dirname, 'data', 'cache')
  ];

  for (const dir of cacheDirs) {
    try {
      if (fs.existsSync(dir)) {
        const files = fs.readdirSync(dir);
        for (const file of files) {
          const filePath = path.join(dir, file);
          if (fs.statSync(filePath).isFile()) {
            fs.unlinkSync(filePath);
            console.log(`Deleted: ${filePath}`);
          }
        }
        console.log(`✅ Cleared cache directory: ${dir} (${files.length} files)`);
      } else {
        console.log(`📁 Directory doesn't exist: ${dir}`);
      }
    } catch (error) {
      console.error(`❌ Error clearing ${dir}:`, error.message);
    }
  }
  
  console.log('\n🎯 Cache cleared! All thumbnails and previews will be regenerated with fixed Sharp pipeline.');
  console.log('🔄 This ensures EXIF orientation is handled properly and manual rotations work correctly.');
}

// Run the script
clearImageCache().catch(console.error);