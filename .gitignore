# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Production builds
.next/
out/
build/
dist/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database files
*.db
*.sqlite
*.sqlite3

# Data directories
data/
!data/.gitkeep

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# TypeScript build cache
tsconfig.tsbuildinfo

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows specific
*.lnk

# Backup files
*.backup
*.bak

# Test files
test-results/
playwright-report/
test-results.xml

# Sharp cache
.sharp-cache/

# API keys and secrets
.env.production
config/secrets.json
keys/

# Service files
service-config.json

# Maintenance reports
maintenance-*.json

# Claude Code session files
*-caveat-the-messages-below-were-generated-by-the-u.txt
*-this-session-is-being-continued-from-a-previous-co.txt

# Temporary testing files
test-*.html
test-*.js
layout-*.png
backend.log
nul

# Archives and temporary storage
archives/temp-session-files/
cleanup-backup/

# Project directories
files/