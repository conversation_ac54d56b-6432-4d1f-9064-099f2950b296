const fs = require('fs').promises;
const path = require('path');

class OCRCache {
  /**
   * Get the cache file path for an image with OCR parameters
   * @param {string} imagePath - The path to the image file
   * @param {Object} options - OCR parameters (extractionType, mode, modelId)
   * @returns {string} - The path to the corresponding .json cache file with parameters
   */
  static getCacheFilePath(imagePath, options = {}) {
    const dir = path.dirname(imagePath);
    const baseName = path.basename(imagePath, path.extname(imagePath));
    
    // Create a cache key suffix based on OCR parameters
    const cacheKeySuffix = this.generateCacheKeySuffix(options);
    return path.join(dir, `${baseName}_ocr_results${cacheKeySuffix}.json`);
  }

  /**
   * Generate cache key suffix based on OCR parameters
   * @param {Object} options - OCR parameters
   * @returns {string} - Cache key suffix
   */
  static generateCacheKeySuffix(options) {
    const {
      extractionType = 'driver_license',
      mode = 'us',
      modelId = 'default',
      cardSide = null
    } = options;
    
    // Create a short hash of the parameters for the filename
    const keyData = `${extractionType}_${mode}_${modelId}_${cardSide || 'none'}`;
    
    // Simple hash to keep filename reasonable length
    let hash = 0;
    for (let i = 0; i < keyData.length; i++) {
      const char = keyData.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32bit integer
    }
    
    return `_${Math.abs(hash).toString(36)}`;
  }

  /**
   * Check if a cached result exists for an image with specific parameters
   * @param {string} imagePath - The path to the image file
   * @param {Object} options - OCR parameters
   * @returns {Promise<boolean>} - True if cache exists
   */
  static async exists(imagePath, options = {}) {
    try {
      const cacheFilePath = this.getCacheFilePath(imagePath, options);
      await fs.access(cacheFilePath);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Load cached OCR results for an image with specific parameters
   * @param {string} imagePath - The path to the image file
   * @param {Object} options - OCR parameters
   * @returns {Promise<Object|null>} - The cached OCR results or null if not found
   */
  static async load(imagePath, options = {}) {
    try {
      const cacheFilePath = this.getCacheFilePath(imagePath, options);
      console.log(`🔍 Loading OCR cache from: ${cacheFilePath}`);
      
      const data = await fs.readFile(cacheFilePath, 'utf8');
      const cachedResult = JSON.parse(data);
      
      // Add metadata about the cache
      cachedResult.cached = true;
      cachedResult.cacheTimestamp = cachedResult.cacheTimestamp || new Date().toISOString();
      cachedResult.cacheParameters = {
        extractionType: options.extractionType,
        mode: options.mode,
        modelId: options.modelId,
        cardSide: options.cardSide
      };
      
      console.log(`✅ Loaded cached OCR result with parameters:`, cachedResult.cacheParameters);
      return cachedResult;
    } catch (error) {
      console.log(`ℹ️ No cached OCR result found for parameters:`, options);
      return null;
    }
  }

  /**
   * Save OCR results to cache with specific parameters
   * @param {string} imagePath - The path to the image file
   * @param {Object} ocrResult - The OCR result to cache
   * @param {Object} options - OCR parameters used for this result
   * @returns {Promise<boolean>} - True if saved successfully
   */
  static async save(imagePath, ocrResult, options = {}) {
    try {
      const cacheFilePath = this.getCacheFilePath(imagePath, options);
      
      // Prepare cache data
      const cacheData = {
        ...ocrResult,
        cached: false, // Will be set to true when loaded from cache
        cacheTimestamp: new Date().toISOString(),
        imagePath: imagePath,
        imageBasename: path.basename(imagePath),
        cacheParameters: {
          extractionType: options.extractionType,
          mode: options.mode,
          modelId: options.modelId,
          cardSide: options.cardSide
        }
      };

      // Write cache file
      await fs.writeFile(cacheFilePath, JSON.stringify(cacheData, null, 2));
      
      console.log(`💾 OCR results cached to: ${cacheFilePath}`);
      console.log(`📝 Cache parameters:`, cacheData.cacheParameters);
      return true;
    } catch (error) {
      console.error('Error saving OCR result to cache:', error);
      return false;
    }
  }

  /**
   * Delete cached OCR results for an image with specific parameters
   * @param {string} imagePath - The path to the image file
   * @param {Object} options - OCR parameters (optional, deletes specific cache if provided)
   * @returns {Promise<boolean>} - True if deleted successfully
   */
  static async delete(imagePath, options = null) {
    try {
      if (options) {
        // Delete specific cache file for these parameters
        const cacheFilePath = this.getCacheFilePath(imagePath, options);
        await fs.unlink(cacheFilePath);
        console.log(`🗑️ Specific OCR cache deleted: ${cacheFilePath}`);
      } else {
        // Delete all cache files for this image (legacy support)
        const dir = path.dirname(imagePath);
        const baseName = path.basename(imagePath, path.extname(imagePath));
        const files = await fs.readdir(dir);
        
        const cacheFiles = files.filter(file => 
          file.startsWith(`${baseName}_ocr_results`) && file.endsWith('.json')
        );
        
        for (const file of cacheFiles) {
          await fs.unlink(path.join(dir, file));
        }
        
        console.log(`🗑️ All OCR caches deleted for: ${imagePath} (${cacheFiles.length} files)`);
      }
      return true;
    } catch (error) {
      if (error.code !== 'ENOENT') {
        console.error('Error deleting OCR cache:', error);
      }
      return false;
    }
  }

  /**
   * Get cache info for an image (existence, timestamp, etc.)
   * @param {string} imagePath - The path to the image file
   * @returns {Promise<Object>} - Cache information
   */
  static async getInfo(imagePath) {
    try {
      const cacheFilePath = this.getCacheFilePath(imagePath);
      const stats = await fs.stat(cacheFilePath);
      
      return {
        exists: true,
        path: cacheFilePath,
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime
      };
    } catch (error) {
      return {
        exists: false,
        path: this.getCacheFilePath(imagePath),
        size: 0,
        created: null,
        modified: null
      };
    }
  }

  /**
   * Check if cache is valid (not too old)
   * @param {string} imagePath - The path to the image file
   * @param {number} maxAgeMs - Maximum age in milliseconds (default: 7 days)
   * @returns {Promise<boolean>} - True if cache is valid
   */
  static async isValid(imagePath, maxAgeMs = 7 * 24 * 60 * 60 * 1000) {
    try {
      const info = await this.getInfo(imagePath);
      if (!info.exists) return false;
      
      const ageMs = Date.now() - info.modified.getTime();
      return ageMs <= maxAgeMs;
    } catch (error) {
      return false;
    }
  }

  /**
   * Clear all cache files in a directory
   * @param {string} directoryPath - The directory to clear cache from
   * @returns {Promise<number>} - Number of cache files deleted
   */
  static async clearDirectory(directoryPath) {
    try {
      const files = await fs.readdir(directoryPath);
      const cacheFiles = files.filter(file => 
        file.includes('_ocr_results') && file.endsWith('.json')
      );
      
      let deletedCount = 0;
      for (const file of cacheFiles) {
        try {
          await fs.unlink(path.join(directoryPath, file));
          deletedCount++;
        } catch (error) {
          console.error(`Error deleting cache file ${file}:`, error);
        }
      }
      
      console.log(`🧹 Cleared ${deletedCount} OCR cache files from ${directoryPath}`);
      return deletedCount;
    } catch (error) {
      console.error('Error clearing cache directory:', error);
      return 0;
    }
  }

  /**
   * Get all cache files for a specific image (all parameter variations)
   * @param {string} imagePath - The path to the image file
   * @returns {Promise<Array>} - Array of cache file info objects
   */
  static async getAllCacheFiles(imagePath) {
    try {
      const dir = path.dirname(imagePath);
      const baseName = path.basename(imagePath, path.extname(imagePath));
      
      // Check if directory exists before trying to read it
      try {
        await fs.access(dir);
      } catch (error) {
        console.log(`Directory does not exist or not accessible: ${dir}`);
        return [];
      }
      
      const files = await fs.readdir(dir);
      
      const cacheFiles = files.filter(file => 
        file.startsWith(`${baseName}_ocr_results`) && file.endsWith('.json')
      );
      
      const cacheInfos = [];
      for (const file of cacheFiles) {
        try {
          const filePath = path.join(dir, file);
          const data = JSON.parse(await fs.readFile(filePath, 'utf8'));
          cacheInfos.push({
            filename: file,
            path: filePath,
            parameters: data.cacheParameters || {},
            timestamp: data.cacheTimestamp,
            extractionType: data.cacheParameters?.extractionType || 'unknown',
            mode: data.cacheParameters?.mode || 'unknown'
          });
        } catch (error) {
          console.warn(`⚠️ Error reading cache file ${file}:`, error.message);
        }
      }
      
      return cacheInfos;
    } catch (error) {
      console.error('Error getting cache files:', error);
      return [];
    }
  }

  /**
   * Smart cache invalidation - clear auto-detect caches when specific mode is requested
   * @param {string} imagePath - The path to the image file
   * @param {Object} newOptions - The new OCR parameters being requested
   * @returns {Promise<number>} - Number of conflicting caches cleared
   */
  static async clearConflictingCaches(imagePath, newOptions) {
    try {
      const allCaches = await this.getAllCacheFiles(imagePath);
      let clearedCount = 0;
      
      for (const cache of allCaches) {
        let shouldClear = false;
        
        // Clear auto-detect cache when specific mode is requested
        if (cache.extractionType === 'auto_detect' && newOptions.extractionType !== 'auto_detect') {
          shouldClear = true;
          console.log(`🗑️ Clearing auto-detect cache (${cache.filename}) for specific mode: ${newOptions.extractionType}`);
        }
        
        // Clear specific mode cache when different specific mode is requested
        if (cache.extractionType !== 'auto_detect' && 
            newOptions.extractionType !== 'auto_detect' && 
            cache.extractionType !== newOptions.extractionType) {
          shouldClear = true;
          console.log(`🗑️ Clearing conflicting mode cache (${cache.filename}) for new mode: ${newOptions.extractionType}`);
        }
        
        // Clear cache when mode changes (us <-> australian)
        if (cache.mode !== newOptions.mode && newOptions.mode) {
          shouldClear = true;
          console.log(`🗑️ Clearing mode cache (${cache.filename}) for mode change: ${cache.mode} → ${newOptions.mode}`);
        }
        
        if (shouldClear) {
          try {
            await fs.unlink(cache.path);
            clearedCount++;
          } catch (error) {
            console.warn(`⚠️ Error clearing conflicting cache ${cache.filename}:`, error.message);
          }
        }
      }
      
      if (clearedCount > 0) {
        console.log(`🧹 Cleared ${clearedCount} conflicting cache files for smart cache invalidation`);
      }
      
      return clearedCount;
    } catch (error) {
      console.error('Error clearing conflicting caches:', error);
      return 0;
    }
  }
}

module.exports = OCRCache;