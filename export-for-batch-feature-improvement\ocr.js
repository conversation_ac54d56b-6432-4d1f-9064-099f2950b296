const express = require('express');
const multer = require('multer');
const OCRService = require('../services/ocr-service');
const LocalModelService = require('../services/local-model-service');
const CostTracker = require('../services/cost-tracker');
const OCRCache = require('../utils/ocr-cache');
const ModelValidator = require('../services/model-validator');
const router = express.Router();

// Initialize model validator
const modelValidator = new ModelValidator();
const initModelValidator = () => {
  const apiKey = process.env.OPENROUTER_API_KEY || 
                 process.env.OPENAI_API_KEY || 
                 'sk-or-v1-1786c4a6335f5f6c59e2c5d8c2c5b8a5f6c59e2c5d8c2c5b8a5f6c59e2ca47';
  
  if (apiKey && apiKey.startsWith('sk-or-v1-')) {
    modelValidator.initialize(apiKey);
  }
};

// Initialize validator
initModelValidator();

// Configure multer for image uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 1
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|webp|tiff/;
    const extname = allowedTypes.test(file.originalname.toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Initialize services
const ocrService = new OCRService();
const localModelService = new LocalModelService();
const costTracker = new CostTracker();

// Middleware to validate model before OCR processing
const validateModelMiddleware = async (req, res, next) => {
  try {
    const { modelId } = req.body;
    
    if (!modelId) {
      return next(); // No model specified, proceed with default
    }

    // Only validate OpenRouter models (they start with provider names)
    if (modelId.includes('/') && modelValidator.getStatus().initialized) {
      console.log(`🔍 OCR Routes: Validating model ${modelId}...`);
      
      const validation = await modelValidator.validateModel(modelId);
      if (!validation.valid) {
        console.warn(`⚠️ OCR Routes: Model ${modelId} is invalid: ${validation.error}`);
        
        // Try to find a suggested replacement
        const suggestions = await modelValidator.getSuggestedFixes([validation]);
        if (suggestions.length > 0 && suggestions[0].autoFixRecommendation) {
          const replacement = suggestions[0].autoFixRecommendation;
          console.log(`🔄 OCR Routes: Auto-fixing ${modelId} → ${replacement}`);
          req.body.modelId = replacement;
          req.body.originalModelId = modelId;
          req.body.autoFixed = true;
        } else {
          return res.status(400).json({
            success: false,
            error: `Model "${modelId}" is not available`,
            details: validation.error,
            suggestions: suggestions,
            fallbackModel: ocrService.selectOptimalModel()
          });
        }
      } else {
        console.log(`✅ OCR Routes: Model ${modelId} is valid`);
      }
    }
    
    next();
  } catch (error) {
    console.error('❌ OCR Routes: Model validation error:', error);
    // Don't block OCR processing on validation errors, just log and continue
    next();
  }
};

// Process image with OCR (file upload)
router.post('/analyze', upload.single('image'), validateModelMiddleware, async (req, res) => {
  try {
    const { modelId, extractionType = 'driver_license' } = req.body;
    
    if (!req.file) {
      return res.status(400).json({ error: 'No image file provided' });
    }

    const imageData = req.file.buffer;
    const options = {
      extractionType,
      modelOverride: modelId
    };

    const result = await ocrService.processImage(imageData, options);
    
    if (result.success) {
      // Record cost tracking
      await costTracker.recordTransaction({
        modelId: result.modelUsed,
        providerId: result.modelUsed.split('-')[0] || 'unknown',
        cost: result.cost || 0,
        tokens: 0, // Could calculate from result
        success: true,
        extractionType,
        imageSize: imageData.length
      });
    }

    res.json(result);
  } catch (error) {
    console.error('OCR analysis error:', error);
    res.status(500).json({ 
      error: 'Failed to analyze image',
      details: error.message 
    });
  }
});

// Process image with OCR (by file path)
router.post('/analyze-by-path', validateModelMiddleware, async (req, res) => {
  try {
    const { 
      imagePath, 
      modelId, 
      extractionType = 'driver_license', 
      mode = 'us', 
      cardSide = null,
      forceRefresh = false,
      customPrompt = null
    } = req.body;
    
    console.log('OCR analyze-by-path request:', {
      imagePath: imagePath ? 'provided' : 'missing',
      modelId,
      extractionType,
      mode,
      forceRefresh,
      hasCustomPrompt: !!customPrompt
    });
    
    if (!imagePath) {
      console.error('OCR analyze-by-path: No image path provided');
      return res.status(400).json({ error: 'No image path provided' });
    }

    // Decode the base64 image path
    let decodedPath;
    try {
      decodedPath = Buffer.from(imagePath, 'base64').toString();
      console.log('OCR analyze-by-path: Decoded path:', decodedPath);
    } catch (decodeError) {
      console.error('OCR analyze-by-path: Failed to decode base64 path:', decodeError.message);
      return res.status(400).json({ error: 'Invalid image path encoding' });
    }
    
    // Check if file exists
    const fs = require('fs').promises;
    try {
      await fs.access(decodedPath);
      console.log('OCR analyze-by-path: File exists, proceeding with OCR');
    } catch (error) {
      console.error('OCR analyze-by-path: Image file not found:', decodedPath, error.message);
      return res.status(404).json({ error: 'Image file not found', path: decodedPath });
    }

    // Smart cache management
    const cacheOptions = {
      extractionType,
      mode,
      modelId,
      cardSide,
      customPrompt: customPrompt ? 'custom' : null // Hash or identifier for custom prompts
    };
    
    // Clear conflicting caches when switching modes (unless force refresh already requested)
    if (!forceRefresh) {
      await OCRCache.clearConflictingCaches(decodedPath, cacheOptions);
    }
    
    // Check for cached results first (unless forced refresh)
    if (!forceRefresh) {
      let cachedResult = await OCRCache.load(decodedPath, cacheOptions);
      let matchedCacheOptions = cacheOptions;
      
      // If not found with original modelId, try with mapped model ID (e.g., gpt-4o-mini -> openai/gpt-4o-mini)
      if (!cachedResult && modelId) {
        let mappedModelId = null;
        
        // Apply same model mapping logic as in cache/check endpoint
        if (modelId === 'gpt-4o-mini') {
          mappedModelId = 'openai/gpt-4o-mini';
        } else if (modelId === 'gpt-4o') {
          mappedModelId = 'openai/gpt-4o';
        } else if (modelId.startsWith('openai/')) {
          // Try without prefix
          mappedModelId = modelId.replace('openai/', '');
        }
        
        if (mappedModelId) {
          const mappedCacheOptions = {
            extractionType,
            mode,
            modelId: mappedModelId,
            cardSide
          };
          
          const mappedCachedResult = await OCRCache.load(decodedPath, mappedCacheOptions);
          if (mappedCachedResult) {
            cachedResult = mappedCachedResult;
            matchedCacheOptions = mappedCacheOptions;
            console.log(`🔍 Cache found with mapped model ID: ${modelId} → ${mappedModelId}`);
          }
        }
      }
      
      if (cachedResult) {
        console.log(`💾 Returning parameter-specific cached OCR result for: ${decodedPath}`);
        console.log(`📝 Cache matched parameters:`, matchedCacheOptions);
        return res.json({
          success: true,
          result: cachedResult,
          cached: true,
          cacheTimestamp: cachedResult.cacheTimestamp,
          cacheParameters: cachedResult.cacheParameters,
          modelUsed: cachedResult.modelUsed || 'cached-model',
          message: 'Results loaded from parameter-specific cache'
        });
      } else {
        console.log(`🆆 No cached result found for parameters:`, cacheOptions);
      }
    } else {
      console.log(`♾️ Force refresh requested - clearing ALL caches and skipping cache lookup`);
      // When force refresh is requested, clear all caches for this image
      await OCRCache.delete(decodedPath); // This will delete all variations
    }

    // Read the image file
    const imageData = await fs.readFile(decodedPath);
    console.log('OCR analyze-by-path: Image data loaded, size:', imageData.length, 'bytes');
    
    const options = {
      extractionType,
      modelOverride: modelId,
      mode,
      cardSide,
      customPrompt
    };

    console.log('OCR analyze-by-path: Starting OCR processing with options:', options);
    const result = await ocrService.processImage(imageData, options);
    console.log('OCR analyze-by-path: OCR processing completed:', { 
      success: result.success, 
      modelUsed: result.modelUsed,
      hasResult: !!result.result,
      resultFields: result.result ? Object.keys(result.result) : []
    });
    
    if (result.success && result.result) {
      console.log('OCR analyze-by-path: Result data preview:', {
        firstName: result.result.firstName || '[EMPTY]',
        lastName: result.result.lastName || '[EMPTY]',
        licenseNumber: result.result.licenseNumber || '[EMPTY]',
        rawTextLength: result.result.rawText ? result.result.rawText.length : 0
      });
    }
    
    if (result.success) {
      // Record cost tracking
      await costTracker.recordTransaction({
        modelId: result.modelUsed,
        providerId: result.modelUsed.split('-')[0] || 'unknown',
        cost: result.cost || 0,
        tokens: 0,
        success: true,
        extractionType,
        imageSize: imageData.length
      });

      // Save OCR results using unified FileManager
      if (result.result) {
        try {
          // Save to cache with parameters for proper cache key generation
          const cacheOptions = {
            extractionType,
            mode,
            modelId: result.modelUsed || modelId, // Use actual model used
            cardSide,
            customPrompt: customPrompt ? 'custom' : null
          };
          
          console.log('🔍 Cache Debug: Attempting to save cache with options:', cacheOptions);
          console.log('🔍 Cache Debug: Decoded path:', decodedPath);
          console.log('🔍 Cache Debug: Result data preview:', {
            firstName: result.result.firstName,
            lastName: result.result.lastName,
            hasRawText: !!result.result.rawText,
            rawTextLength: result.result.rawText ? result.result.rawText.length : 0
          });
          
          const saveSuccess = await OCRCache.save(decodedPath, result.result, cacheOptions);
          console.log(`💾 Cache Debug: Save result: ${saveSuccess ? 'SUCCESS' : 'FAILED'}`);
          
          if (saveSuccess) {
            console.log(`✅ OCR results cached with parameters for: ${decodedPath}`);
            
            // Immediately verify cache was saved
            const verifyCache = await OCRCache.exists(decodedPath, cacheOptions);
            console.log(`🔍 Cache Debug: Immediate verification - exists: ${verifyCache}`);
          } else {
            console.error(`❌ Cache Debug: OCRCache.save() returned false for: ${decodedPath}`);
          }
        } catch (cacheError) {
          console.error('❌ Cache Debug: Exception during cache saving:', {
            message: cacheError.message,
            stack: cacheError.stack,
            decodedPath,
            cacheOptions: {
              extractionType,
              mode,
              modelId: result.modelUsed || modelId,
              cardSide,
              customPrompt: customPrompt ? 'custom' : null
            }
          });
        }

        // Save using new FileManager service with automatic JSON saving
        try {
          const fileManager = require('../services/file-manager');
          const saveResult = await fileManager.saveOCRResults(decodedPath, result.result, {
            autoSaveJSON: true,
            autoSaveTXT: true, // Save TXT alongside JSON by default to allow plain-text review
            includeReadySearch: true
          });
          
          if (saveResult.success) {
            result.savedFiles = saveResult.files;
            console.log(`OCR results saved via FileManager for: ${decodedPath}`);
          }
        } catch (saveError) {
          console.warn('Failed to save OCR results via FileManager:', saveError.message);
          
          // Fallback to legacy saving if available
          try {
            const legacySavedFiles = await ocrService.saveOCRResults?.(result.result, decodedPath, {
              formats: ['json'] // Only JSON for consistency
            });
            if (legacySavedFiles) {
              result.savedFiles = Object.entries(legacySavedFiles).map(([type, path]) => ({ type, path }));
              console.log('Fallback to legacy OCR saving successful');
            }
          } catch (legacyError) {
            console.warn('Legacy OCR saving also failed:', legacyError.message);
          }
        }
      }
    }

    const responseData = {
      ...result,
      cached: false,
      costInfo: result.success ? {
        modelUsed: result.modelUsed,
        estimatedCost: result.cost || 0,
        currentLimits: costTracker.getCurrentLimits()
      } : null
    };
    
    console.log('OCR analyze-by-path: Final response to frontend:', {
      success: responseData.success,
      hasResult: !!responseData.result,
      modelUsed: responseData.modelUsed,
      cost: responseData.cost,
      resultPreview: responseData.result ? {
        firstName: responseData.result.firstName || '[EMPTY]',
        lastName: responseData.result.lastName || '[EMPTY]'
      } : '[NO RESULT]'
    });
    
    res.json(responseData);
  } catch (error) {
    console.error('OCR analysis by path error:', error);
    res.status(500).json({ 
      error: 'Failed to analyze image',
      details: error.message 
    });
  }
});

// Get cost tracking report
router.get('/cost-report', async (req, res) => {
  try {
    const { period = '7d' } = req.query;
    
    await costTracker.initialize();
    const report = costTracker.getSpendingReport(period);
    const statistics = costTracker.getStatistics();
    
    res.json({
      success: true,
      data: {
        ...report,
        statistics,
        currentLimits: costTracker.getCurrentLimits()
      }
    });
  } catch (error) {
    console.error('Error generating cost report:', error);
    res.status(500).json({
      error: 'Failed to generate cost report',
      details: error.message
    });
  }
});

// Get cost statistics
router.get('/cost-stats', async (req, res) => {
  try {
    await costTracker.initialize();
    const statistics = costTracker.getStatistics();
    
    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    console.error('Error getting cost statistics:', error);
    res.status(500).json({
      error: 'Failed to get cost statistics',
      details: error.message
    });
  }
});

// Update cost limits
router.post('/cost-limits', async (req, res) => {
  try {
    const { daily, monthly, total } = req.body;
    
    await costTracker.initialize();
    const newLimits = await costTracker.updateLimits({ daily, monthly, total });
    
    res.json({
      success: true,
      data: newLimits
    });
  } catch (error) {
    console.error('Error updating cost limits:', error);
    res.status(500).json({
      error: 'Failed to update cost limits',
      details: error.message
    });
  }
});

// Batch process multiple images
router.post('/batch-analyze', upload.array('images', 10), async (req, res) => {
  try {
    const { modelId, extractionType = 'driver_license' } = req.body;
    
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: 'No image files provided' });
    }

    const results = [];
    const options = {
      extractionType,
      modelOverride: modelId
    };

    for (const file of req.files) {
      try {
        const result = await ocrService.processImage(file.buffer, options);
        results.push({
          filename: file.originalname,
          ...result
        });

        if (result.success) {
          await costTracker.recordTransaction({
            modelId: result.modelUsed,
            providerId: result.modelUsed.split('-')[0] || 'unknown',
            cost: result.cost || 0,
            tokens: 0,
            success: true,
            extractionType,
            imageSize: file.buffer.length
          });
        }
      } catch (error) {
        results.push({
          filename: file.originalname,
          success: false,
          error: error.message
        });
      }
    }

    res.json({ results });
  } catch (error) {
    console.error('Batch OCR analysis error:', error);
    res.status(500).json({ 
      error: 'Failed to analyze images',
      details: error.message 
    });
  }
});

// Get available models
router.get('/models', async (req, res) => {
  try {
    const apiModels = ocrService.getModels();
    const localModels = await localModelService.getAvailableModels();
    
    res.json({
      api: apiModels,
      local: localModels,
      total: apiModels.length + localModels.length
    });
  } catch (error) {
    console.error('Error fetching models:', error);
    res.status(500).json({ 
      error: 'Failed to fetch models',
      details: error.message 
    });
  }
});

// Get model providers
router.get('/providers', async (req, res) => {
  try {
    const providers = ocrService.getProviders();
    const localSystemInfo = localModelService.getSystemInfo();
    
    res.json({
      providers,
      localSystem: localSystemInfo
    });
  } catch (error) {
    console.error('Error fetching providers:', error);
    res.status(500).json({ 
      error: 'Failed to fetch providers',
      details: error.message 
    });
  }
});

// Configure provider
router.post('/providers/:providerId/configure', async (req, res) => {
  try {
    const { providerId } = req.params;
    const config = req.body;
    
    const provider = await ocrService.configureProvider(providerId, config);
    
    res.json({
      success: true,
      provider: {
        id: provider.id,
        name: provider.name,
        isConfigured: provider.isConfigured,
        models: provider.models.map(m => ({
          id: m.id,
          name: m.name,
          type: m.type,
          isEnabled: m.isEnabled
        }))
      }
    });
  } catch (error) {
    console.error('Error configuring provider:', error);
    res.status(500).json({ 
      error: 'Failed to configure provider',
      details: error.message 
    });
  }
});

// Install local model
router.post('/local/install/:modelId', async (req, res) => {
  try {
    const { modelId } = req.params;
    await localModelService.initialize();
    
    const result = await localModelService.installModel(modelId);
    
    res.json({
      success: true,
      modelId,
      installed: result
    });
  } catch (error) {
    console.error('Error installing model:', error);
    res.status(500).json({ 
      error: 'Failed to install model',
      details: error.message 
    });
  }
});

// Uninstall local model
router.delete('/local/uninstall/:modelId', async (req, res) => {
  try {
    const { modelId } = req.params;
    await localModelService.initialize();
    
    const result = await localModelService.uninstallModel(modelId);
    
    res.json({
      success: true,
      modelId,
      uninstalled: result
    });
  } catch (error) {
    console.error('Error uninstalling model:', error);
    res.status(500).json({ 
      error: 'Failed to uninstall model',
      details: error.message 
    });
  }
});

// Get local model status
router.get('/local/status', async (req, res) => {
  try {
    await localModelService.initialize();
    
    const systemInfo = localModelService.getSystemInfo();
    const installedModels = localModelService.getInstalledModels();
    const availableModels = localModelService.getAvailableModels();
    
    res.json({
      systemInfo,
      installedModels,
      availableModels
    });
  } catch (error) {
    console.error('Error getting local model status:', error);
    res.status(500).json({ 
      error: 'Failed to get local model status',
      details: error.message 
    });
  }
});

// Get cost statistics
router.get('/cost/stats', async (req, res) => {
  try {
    const stats = costTracker.getStatistics();
    const currentLimits = costTracker.getCurrentLimits();
    
    res.json({
      ...stats,
      currentLimits
    });
  } catch (error) {
    console.error('Error getting cost stats:', error);
    res.status(500).json({ 
      error: 'Failed to get cost statistics',
      details: error.message 
    });
  }
});

// Get spending report
router.get('/cost/report/:period?', async (req, res) => {
  try {
    const { period = '7d' } = req.params;
    const report = costTracker.getSpendingReport(period);
    
    res.json(report);
  } catch (error) {
    console.error('Error getting spending report:', error);
    res.status(500).json({ 
      error: 'Failed to get spending report',
      details: error.message 
    });
  }
});

// Update cost limits
router.put('/cost/limits', async (req, res) => {
  try {
    const limits = await costTracker.updateLimits(req.body);
    
    res.json({
      success: true,
      limits
    });
  } catch (error) {
    console.error('Error updating cost limits:', error);
    res.status(500).json({ 
      error: 'Failed to update cost limits',
      details: error.message 
    });
  }
});

// Update cost settings
router.put('/cost/settings', async (req, res) => {
  try {
    const settings = await costTracker.updateSettings(req.body);
    
    res.json({
      success: true,
      settings
    });
  } catch (error) {
    console.error('Error updating cost settings:', error);
    res.status(500).json({ 
      error: 'Failed to update cost settings',
      details: error.message 
    });
  }
});

// Export cost data
router.get('/cost/export/:format?', async (req, res) => {
  try {
    const { format = 'json' } = req.params;
    const data = costTracker.exportData(format);
    
    const filename = `cost-data-${new Date().toISOString().split('T')[0]}.${format}`;
    const contentType = format === 'csv' ? 'text/csv' : 'application/json';
    
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
    res.send(data);
  } catch (error) {
    console.error('Error exporting cost data:', error);
    res.status(500).json({ 
      error: 'Failed to export cost data',
      details: error.message 
    });
  }
});

// Check if request is allowed (rate limits and cost limits)
router.post('/check-limits', async (req, res) => {
  try {
    const { modelId, estimatedCost = 0 } = req.body;
    
    // Check cost limits
    const costCheck = costTracker.checkLimits(modelId, 'api', estimatedCost);
    
    // Check rate limits (if model info available)
    let rateCheck = { allowed: true };
    const model = ocrService.findModel(modelId);
    if (model && model.rateLimit) {
      rateCheck = costTracker.checkRateLimit(modelId, model.rateLimit);
    }
    
    res.json({
      allowed: costCheck.allowed && rateCheck.allowed,
      costCheck,
      rateCheck
    });
  } catch (error) {
    console.error('Error checking limits:', error);
    res.status(500).json({ 
      error: 'Failed to check limits',
      details: error.message 
    });
  }
});

// Get OCR processing statistics
router.get('/stats', async (req, res) => {
  try {
    const ocrStats = ocrService.getStats();
    const costStats = costTracker.getStatistics();
    
    res.json({
      ocr: ocrStats,
      cost: costStats
    });
  } catch (error) {
    console.error('Error getting OCR stats:', error);
    res.status(500).json({ 
      error: 'Failed to get OCR statistics',
      details: error.message 
    });
  }
});

// Save OCR results to files
router.post('/save-results', async (req, res) => {
  try {
    const { ocrResult, imagePath, formats = ['txt', 'json'] } = req.body;
    
    if (!ocrResult || !imagePath) {
      return res.status(400).json({ error: 'OCR result and image path are required' });
    }

    const savedFiles = await ocrService.saveOCRResults(ocrResult, imagePath, {
      formats
    });

    res.json({
      success: true,
      savedFiles
    });
  } catch (error) {
    console.error('Error saving OCR results:', error);
    res.status(500).json({ 
      error: 'Failed to save OCR results',
      details: error.message 
    });
  }
});

// Test model connection
router.post('/test/:modelId', async (req, res) => {
  try {
    const { modelId } = req.params;
    
    // Create a simple test image (1x1 pixel)
    const testImage = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
      0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
      0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);
    
    const startTime = Date.now();
    const result = await ocrService.processImage(testImage, {
      modelOverride: modelId,
      extractionType: 'auto_detect'
    });
    
    res.json({
      success: result.success,
      modelId,
      responseTime: Date.now() - startTime,
      error: result.error || null
    });
  } catch (error) {
    console.error('Error testing model:', error);
    res.status(500).json({ 
      error: 'Failed to test model',
      details: error.message 
    });
  }
});

// Check if cached OCR results exist for an image
router.post('/cache/check', async (req, res) => {
  try {
    const { imagePath, extractionType, mode, modelId, cardSide } = req.body;
    
    if (!imagePath) {
      return res.status(400).json({ error: 'No image path provided' });
    }

    const decodedPath = Buffer.from(imagePath, 'base64').toString();
    
    // If parameters provided, check for specific cache
    if (extractionType || mode || modelId) {
      const cacheOptions = { extractionType, mode, modelId, cardSide };
      let exists = await OCRCache.exists(decodedPath, cacheOptions);
      let matchedOptions = cacheOptions;
      
      // If not found with original modelId, try with mapped model ID (e.g., gpt-4o-mini -> openai/gpt-4o-mini)
      if (!exists && modelId) {
        // Try to map the model ID like the OCR service does
        let mappedModelId = null;
        
        // Common model mappings that OCR service uses
        if (modelId === 'gpt-4o-mini') {
          mappedModelId = 'openai/gpt-4o-mini';
        } else if (modelId === 'gpt-4o') {
          mappedModelId = 'openai/gpt-4o';
        } else if (modelId.startsWith('openai/')) {
          // Try without prefix
          mappedModelId = modelId.replace('openai/', '');
        }
        
        if (mappedModelId) {
          const mappedCacheOptions = { extractionType, mode, modelId: mappedModelId, cardSide };
          const mappedExists = await OCRCache.exists(decodedPath, mappedCacheOptions);
          
          if (mappedExists) {
            exists = true;
            matchedOptions = mappedCacheOptions;
            console.log(`🔍 Cache found with mapped model ID: ${modelId} → ${mappedModelId}`);
          }
        }
      }
      
      // If still not found, try mode fallback (us <-> australian)
      if (!exists && mode) {
        const alternateMode = mode === 'us' ? 'australian' : 'us';
        const modeFallbackOptions = { extractionType, mode: alternateMode, modelId, cardSide };
        const modeFallbackExists = await OCRCache.exists(decodedPath, modeFallbackOptions);
        
        if (modeFallbackExists) {
          exists = true;
          matchedOptions = modeFallbackOptions;
          console.log(`🔄 Cache found with alternate mode: ${mode} → ${alternateMode}`);
        } else if (modelId) {
          // Also try mode fallback with mapped model ID
          let mappedModelId = null;
          if (modelId === 'gpt-4o-mini') {
            mappedModelId = 'openai/gpt-4o-mini';
          } else if (modelId === 'gpt-4o') {
            mappedModelId = 'openai/gpt-4o';
          } else if (modelId.startsWith('openai/')) {
            mappedModelId = modelId.replace('openai/', '');
          }
          
          if (mappedModelId) {
            const mappedModeFallbackOptions = { extractionType, mode: alternateMode, modelId: mappedModelId, cardSide };
            const mappedModeFallbackExists = await OCRCache.exists(decodedPath, mappedModeFallbackOptions);
            
            if (mappedModeFallbackExists) {
              exists = true;
              matchedOptions = mappedModeFallbackOptions;
              console.log(`🔄 Cache found with alternate mode and mapped model: ${mode} → ${alternateMode}, ${modelId} → ${mappedModelId}`);
            }
          }
        }
      }
      
      const info = await OCRCache.getInfo(decodedPath);
      
      res.json({
        exists,
        info,
        path: decodedPath,
        parameters: matchedOptions, // Return the parameters that actually matched
        specificMatch: true
      });
    } else {
      // No parameters provided, check for any cached results
      const allCacheFiles = await OCRCache.getAllCacheFiles(decodedPath);
      const exists = allCacheFiles.length > 0;
      
      res.json({
        exists,
        path: decodedPath,
        availableVariations: allCacheFiles.map(cache => ({
          filename: cache.filename,
          parameters: cache.parameters,
          timestamp: cache.timestamp,
          extractionType: cache.extractionType,
          mode: cache.mode
        })),
        specificMatch: false
      });
    }
  } catch (error) {
    console.error('Error checking OCR cache:', error);
    res.status(500).json({ 
      error: 'Failed to check cache',
      details: error.message 
    });
  }
});

// Load cached OCR results for an image
router.post('/cache/load', async (req, res) => {
  try {
    const { imagePath, extractionType, mode, modelId, cardSide } = req.body;
    
    if (!imagePath) {
      return res.status(400).json({ error: 'No image path provided' });
    }

    const decodedPath = Buffer.from(imagePath, 'base64').toString();
    
    let cachedResult = null;
    
    // If parameters provided, try to load specific cache (with model-ID mapping fallback)
    if (extractionType || mode || modelId) {
      const cacheOptions = { extractionType, mode, modelId, cardSide };
      cachedResult = await OCRCache.load(decodedPath, cacheOptions);

      // If not found with original modelId, attempt common model-ID aliases like cache/check does
      if (!cachedResult && modelId) {
        let mappedModelId = null;

        if (modelId === 'gpt-4o-mini') {
          mappedModelId = 'openai/gpt-4o-mini';
        } else if (modelId === 'gpt-4o') {
          mappedModelId = 'openai/gpt-4o';
        } else if (modelId.startsWith('openai/')) {
          // Also try without prefix
          mappedModelId = modelId.replace('openai/', '');
        }

        if (mappedModelId) {
          const mappedOptions = { extractionType, mode, modelId: mappedModelId, cardSide };
          cachedResult = await OCRCache.load(decodedPath, mappedOptions);

          if (cachedResult) {
            res.json({
              success: true,
              result: cachedResult,
              cached: true,
              parameters: mappedOptions,
              message: 'Results loaded from mapped-model cache',
              fallback: true
            });
            return;
          }
        }
      }
      
      // If still not found, try mode fallback (us <-> australian)
      if (!cachedResult && mode) {
        const alternateMode = mode === 'us' ? 'australian' : 'us';
        const modeFallbackOptions = { extractionType, mode: alternateMode, modelId, cardSide };
        cachedResult = await OCRCache.load(decodedPath, modeFallbackOptions);
        
        if (cachedResult) {
          console.log(`🔄 Cache found with alternate mode: ${mode} → ${alternateMode}`);
          res.json({
            success: true,
            result: cachedResult,
            cached: true,
            parameters: modeFallbackOptions,
            message: `Results loaded from ${alternateMode} mode cache (requested ${mode})`,
            fallback: true,
            modeChanged: true,
            originalMode: mode,
            usedMode: alternateMode
          });
          return;
        }
        
        // Also try mode fallback with mapped model ID
        if (modelId) {
          let mappedModelId = null;
          if (modelId === 'gpt-4o-mini') {
            mappedModelId = 'openai/gpt-4o-mini';
          } else if (modelId === 'gpt-4o') {
            mappedModelId = 'openai/gpt-4o';
          } else if (modelId.startsWith('openai/')) {
            mappedModelId = modelId.replace('openai/', '');
          }
          
          if (mappedModelId) {
            const mappedModeFallbackOptions = { extractionType, mode: alternateMode, modelId: mappedModelId, cardSide };
            cachedResult = await OCRCache.load(decodedPath, mappedModeFallbackOptions);
            
            if (cachedResult) {
              console.log(`🔄 Cache found with alternate mode and mapped model: ${mode} → ${alternateMode}, ${modelId} → ${mappedModelId}`);
              res.json({
                success: true,
                result: cachedResult,
                cached: true,
                parameters: mappedModeFallbackOptions,
                message: `Results loaded from ${alternateMode} mode cache with mapped model (requested ${mode})`,
                fallback: true,
                modeChanged: true,
                modelMapped: true,
                originalMode: mode,
                usedMode: alternateMode,
                originalModelId: modelId,
                usedModelId: mappedModelId
              });
              return;
            }
          }
        }
      }

      if (cachedResult) {
        res.json({
          success: true,
          result: cachedResult,
          cached: true,
          parameters: cacheOptions,
          message: 'Results loaded from parameter-specific cache'
        });
        return;
      }
    }
    
    // Fallback: try to find any cached result for this image
    const allCacheFiles = await OCRCache.getAllCacheFiles(decodedPath);
    
    if (allCacheFiles.length > 0) {
      // Load the most recent cache file
      const mostRecent = allCacheFiles.sort((a, b) => 
        new Date(b.timestamp || 0).getTime() - new Date(a.timestamp || 0).getTime()
      )[0];
      
      try {
        const fs = require('fs').promises;
        const data = await fs.readFile(mostRecent.path, 'utf8');
        const parsedResult = JSON.parse(data);
        
        res.json({
          success: true,
          result: parsedResult,
          cached: true,
          parameters: mostRecent.parameters,
          message: 'Results loaded from most recent cache variation',
          fallback: true
        });
        return;
      } catch (parseError) {
        console.error('Error parsing cached file:', parseError);
      }
    }
    
    // No cached results found
    res.status(404).json({
      success: false,
      error: 'No cached results found'
    });
    
  } catch (error) {
    console.error('Error loading cached OCR results:', error);
    res.status(500).json({ 
      error: 'Failed to load cached results',
      details: error.message 
    });
  }
});

// Delete cached OCR results for an image
router.delete('/cache/delete', async (req, res) => {
  try {
    const { imagePath } = req.body;
    
    if (!imagePath) {
      return res.status(400).json({ error: 'No image path provided' });
    }

    const decodedPath = Buffer.from(imagePath, 'base64').toString();
    const deleted = await OCRCache.delete(decodedPath);
    
    res.json({
      success: deleted,
      message: deleted ? 'Cache deleted successfully' : 'Cache not found or could not be deleted'
    });
  } catch (error) {
    console.error('Error deleting OCR cache:', error);
    res.status(500).json({ 
      error: 'Failed to delete cache',
      details: error.message 
    });
  }
});

// Clear all cached OCR results in a directory
router.delete('/cache/clear-directory', async (req, res) => {
  try {
    const { directoryPath } = req.body;
    
    if (!directoryPath) {
      return res.status(400).json({ error: 'No directory path provided' });
    }

    const decodedPath = Buffer.from(directoryPath, 'base64').toString();
    const deletedCount = await OCRCache.clearDirectory(decodedPath);
    
    res.json({
      success: true,
      deletedCount,
      message: `Cleared ${deletedCount} cached OCR results`
    });
  } catch (error) {
    console.error('Error clearing directory cache:', error);
    res.status(500).json({ 
      error: 'Failed to clear directory cache',
      details: error.message 
    });
  }
});

// Get saved OCR data for an image
router.get('/saved-data/:imageId', async (req, res) => {
  try {
    const { imageId } = req.params;
    
    if (!imageId) {
      return res.status(400).json({ error: 'Image ID is required' });
    }

    // Decode the base64 image ID to get the file path
    let decodedPath;
    try {
      decodedPath = Buffer.from(imageId, 'base64').toString();
    } catch (decodeError) {
      return res.status(400).json({ error: 'Invalid image ID encoding' });
    }

    const fileManager = require('../services/file-manager');
    
    // Check for both JSON and TXT files
    const jsonPath = fileManager.generateResultFilename(decodedPath, 'json', 'ocr_results');
    const txtPath = fileManager.generateResultFilename(decodedPath, 'txt', 'ocr_results');
    
    const fs = require('fs').promises;
    let savedData = {
      hasJsonFile: false,
      hasTxtFile: false,
      jsonPath: null,
      txtPath: null,
      jsonContent: null,
      txtContent: null
    };

    // Check JSON file
    try {
      await fs.access(jsonPath);
      savedData.hasJsonFile = true;
      savedData.jsonPath = jsonPath;
      
      const jsonContent = await fs.readFile(jsonPath, 'utf8');
      savedData.jsonContent = JSON.parse(jsonContent);
    } catch (error) {
      // JSON file doesn't exist, that's okay
    }

    // Check TXT file
    try {
      await fs.access(txtPath);
      savedData.hasTxtFile = true;
      savedData.txtPath = txtPath;
      
      savedData.txtContent = await fs.readFile(txtPath, 'utf8');
    } catch (error) {
      // TXT file doesn't exist, that's okay
    }

    if (!savedData.hasJsonFile && !savedData.hasTxtFile) {
      return res.status(404).json({
        success: false,
        error: 'No saved OCR data found for this image'
      });
    }

    res.json({
      success: true,
      data: savedData
    });
  } catch (error) {
    console.error('Error retrieving saved OCR data:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve saved OCR data',
      details: error.message 
    });
  }
});

// Update saved OCR data for an image
router.put('/saved-data/:imageId', async (req, res) => {
  try {
    const { imageId } = req.params;
    const { txtContent } = req.body;
    
    if (!imageId) {
      return res.status(400).json({ error: 'Image ID is required' });
    }

    if (txtContent === undefined) {
      return res.status(400).json({ error: 'TXT content is required' });
    }

    // Decode the base64 image ID to get the file path
    let decodedPath;
    try {
      decodedPath = Buffer.from(imageId, 'base64').toString();
    } catch (decodeError) {
      return res.status(400).json({ error: 'Invalid image ID encoding' });
    }

    const fileManager = require('../services/file-manager');
    const txtPath = fileManager.generateResultFilename(decodedPath, 'txt', 'ocr_results');
    
    const fs = require('fs').promises;
    
    // Write the updated TXT content
    await fs.writeFile(txtPath, txtContent, 'utf8');
    
    res.json({
      success: true,
      message: 'Saved OCR data updated successfully',
      txtPath
    });
  } catch (error) {
    console.error('Error updating saved OCR data:', error);
    res.status(500).json({ 
      error: 'Failed to update saved OCR data',
      details: error.message 
    });
  }
});

// Test connection endpoint for frontend connection button
router.post('/test-connection', async (req, res) => {
  try {
    const { modelId } = req.body;
    
    if (!modelId) {
      return res.status(400).json({ 
        success: false, 
        error: 'Model ID is required' 
      });
    }
    
    // Create a simple test image (1x1 pixel PNG)
    const testImage = Buffer.from([
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
      0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
      0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
      0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
      0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
      0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);
    
    const startTime = Date.now();
    
    try {
      const result = await ocrService.processImage(testImage, {
        modelOverride: modelId,
        extractionType: 'auto_detect'
      });
      
      res.json({
        success: true,
        modelId,
        responseTime: Date.now() - startTime,
        message: 'Connection test successful'
      });
    } catch (testError) {
      res.status(200).json({
        success: false,
        modelId,
        responseTime: Date.now() - startTime,
        error: testError.message || 'Connection test failed'
      });
    }
  } catch (error) {
    console.error('Error testing connection:', error);
    res.status(500).json({ 
      success: false,
      error: 'Failed to test connection',
      details: error.message 
    });
  }
});

// Get actual OCR prompts used in the project
router.get('/prompts', async (req, res) => {
  try {
    // Get all prompt types and their actual content
    const prompts = [
      {
        id: 'us-driver-license',
        name: 'US Driver License',
        content: ocrService.buildUSPrompt(),
        description: 'Standard US driver license OCR prompt with name parsing and card side detection',
        category: 'Official',
        extractionType: 'driver_license',
        mode: 'us'
      },
      {
        id: 'australian-driver-license',
        name: 'Australian Driver License',
        content: ocrService.buildAustralianPrompt(),
        description: 'Australian driver license with dual number system and given names parsing',
        category: 'Official',
        extractionType: 'driver_license',
        mode: 'australian'
      },
      {
        id: 'us-driver-license-back',
        name: 'US Driver License (Back)',
        content: ocrService.buildUSPrompt(),
        description: 'Back side of US driver license focusing on restrictions and endorsements',
        category: 'Official',
        extractionType: 'driver_license',
        mode: 'us',
        cardSide: 'back'
      },
      {
        id: 'australian-driver-license-back',
        name: 'Australian Driver License (Back)',
        content: ocrService.buildAustralianPrompt('back'),
        description: 'Back side of Australian driver license focusing on address information',
        category: 'Official',
        extractionType: 'driver_license',
        mode: 'australian',
        cardSide: 'back'
      },
      {
        id: 'auto-detect',
        name: 'Auto-Detect Document Type',
        content: ocrService.buildAutoDetectPrompt(),
        description: 'Automatically detects document type and extracts appropriate fields',
        category: 'Official',
        extractionType: 'auto_detect',
        mode: 'auto-detect'
      },
      {
        id: 'passport',
        name: 'Passport',
        content: ocrService.buildPassportPrompt(),
        description: 'International passport document processing',
        category: 'Official',
        extractionType: 'passport',
        mode: 'passport'
      },
      {
        id: 'selfie',
        name: 'Selfie Description',
        content: ocrService.buildSelfiePrompt(),
        description: 'Person identification and description from selfie photos',
        category: 'Official',
        extractionType: 'selfie',
        mode: 'selfie'
      },
      {
        id: 'us-id-card',
        name: 'US ID Card',
        content: ocrService.buildUSIDCardPrompt(),
        description: 'US state-issued identification cards',
        category: 'Official',
        extractionType: 'us_id_card',
        mode: 'us'
      }
    ];

    res.json({
      success: true,
      prompts
    });
  } catch (error) {
    console.error('Error fetching OCR prompts:', error);
    res.status(500).json({
      error: 'Failed to fetch OCR prompts',
      details: error.message
    });
  }
});

module.exports = router;