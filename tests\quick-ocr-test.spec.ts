import { test, expect } from '@playwright/test';

// Quick OCR Testing check
test.describe('Quick OCR Testing Check', () => {
  test('should check OCR Testing page directly', async ({ page }) => {
    // Try to navigate directly to OCR Testing page
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Take a screenshot for debugging
    await page.screenshot({ path: 'ocr-testing-direct.png' });
    
    // Check if we can access the page
    const pageTitle = await page.title();
    console.log('Page title:', pageTitle);
    
    // Check for OCR Testing content
    const heading = page.locator('h1');
    const headingText = await heading.textContent();
    console.log('Page heading:', headingText);
    
    // Check for images
    const images = page.locator('img');
    const imageCount = await images.count();
    console.log(`Found ${imageCount} images`);
    
    // Check each image
    for (let i = 0; i < Math.min(imageCount, 5); i++) {
      const image = images.nth(i);
      const src = await image.getAttribute('src');
      const alt = await image.getAttribute('alt');
      console.log(`Image ${i}: src="${src}", alt="${alt}"`);
      
      // Check if image loads
      if (src) {
        try {
          const response = await page.request.get(src);
          console.log(`Image ${i} URL ${src}: ${response.status()}`);
        } catch (error) {
          console.log(`Image ${i} URL ${src}: ERROR - ${(error as Error).message}`);
        }
      }
    }
    
    // Check for "Select Test Images" section
    const selectImagesSection = page.locator('text=Select Test Images');
    const selectImagesVisible = await selectImagesSection.isVisible();
    console.log('Select Test Images section visible:', selectImagesVisible);
    
    // Check if images are actually displaying properly
    if (imageCount > 0) {
      for (let i = 0; i < Math.min(imageCount, 3); i++) {
        const image = images.nth(i);
        try {
          const naturalWidth = await image.evaluate((img: HTMLImageElement) => img.naturalWidth);
          const naturalHeight = await image.evaluate((img: HTMLImageElement) => img.naturalHeight);
          console.log(`Image ${i} dimensions: ${naturalWidth}x${naturalHeight}`);
          
          if (naturalWidth > 0 && naturalHeight > 0) {
            console.log(`✅ Image ${i} loaded successfully`);
          } else {
            console.log(`❌ Image ${i} failed to load`);
          }
        } catch (error) {
          console.log(`❌ Image ${i} evaluation error: ${(error as Error).message}`);
        }
      }
    }
  });
  
  test('should test main homepage and check for project creation', async ({ page }) => {
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot
    await page.screenshot({ path: 'homepage-check.png' });
    
    // Check the page content
    const bodyText = await page.locator('body').textContent();
    console.log('Homepage content preview:', bodyText?.substring(0, 200) + '...');
    
    // Check for project creation elements
    const createButton = page.getByRole('button', { name: /create.*project/i });
    const createButtonVisible = await createButton.isVisible();
    console.log('Create project button visible:', createButtonVisible);
    
    if (createButtonVisible) {
      const isDisabled = await createButton.isDisabled();
      console.log('Create project button disabled:', isDisabled);
      
      // Check for form inputs
      const nameInput = page.locator('input[name="name"], input[placeholder*="name"]');
      const pathInput = page.locator('input[name="path"], input[name="rootPath"], input[placeholder*="path"]');
      
      console.log('Name input visible:', await nameInput.isVisible());
      console.log('Path input visible:', await pathInput.isVisible());
      
      // Check if there are validation errors
      const errors = page.locator('.error, [class*="error"], .text-red, [class*="text-red"]');
      const errorCount = await errors.count();
      console.log('Found', errorCount, 'error elements');
      
      if (errorCount > 0) {
        for (let i = 0; i < Math.min(errorCount, 3); i++) {
          const error = errors.nth(i);
          const errorText = await error.textContent();
          console.log(`Error ${i}: ${errorText}`);
        }
      }
    }
  });
});