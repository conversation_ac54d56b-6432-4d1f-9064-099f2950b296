const express = require('express');
const path = require('path');
const fs = require('fs').promises;
const router = express.Router();

// Simple debug route to test image finding
router.post('/images-debug', async (req, res) => {
  try {
    const { folderPath, recursive = false } = req.body;
    
    if (!folderPath) {
      return res.status(400).json({ error: 'Folder path is required' });
    }

    console.log('Debug: Testing folder access for:', folderPath);

    // Test if we can access the folder directly
    try {
      const stats = await fs.stat(folderPath);
      if (!stats.isDirectory()) {
        return res.status(400).json({ error: 'Path is not a directory' });
      }
      console.log('Debug: Folder exists and is accessible');
    } catch (error) {
      console.log('Debug: Folder access error:', error);
      return res.status(400).json({ error: 'Path does not exist or is not accessible' });
    }

    // Try to read the folder contents
    try {
      const files = await fs.readdir(folderPath, { withFileTypes: true });
      console.log('Debug: Found', files.length, 'items in folder');
      
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.jfif'];
      const images = [];
      
      for (const file of files) {
        if (file.isFile()) {
          const ext = path.extname(file.name).toLowerCase();
          console.log('Debug: Checking file:', file.name, 'extension:', ext);
          
          if (imageExtensions.includes(ext)) {
            console.log('Debug: Found image:', file.name);
            const fullPath = path.join(folderPath, file.name);
            
            try {
              const stats = await fs.stat(fullPath);
              images.push({
                id: Buffer.from(fullPath).toString('base64'),
                filename: file.name,
                path: fullPath,
                relativePath: file.name,
                lastModified: stats.mtime,
                fileSize: stats.size,
                width: 0,
                height: 0,
                thumbnailUrl: `/api/thumbnails/${Buffer.from(fullPath).toString('base64')}.jpg`,
                previewUrl: `/api/previews/${Buffer.from(fullPath).toString('base64')}.jpg`
              });
            } catch (statError) {
              console.log('Debug: Error getting file stats for:', file.name, statError);
            }
          }
        }
      }
      
      console.log('Debug: Final image count:', images.length);
      
      return res.json({
        success: true,
        folderPath,
        imageCount: images.length,
        recursive,
        images,
        debug: {
          totalFiles: files.length,
          imageFiles: images.length
        }
      });
      
    } catch (readError) {
      console.log('Debug: Error reading folder:', readError);
      return res.status(500).json({ error: 'Error reading folder contents' });
    }

  } catch (error) {
    console.error("Debug API Error:", error);
    return res.status(500).json({
      success: false,
      error: "Debug API error",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

module.exports = router;