/**
 * SINGLE SOURCE OF TRUTH FOR ALL PORT CONFIGURATION
 * 
 * This file is the ONLY place where ports should be defined.
 * All other systems must import from here.
 */

// Default ports - ONLY change these values
const DEFAULT_PORTS = {
  frontend: 3976,
  backend: 3176,
  ngrok: 4663
};

// Environment overrides (for development flexibility)
const PORTS = {
  frontend: process.env.FRONTEND_PORT ? parseInt(process.env.FRONTEND_PORT) : DEFAULT_PORTS.frontend,
  backend: process.env.BACKEND_PORT ? parseInt(process.env.BACKEND_PORT) : DEFAULT_PORTS.backend,
  ngrok: process.env.NGROK_PORT ? parseInt(process.env.NGROK_PORT) : DEFAULT_PORTS.ngrok
};

// Export configuration
module.exports = {
  PORTS,
  DEFAULT_PORTS,
  
  // Convenience getters
  getFrontendPort: () => PORTS.frontend,
  getBackendPort: () => PORTS.backend,
  getNgrokPort: () => PORTS.ngrok,
  getBackendUrl: () => `http://127.0.0.1:${PORTS.backend}`,
  
  // Port availability checking
  isPortAvailable: async (port) => {
    const net = require('net');
    return new Promise((resolve) => {
      const server = net.createServer();
      server.listen(port, (err) => {
        if (err) {
          resolve(false);
        } else {
          server.close(() => resolve(true));
        }
      });
      server.on('error', () => resolve(false));
    });
  },
  
  // Generate environment string for spawned processes
  getEnvironmentString: () => {
    return `FRONTEND_PORT=${PORTS.frontend} BACKEND_PORT=${PORTS.backend} NGROK_PORT=${PORTS.ngrok}`;
  }
};

// Log current configuration (only in development)
if (process.env.NODE_ENV !== 'production' && !process.env.BUILD_TIME) {
  console.log(`🔧 Port Configuration Loaded:`, {
    frontend: PORTS.frontend,
    backend: PORTS.backend, 
    ngrok: PORTS.ngrok,
    backendUrl: `http://127.0.0.1:${PORTS.backend}`
  });
}