import { test, expect } from '@playwright/test';

test.describe('Debug Navigation', () => {
  test('should debug homepage content', async ({ page }) => {
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    
    // Take a screenshot for debugging
    await page.screenshot({ path: 'homepage-debug.png' });
    
    // Log all text content
    const bodyText = await page.locator('body').textContent();
    console.log('Page text content:', bodyText);
    
    // Check for OCR Testing link variations
    const ocrTestingLink = page.locator('text=OCR Testing');
    const ocrTestingButton = page.locator('button').filter({ hasText: 'OCR Testing' });
    const ocrTestingLink2 = page.locator('a').filter({ hasText: 'OCR Testing' });
    
    console.log('OCR Testing link visible:', await ocrTestingLink.isVisible());
    console.log('OCR Testing button visible:', await ocrTestingButton.isVisible());
    console.log('OCR Testing link2 visible:', await ocrTestingLink2.isVisible());
    
    // Check if we're on the project overview page
    const projectOverview = page.locator('text=Project Overview');
    console.log('Project Overview visible:', await projectOverview.isVisible());
    
    // Look for any navigation elements
    const navElements = page.locator('nav, .nav, [role="navigation"]');
    const navCount = await navElements.count();
    console.log('Navigation elements found:', navCount);
    
    // Look for OCR Testing in any form
    const allOcrText = page.locator('*').filter({ hasText: /OCR/i });
    const ocrCount = await allOcrText.count();
    console.log('Elements with OCR text:', ocrCount);
    
    for (let i = 0; i < Math.min(ocrCount, 3); i++) {
      const element = allOcrText.nth(i);
      const text = await element.textContent();
      console.log(`OCR element ${i}: "${text}"`);
    }
  });
});