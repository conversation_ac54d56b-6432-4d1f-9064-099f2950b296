#!/usr/bin/env python3
"""
DL Organizer Performance Monitor & Agent Launcher
Provides intelligent task routing and performance monitoring
"""

import os
import sys
import subprocess
import time
import json
from pathlib import Path

# Configuration
MAX_TOKENS = 5000
AGENT_TIMEOUT = 300  # 5 minutes max per agent

class AgentRouter:
    """Routes tasks to appropriate subagents based on content analysis"""
    
    def __init__(self):
        self.agents_dir = Path("C:/claude/dl-organizer/.claude/agents")
        self.available_agents = self._discover_agents()
        
    def _discover_agents(self):
        """Find available agents"""
        if not self.agents_dir.exists():
            return {}
            
        agents = {}
        for agent_file in self.agents_dir.glob("*.md"):
            agent_name = agent_file.stem
            agents[agent_name] = str(agent_file)
            
        return agents
    
    def suggest_agent(self, task_description):
        """Suggest the best agent for a task"""
        task_lower = task_description.lower()
        
        # Agent routing rules
        routing_rules = {
            'infrastructure-specialist': [
                'port', 'win32', 'launcher', 'startup', 'npm', 'node.js', 
                'bulletproof', 'error', 'process', 'windows'
            ],
            'ui-design-specialist': [
                'typescript', 'component', 'frontend', 'react', 'style', 
                'compile', 'tsx', 'jsx', 'css', 'tailwind'
            ],
            'data-flow-specialist': [
                'api', 'backend', 'database', 'routing', 'express', 
                'sqlite', 'query', 'endpoint'
            ],
            'ocr-ai-specialist': [
                'ocr', 'openrouter', 'model', 'ai', 'gpt', 'license', 
                'detection', 'confidence'
            ],
            'repository-manager': [
                'git', 'cleanup', 'documentation', 'readme', 'commit', 
                'archive', 'organization'
            ],
            'qa-testing-specialist': [
                'test', 'jest', 'playwright', 'coverage', 'unit', 'e2e', 
                'testing', 'spec'
            ]
        }
        
        # Score each agent
        agent_scores = {}
        for agent, keywords in routing_rules.items():
            if agent in self.available_agents:
                score = sum(1 for keyword in keywords if keyword in task_lower)
                if score > 0:
                    agent_scores[agent] = score
        
        # Return best match
        if agent_scores:
            best_agent = max(agent_scores, key=agent_scores.get)
            return best_agent, agent_scores[best_agent]
        
        return None, 0

def monitor_claude_code():
    """Monitor and optimize Claude Code performance"""
    
    router = AgentRouter()
    
    print("🚀 DL Organizer Performance Monitor")
    print("=" * 50)
    print(f"📊 Token Budget: {MAX_TOKENS:,} tokens")
    print(f"🤖 Available Agents: {len(router.available_agents)}")
    
    for agent in router.available_agents:
        print(f"   ✅ {agent}")
    
    print("=" * 50)
    
    # Check if Claude Code is running efficiently
    try:
        # Get current Claude Code processes
        result = subprocess.run(
            ['tasklist', '/FI', 'IMAGENAME eq node.exe', '/FO', 'CSV'],
            capture_output=True, text=True, check=True
        )
        
        node_processes = []
        for line in result.stdout.split('\n')[1:]:  # Skip header
            if line.strip() and 'node.exe' in line:
                parts = [p.strip('"') for p in line.split('","')]
                if len(parts) >= 5:
                    node_processes.append({
                        'name': parts[0],
                        'pid': parts[1],
                        'memory': parts[4]
                    })
        
        if node_processes:
            print(f"🔍 Found {len(node_processes)} Node.js processes")
            total_memory = 0
            for proc in node_processes:
                memory_kb = int(proc['memory'].replace(',', '').replace(' K', ''))
                total_memory += memory_kb
                if memory_kb > 500000:  # > 500MB
                    print(f"⚠️  High memory process: PID {proc['pid']} ({memory_kb:,} KB)")
            
            print(f"📈 Total Node.js Memory: {total_memory:,} KB")
            
            if total_memory > 2000000:  # > 2GB
                print("🚨 WARNING: High memory usage detected!")
                print("💡 Consider restarting Claude Code or using subagents")
        
    except subprocess.CalledProcessError:
        print("ℹ️  Could not check process memory usage")
    
    print("\n💡 OPTIMIZATION SUGGESTIONS:")
    print("1. Use subagents for specific tasks:")
    
    # Example task routing
    example_tasks = [
        "Fix TypeScript compilation errors in src/types/",
        "Add missing database indexes for performance",
        "Create Jest test for OCR service error handling",
        "Standardize error handling in backend/routes/",
        "Optimize batch processing concurrency"
    ]
    
    for task in example_tasks:
        agent, score = router.suggest_agent(task)
        if agent:
            print(f"   • '{task[:40]}...' → {agent}")
        else:
            print(f"   • '{task[:40]}...' → general task")
    
    print("\n2. Token-efficient commands:")
    print('   win-claude-code "infrastructure-specialist: Fix Win32 startup errors"')
    print('   win-claude-code "ui-design-specialist: Fix TypeScript compilation"')
    print('   win-claude-code "qa-testing-specialist: Create basic test coverage"')
    
    print("\n🎯 PERFORMANCE TIPS:")
    print("• Use specific agent commands instead of broad /task requests")
    print("• Break large tasks into smaller, focused agent requests")
    print("• Monitor token usage with enhanced startup info")
    print("• Restart Claude Code if memory usage > 2GB")
    
    return router

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # Route a specific task
        task = " ".join(sys.argv[1:])
        router = AgentRouter()
        agent, score = router.suggest_agent(task)
        
        if agent and score > 0:
            print(f"💡 Suggested agent: {agent} (confidence: {score})")
            print(f"Command: {agent}: {task}")
        else:
            print("ℹ️  No specific agent suggested - general task")
    else:
        # Run monitor
        monitor_claude_code()
