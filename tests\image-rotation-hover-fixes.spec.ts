import { test, expect } from '@playwright/test'
import { ensureTestProject, deleteTestProject, createSampleImages } from './test-utils'
import * as path from 'path'
import * as fs from 'fs/promises'

test.describe('Image Rotation and Hover Preview Fixes', () => {
  const projectName = 'Test Rotation Hover Project'
  const projectPath = 'C:\\test-rotation-hover'

  test.beforeAll(async () => {
    await deleteTestProject(projectPath)
    await ensureTestProject(projectName, projectPath)
    await createSampleImages(projectPath, 5)
  })

  test.afterAll(async () => {
    await deleteTestProject(projectPath)
  })

  test('should rotate images without disappearing', async ({ page }) => {
    await page.goto('http://localhost:3001')
    
    // Select the test project
    await page.getByText(projectName).click()
    
    // Wait for folder tree to load
    await page.waitForSelector('.folder-tree')
    
    // Click on root folder to load images
    await page.getByRole('button', { name: projectPath }).click()
    
    // Wait for images to load
    await page.waitForSelector('.image-card', { timeout: 10000 })
    
    // Get the first image card
    const firstImageCard = page.locator('.image-card').first()
    
    // Get initial thumbnail URL
    const initialThumbnail = await firstImageCard.locator('img').first().getAttribute('src')
    expect(initialThumbnail).toBeTruthy()
    
    // Click rotate button
    await firstImageCard.locator('button[title="Rotate Clockwise"]').click()
    
    // Wait for rotation to complete (processing indicator should appear and disappear)
    await page.waitForTimeout(500) // Give time for processing to start
    
    // Wait for processing to finish - image should still be visible
    await expect(firstImageCard.locator('img').first()).toBeVisible({ timeout: 10000 })
    
    // Get new thumbnail URL - should be different (with timestamp)
    const newThumbnail = await firstImageCard.locator('img').first().getAttribute('src')
    expect(newThumbnail).toBeTruthy()
    expect(newThumbnail).not.toBe(initialThumbnail)
    
    // Verify the image is still displayed
    await expect(firstImageCard).toBeVisible()
    await expect(firstImageCard.locator('img').first()).toBeVisible()
  })

  test('hover preview adapts to theme', async ({ page }) => {
    await page.goto('http://localhost:3001')
    
    // Select the test project
    await page.getByText(projectName).click()
    
    // Wait for folder tree to load
    await page.waitForSelector('.folder-tree')
    
    // Click on root folder to load images
    await page.getByRole('button', { name: projectPath }).click()
    
    // Wait for images to load
    await page.waitForSelector('.image-card', { timeout: 10000 })
    
    // Test in light theme first
    const themeToggle = page.locator('button[aria-label*="theme"]').first()
    
    // Ensure we're in light theme
    const htmlElement = page.locator('html')
    const currentTheme = await htmlElement.getAttribute('class')
    if (currentTheme?.includes('dark')) {
      await themeToggle.click()
      await page.waitForTimeout(300)
    }
    
    // Hover over first image for 2.5 seconds to trigger preview
    const firstImageCard = page.locator('.image-card').first()
    await firstImageCard.hover()
    await page.waitForTimeout(2500)
    
    // Check hover preview appears with correct background
    const hoverPreview = page.locator('.fixed').filter({ hasText: /\.(jpg|png|jpeg|gif)/i })
    await expect(hoverPreview).toBeVisible()
    
    // Get computed background color in light theme
    const lightBgColor = await hoverPreview.evaluate(el => {
      return window.getComputedStyle(el).backgroundColor
    })
    
    // Move mouse away to close preview
    await page.mouse.move(0, 0)
    await page.waitForTimeout(500)
    
    // Switch to dark theme
    await themeToggle.click()
    await page.waitForTimeout(300)
    
    // Hover again
    await firstImageCard.hover()
    await page.waitForTimeout(2500)
    
    // Check hover preview appears with different background
    const darkHoverPreview = page.locator('.fixed').filter({ hasText: /\.(jpg|png|jpeg|gif)/i })
    await expect(darkHoverPreview).toBeVisible()
    
    const darkBgColor = await darkHoverPreview.evaluate(el => {
      return window.getComputedStyle(el).backgroundColor
    })
    
    // Verify backgrounds are different between themes
    expect(lightBgColor).not.toBe(darkBgColor)
  })

  test('hover preview shows 150% zoom', async ({ page }) => {
    await page.goto('http://localhost:3001')
    
    // Select the test project
    await page.getByText(projectName).click()
    
    // Wait for folder tree to load
    await page.waitForSelector('.folder-tree')
    
    // Click on root folder to load images
    await page.getByRole('button', { name: projectPath }).click()
    
    // Wait for images to load
    await page.waitForSelector('.image-card', { timeout: 10000 })
    
    // Hover over first image
    const firstImageCard = page.locator('.image-card').first()
    await firstImageCard.hover()
    await page.waitForTimeout(2500)
    
    // Check hover preview appears
    const hoverPreview = page.locator('.fixed').filter({ hasText: /\.(jpg|png|jpeg|gif)/i })
    await expect(hoverPreview).toBeVisible()
    
    // Get the image element inside the preview
    const previewImage = hoverPreview.locator('img')
    await expect(previewImage).toBeVisible()
    
    // Verify the image has scale transform
    const transform = await previewImage.evaluate(el => {
      return window.getComputedStyle(el).transform
    })
    
    // Transform should include scale(1.5)
    expect(transform).toContain('1.5')
  })

  test('multiple rotations work correctly', async ({ page }) => {
    await page.goto('http://localhost:3001')
    
    // Select the test project
    await page.getByText(projectName).click()
    
    // Wait for folder tree to load
    await page.waitForSelector('.folder-tree')
    
    // Click on root folder to load images
    await page.getByRole('button', { name: projectPath }).click()
    
    // Wait for images to load
    await page.waitForSelector('.image-card', { timeout: 10000 })
    
    const firstImageCard = page.locator('.image-card').first()
    
    // Perform multiple rotations
    for (let i = 0; i < 3; i++) {
      // Click rotate button
      await firstImageCard.locator('button[title="Rotate Clockwise"]').click()
      
      // Wait for rotation to complete
      await page.waitForTimeout(1000)
      
      // Verify image is still visible
      await expect(firstImageCard.locator('img').first()).toBeVisible()
    }
    
    // Try counter-clockwise rotation
    await firstImageCard.locator('button[title="Rotate Counter-Clockwise"]').click()
    await page.waitForTimeout(1000)
    await expect(firstImageCard.locator('img').first()).toBeVisible()
    
    // Try 180 degree flip
    await firstImageCard.locator('button[title="Flip 180°"]').click()
    await page.waitForTimeout(1000)
    await expect(firstImageCard.locator('img').first()).toBeVisible()
  })
})