"use client";

import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Bot,
  Image as ImageIcon,
  CheckCircle2,
  Play,
  Pause,
  Square,
  FileText,
  Clock,
  DollarSign,
  Zap,
  Settings,
  RefreshCw,
  Search,
  Database,
  Calendar,
  Timer,
  ExternalLink,
  ChevronDown,
  ChevronUp,
  FolderOpen,
  AlertCircle,
} from "lucide-react";
import { ImageFile, OCRResult } from "@/types";
import OCRPanel from "./ocr-panel";
import Image from "next/image";
import { cn } from "@/lib/utils";

interface ProcessingModeTabsProps {
  images: ImageFile[];
  selectedImage: ImageFile | null;
  onImageSelect: (image: ImageFile) => void;
  onAnalyze: (imageId: string, modelId?: string) => void;
  ocrResult: OCRResult | null;
  isProcessing: boolean;
  onSave: (data: OCRResult) => void;
  onCancel: () => void;
  countryMode?: "us" | "australian";
  error?: string | null;
  successMessage?: string | null;
  className?: string;
  // New props for batch folder mode
  folderId?: string;
  folderPath?: string;
  // Multi-select props
  multiSelectIds?: Set<string>;
  batchSelectionMode?: boolean;
  onMultiSelectChange?: (ids: Set<string>) => void;
}

interface BatchProgress {
  total: number;
  completed: number;
  failed: number;
  currentImage?: string;
  currentStatus: "idle" | "processing" | "completed" | "paused";
  startTime?: Date;
  estimatedTime?: number;
  totalCost: number;
  readySearchProcessed?: number;
  readySearchFailed?: number;
}

interface ReadySearchOptions {
  enabled: boolean;
  useFullGivenNames: boolean;
  includeBirthYear: boolean;
  cachedResultsOnly: boolean;
}

export default function ProcessingModeTabs({
  images,
  selectedImage,
  onImageSelect,
  onAnalyze,
  ocrResult,
  isProcessing,
  onSave,
  onCancel,
  countryMode = "us",
  error,
  successMessage,
  className,
  folderId,
  folderPath,
  multiSelectIds = new Set(),
  batchSelectionMode = false,
  onMultiSelectChange,
}: ProcessingModeTabsProps) {
  // Use multiSelectIds from props if available, otherwise use local state
  const selectedImages = Array.from(multiSelectIds);
  const setSelectedImages = (newSelectedImages: string[]) => {
    if (onMultiSelectChange) {
      onMultiSelectChange(new Set(newSelectedImages));
    }
  };
  const [batchProgress, setBatchProgress] = useState<BatchProgress>({
    total: 0,
    completed: 0,
    failed: 0,
    currentStatus: "idle",
    totalCost: 0,
    readySearchProcessed: 0,
    readySearchFailed: 0,
  });
  const [selectedModel, setSelectedModel] = useState("gpt-4o-mini");
  const [readySearchOptions, setReadySearchOptions] =
    useState<ReadySearchOptions>({
      enabled: false,
      useFullGivenNames: false,
      includeBirthYear: true,
      cachedResultsOnly: false,
    });
  const [ocrResults, setOcrResults] = useState<OCRResult[]>([]);

  // New state for batch folder mode
  const [batchFolderImages, setBatchFolderImages] = useState<ImageFile[]>([]);
  const [batchProcessing, setBatchProcessing] = useState(false);
  const [currentBatchWithReadySearch, setCurrentBatchWithReadySearch] =
    useState(false);

  // Handler for batch folder mode selection changes
  const handleBatchFolderSelectionChange = (selectedImages: ImageFile[]) => {
    setBatchFolderImages(selectedImages);
  };

  // Process batch folder mode OCR
  const processBatchFolderOCR = async () => {
    if (batchFolderImages.length === 0) return;

    setBatchProcessing(true);
    setBatchProgress({
      total: batchFolderImages.length,
      completed: 0,
      failed: 0,
      currentStatus: "processing",
      totalCost: 0,
      readySearchProcessed: 0,
      readySearchFailed: 0,
      startTime: new Date(),
    });

    try {
      const response = await fetch("/api/batch-ocr/process-batch-folders", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          images: batchFolderImages,
          mode: "auto-detect",
          regionContext: countryMode,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setBatchProgress((prev) => ({
          ...prev,
          currentStatus: "completed",
          completed: result.stats.successfulExtractions,
          failed: result.stats.failedExtractions,
          currentImage: `✅ Processed ${result.stats.processedImages} images`,
        }));
      } else {
        throw new Error("Batch processing failed");
      }
    } catch (error) {
      setBatchProgress((prev) => ({
        ...prev,
        currentStatus: "completed",
        currentImage: "❌ Batch processing failed",
        failed: batchFolderImages.length,
      }));
    } finally {
      setBatchProcessing(false);
    }
  };

  const handleBatchImageSelect = (imageId: string, selected: boolean) => {
    const currentImages = Array.from(multiSelectIds);
    const newImages = selected 
      ? [...currentImages, imageId] 
      : currentImages.filter((id) => id !== imageId);
    setSelectedImages(newImages);
  };

  const handleSelectAll = () => {
    if (selectedImages.length === images.length) {
      setSelectedImages([]);
    } else {
      setSelectedImages(images.map((img) => img.id));
    }
  };

  const handleBatchProcess = async (withReadySearch: boolean = false) => {
    if (selectedImages.length === 0) return;

    console.log('🚀 Starting batch processing:', {
      imageCount: selectedImages.length,
      withReadySearch,
      countryMode,
      selectedModel
    });

    const startTime = new Date();
    
    // Track whether this batch includes ReadySearch
    setCurrentBatchWithReadySearch(withReadySearch);

    setBatchProgress({
      total: selectedImages.length,
      completed: 0,
      failed: 0,
      currentStatus: "processing",
      startTime,
      totalCost: 0,
      readySearchProcessed: 0,
      readySearchFailed: 0,
    });

    try {
      // Prepare images for sequential batch processing
      const batchImages = selectedImages.map(imageId => {
        const image = images.find(img => img.id === imageId);
        return {
          id: imageId,
          filename: image?.filename || 'Unknown'
        };
      });

      console.log('📦 Prepared batch images:', batchImages.length);

      // Start sequential batch processing
      const response = await fetch('/api/sequential-batch/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          images: batchImages,
          modelId: selectedModel,
          extractionType: countryMode === 'australian' ? 'aus_driver_license' : 'us_driver_license',
          mode: countryMode,
          includeReadySearch: withReadySearch,
          readySearchOptions: readySearchOptions,
          exportFormats: ['json', 'txt']
        }),
      });

      const result = await response.json();
      console.log('📡 API Response:', result);
      
      if (result.success) {
        const jobId = result.jobId;
        console.log(`✅ Job created: ${jobId}`);
        
        // Set up Server-Sent Events for real-time progress
        const eventSource = new EventSource(`/api/sequential-batch/stream/${jobId}`);
        
        eventSource.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            console.log('📡 SSE Update:', data.type, data.job?.progress);
            
            switch (data.type) {
              case 'progress':
                setBatchProgress((prev) => ({
                  ...prev,
                  currentImage: data.job.progress.currentImage || 'Processing...',
                  completed: data.job.progress.ocrCompleted || 0,
                  failed: data.job.progress.ocrFailed || 0,
                  readySearchProcessed: data.job.progress.readySearchCompleted || 0,
                  readySearchFailed: data.job.progress.readySearchFailed || 0,
                  estimatedTime: Math.ceil((selectedImages.length - (data.job.progress.ocrCompleted || 0)) * 8), // 8s per image estimate (adjusted for ReadySearch)
                }));
                break;
                
              case 'completed':
                console.log('🎉 Batch processing completed successfully');
                setBatchProgress((prev) => ({
                  ...prev,
                  currentStatus: "completed",
                  currentImage: withReadySearch
                    ? `🎉 OCR + ReadySearch completed! ${data.job.summary.ocrCompleted} images processed, ${data.job.summary.readySearchCompleted} ReadySearch matches found`
                    : `🎉 OCR Batch completed! ${data.job.summary.ocrCompleted} images processed, ${data.job.summary.filesSaved} files saved`,
                  completed: data.job.summary.ocrCompleted || 0,
                  failed: data.job.summary.ocrFailed || 0,
                  readySearchProcessed: data.job.summary.readySearchCompleted || 0,
                  readySearchFailed: data.job.summary.readySearchFailed || 0,
                  estimatedTime: 0,
                }));
                
                eventSource.close();
                
                // Clear selection after successful completion
                setTimeout(() => {
                  setSelectedImages([]);
                }, 3000);
                break;
                
              case 'failed':
                console.error('❌ Batch processing failed');
                setBatchProgress((prev) => ({
                  ...prev,
                  currentStatus: "completed",
                  currentImage: "❌ Sequential batch processing failed",
                  failed: selectedImages.length,
                }));
                
                eventSource.close();
                break;
                
              case 'error':
                console.error('❌ SSE Error:', data.error);
                setBatchProgress((prev) => ({
                  ...prev,
                  currentStatus: "completed",
                  currentImage: "❌ Connection error during processing",
                }));
                
                eventSource.close();
                break;
            }
          } catch (parseError) {
            console.error('❌ Error parsing SSE data:', parseError);
          }
        };

        eventSource.onerror = (error) => {
          console.error('❌ EventSource failed:', error);
          setBatchProgress((prev) => ({
            ...prev,
            currentStatus: "completed",
            currentImage: "❌ Lost connection to processing server",
          }));
          eventSource.close();
        };

        // Store event source for potential cleanup
        (window as any).currentEventSource = eventSource;
        
      } else {
        throw new Error(result.error || 'Failed to start sequential batch processing');
      }

    } catch (error) {
      console.error('❌ Sequential batch processing error:', error);
      setBatchProgress((prev) => ({
        ...prev,
        currentStatus: "completed",
        currentImage: `❌ Failed to start processing: ${error instanceof Error ? error.message : 'Unknown error'}`,
        failed: selectedImages.length,
      }));
    }
  };

  // Cleanup function for event sources
  const cleanupEventSource = () => {
    if ((window as any).currentEventSource) {
      (window as any).currentEventSource.close();
      (window as any).currentEventSource = null;
    }
  };

  // Clean up event source on component unmount
  React.useEffect(() => {
    return () => {
      cleanupEventSource();
    };
  }, []);

  const estimatedTime = selectedImages.length * 8; // ~8 seconds per image
  const estimatedCost = selectedImages.length * 0.02; // ~$0.02 per image

  return (
    <div className={cn("h-full", className)}>
      <Tabs defaultValue="single" className="h-full flex flex-col">
        <TabsList className="grid w-full grid-cols-2 mb-4">
          <TabsTrigger value="single" className="flex items-center gap-2">
            <ImageIcon className="h-4 w-4" />
            Single Image
          </TabsTrigger>
          <TabsTrigger value="batch" className="flex items-center gap-2">
            <Bot className="h-4 w-4" />
            Batch Processing
          </TabsTrigger>
        </TabsList>

        {/* Single Image Mode */}
        <TabsContent value="single" className="flex-1">
          <OCRPanel
            selectedImage={selectedImage}
            onAnalyze={onAnalyze}
            ocrResult={ocrResult}
            isProcessing={isProcessing}
            onSave={onSave}
            onCancel={onCancel}
            countryMode={countryMode}
            error={error}
            successMessage={successMessage}
            className="h-full"
          />
        </TabsContent>

        {/* Batch Processing Mode */}
        <TabsContent value="batch" className="flex-1 space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Batch OCR Processing</CardTitle>
                <Badge variant="outline">
                  {selectedImages.length} of {images.length} selected
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Processing Mode Options for Australian */}
              {countryMode === "australian" && (
                <Card className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-950/30 dark:to-cyan-950/30 border-blue-200 dark:border-blue-800">
                  <CardHeader className="pb-3">
                    <div className="flex items-center gap-2">
                      <div className="flex items-center gap-1">
                        <Bot className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                        <Search className="h-4 w-4 text-cyan-600 dark:text-cyan-400" />
                      </div>
                      <CardTitle className="text-sm font-semibold">
                        ReadySearch Options
                      </CardTitle>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="text-sm text-muted-foreground mb-3">
                      Configure ReadySearch settings for Australian driver
                      license database searches:
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="useFullNames"
                          checked={readySearchOptions.useFullGivenNames}
                          onCheckedChange={(checked) =>
                            setReadySearchOptions((prev) => ({
                              ...prev,
                              useFullGivenNames: !!checked,
                            }))
                          }
                        />
                        <label htmlFor="useFullNames" className="text-xs">
                          Use full given names (not just first name)
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="includeBirthYear"
                          checked={readySearchOptions.includeBirthYear}
                          onCheckedChange={(checked) =>
                            setReadySearchOptions((prev) => ({
                              ...prev,
                              includeBirthYear: !!checked,
                            }))
                          }
                        />
                        <label htmlFor="includeBirthYear" className="text-xs">
                          Include birth year in search
                        </label>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="cachedOnly"
                          checked={readySearchOptions.cachedResultsOnly}
                          onCheckedChange={(checked) =>
                            setReadySearchOptions((prev) => ({
                              ...prev,
                              cachedResultsOnly: !!checked,
                            }))
                          }
                        />
                        <label htmlFor="cachedOnly" className="text-xs">
                          <Database className="h-3 w-3 inline mr-1" />
                          Only process images with cached OCR results
                        </label>
                      </div>

                      <div className="text-xs text-muted-foreground bg-blue-50 dark:bg-blue-950/30 p-2 rounded">
                        <div className="font-medium mb-1">
                          ReadySearch will:
                        </div>
                        <div>• Search Australian driver license database</div>
                        <div>• Process only front-side license images</div>
                        <div>• Save results to image cache files</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Batch Controls */}
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSelectAll}
                  className="flex items-center gap-2"
                >
                  <CheckCircle2 className="h-4 w-4" />
                  {selectedImages.length === images.length
                    ? "Deselect All"
                    : "Select All"}
                </Button>

                {selectedImages.length > 0 && (
                  <>
                    {countryMode === "australian" ? (
                      // Australian mode: Show separate buttons for with/without ReadySearch
                      <div className="flex gap-2">
                        <Button
                          onClick={() => handleBatchProcess(false)}
                          disabled={
                            isProcessing ||
                            batchProgress.currentStatus === "processing"
                          }
                          variant="outline"
                          className="flex items-center gap-2"
                        >
                          <Play className="h-4 w-4" />
                          OCR Only ({selectedImages.length})
                        </Button>
                        <Button
                          onClick={() => handleBatchProcess(true)}
                          disabled={
                            isProcessing ||
                            batchProgress.currentStatus === "processing"
                          }
                          className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700"
                        >
                          <Play className="h-4 w-4" />
                          <Search className="h-3 w-3" />
                          OCR + ReadySearch ({selectedImages.length})
                        </Button>
                      </div>
                    ) : (
                      // Non-Australian mode: Show single OCR button
                      <Button
                        onClick={() => handleBatchProcess(false)}
                        disabled={
                          isProcessing ||
                          batchProgress.currentStatus === "processing"
                        }
                        className="flex items-center gap-2"
                      >
                        <Play className="h-4 w-4" />
                        Process {selectedImages.length} Images
                      </Button>
                    )}
                  </>
                )}
              </div>

              {/* Batch Statistics */}
              {selectedImages.length > 0 && (
                <div className="grid grid-cols-2 gap-4 p-3 bg-muted/30 rounded-lg">
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>Est. Time: {Math.ceil(estimatedTime / 60)}min</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <DollarSign className="h-4 w-4 text-muted-foreground" />
                    <span>Est. Cost: ${estimatedCost.toFixed(2)}</span>
                  </div>
                </div>
              )}

              {/* Progress Indicator */}
              {batchProgress.currentStatus === "processing" && (
                <div className="space-y-3 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">
                      Processing Images...
                    </span>
                    <span className="text-sm text-muted-foreground">
                      {batchProgress.completed} / {batchProgress.total}
                    </span>
                  </div>

                  <Progress
                    value={
                      (batchProgress.completed / batchProgress.total) * 100
                    }
                    className="h-3"
                  />

                  {batchProgress.currentImage && (
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-sm">
                        {!batchProgress.currentImage.includes("✅") &&
                          !batchProgress.currentImage.includes("❌") && (
                            <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600" />
                          )}
                        <span
                          className={cn(
                            "font-mono text-xs",
                            batchProgress.currentImage.includes("✅") &&
                              "text-green-600",
                            batchProgress.currentImage.includes("❌") &&
                              "text-red-600"
                          )}
                        >
                          {batchProgress.currentImage}
                        </span>
                      </div>

                      {batchProgress.estimatedTime &&
                        batchProgress.estimatedTime > 0 && (
                          <div className="flex items-center gap-1 text-xs text-muted-foreground">
                            <Clock className="h-3 w-3" />
                            <span>
                              ~{batchProgress.estimatedTime}s remaining
                            </span>
                          </div>
                        )}
                    </div>
                  )}

                  <div
                    className={cn(
                      "grid gap-4 text-xs",
                      currentBatchWithReadySearch
                        ? "grid-cols-5"
                        : "grid-cols-3"
                    )}
                  >
                    <div className="text-center">
                      <div className="font-medium text-green-600">
                        {batchProgress.completed}
                      </div>
                      <div className="text-muted-foreground">OCR Success</div>
                    </div>
                    <div className="text-center">
                      <div className="font-medium text-red-600">
                        {batchProgress.failed}
                      </div>
                      <div className="text-muted-foreground">OCR Failed</div>
                    </div>
                    {currentBatchWithReadySearch && (
                      <>
                        <div className="text-center">
                          <div className="font-medium text-purple-600">
                            {batchProgress.readySearchProcessed || 0}
                          </div>
                          <div className="text-muted-foreground">RS Found</div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium text-orange-600">
                            {batchProgress.readySearchFailed || 0}
                          </div>
                          <div className="text-muted-foreground">RS Failed</div>
                        </div>
                      </>
                    )}
                    <div className="text-center">
                      <div className="font-medium text-blue-600">
                        ${batchProgress.totalCost.toFixed(3)}
                      </div>
                      <div className="text-muted-foreground">Cost</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Completion Status */}
              {batchProgress.currentStatus === "completed" && (
                <CompletionReport 
                  batchProgress={batchProgress}
                  countryMode={countryMode}
                  currentBatchWithReadySearch={currentBatchWithReadySearch}
                  selectedImages={selectedImages}
                  images={images}
                />
              )}
            </CardContent>
          </Card>

          {/* Image Selection Grid */}
          <Card className="flex-1">
            <CardHeader className="pb-3">
              <CardTitle className="text-base">
                Select Images for Processing
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {images.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <ImageIcon className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>No images in selected folder</p>
                  <p className="text-sm">Select a folder to see images</p>
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-3 max-h-96 overflow-y-auto">
                  {images.map((image) => (
                    <div
                      key={image.id}
                      className={cn(
                        "relative border rounded-lg p-2 transition-all hover:shadow-md cursor-pointer",
                        selectedImages.includes(image.id)
                          ? "border-primary bg-primary/5"
                          : "border-border hover:border-primary/50"
                      )}
                      onClick={() =>
                        handleBatchImageSelect(
                          image.id,
                          !selectedImages.includes(image.id)
                        )
                      }
                    >
                      <div className="absolute top-1 left-1 z-10">
                        <Checkbox
                          checked={selectedImages.includes(image.id)}
                          onCheckedChange={() => {}} // Handled by parent onClick
                          className="bg-background border-border shadow-sm"
                        />
                      </div>

                      <div
                        className="aspect-square relative overflow-hidden rounded"
                        style={{ position: "relative" }}
                      >
                        <Image
                          src={image.thumbnailUrl}
                          alt={image.filename}
                          fill
                          className="object-cover"
                        />
                      </div>

                      <div className="mt-1">
                        <p className="text-xs font-medium truncate">
                          {image.filename}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {Math.round(image.fileSize / 1024)}KB
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Completion Report Component
interface CompletionReportProps {
  batchProgress: BatchProgress;
  countryMode: "us" | "australian";
  currentBatchWithReadySearch: boolean;
  selectedImages: string[];
  images: ImageFile[];
}

function CompletionReport({ 
  batchProgress, 
  countryMode, 
  currentBatchWithReadySearch, 
  selectedImages,
  images 
}: CompletionReportProps) {
  const [showDetails, setShowDetails] = useState(false);
  const [showJsonReport, setShowJsonReport] = useState(false);

  // Calculate processing duration
  const processingDuration = batchProgress.startTime 
    ? Math.round((new Date().getTime() - new Date(batchProgress.startTime).getTime()) / 1000)
    : 0;

  const completedTime = new Date().toLocaleString();
  const successRate = Math.round((batchProgress.completed / (batchProgress.completed + batchProgress.failed)) * 100);

  const handleViewResults = async () => {
    setShowDetails(!showDetails);
  };

  const handleDownloadReport = () => {
    const reportData = {
      timestamp: completedTime,
      processingMode: currentBatchWithReadySearch ? 'OCR + ReadySearch' : 'OCR Only',
      countryMode,
      duration: processingDuration,
      summary: {
        totalImages: selectedImages.length,
        ocrSuccessful: batchProgress.completed,
        ocrFailed: batchProgress.failed,
        readySearchMatches: batchProgress.readySearchProcessed || 0,
        readySearchFailed: batchProgress.readySearchFailed || 0,
        successRate: successRate
      },
      details: selectedImages.map((imageId, index) => {
        const image = images.find(img => img.id === imageId);
        const isSuccess = index < batchProgress.completed;
        
        return {
          imageId,
          filename: image?.filename || 'Unknown',
          status: isSuccess ? 'Success' : 'Failed',
          hasOcrResults: isSuccess,
          hasReadySearchResults: currentBatchWithReadySearch && isSuccess,
          estimatedPath: image ? Buffer.from(image.id, 'base64').toString('utf-8') : 'Unknown'
        };
      })
    };

    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `batch-processing-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const openImageFolder = (imageId: string) => {
    const image = images.find(img => img.id === imageId);
    if (image) {
      // Extract folder path from image path
      const imagePath = Buffer.from(image.id, 'base64').toString('utf-8');
      const folderPath = imagePath.substring(0, imagePath.lastIndexOf('\\'));
      
      // Open folder in file explorer (this would need to be implemented via backend)
      console.log('📂 Opening folder:', folderPath);
      
      // For now, copy path to clipboard
      navigator.clipboard.writeText(folderPath).then(() => {
        console.log('📋 Folder path copied to clipboard');
      }).catch(err => {
        console.error('Failed to copy folder path:', err);
      });
    }
  };

  return (
    <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 rounded-lg border border-green-200 dark:border-green-800">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2 text-green-700 dark:text-green-300">
          <CheckCircle2 className="h-5 w-5" />
          <span className="font-medium">
            {countryMode === "australian" && currentBatchWithReadySearch
              ? "OCR + ReadySearch Complete!"
              : "OCR Batch Processing Complete!"}
          </span>
        </div>
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <Calendar className="h-3 w-3" />
          <span>{completedTime}</span>
        </div>
      </div>

      {/* Processing Summary */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
        <div className="text-center p-2 bg-white/50 dark:bg-black/20 rounded">
          <div className="text-lg font-bold text-green-600">{batchProgress.completed}</div>
          <div className="text-xs text-muted-foreground">OCR Success</div>
        </div>
        <div className="text-center p-2 bg-white/50 dark:bg-black/20 rounded">
          <div className="text-lg font-bold text-red-600">{batchProgress.failed}</div>
          <div className="text-xs text-muted-foreground">OCR Failed</div>
        </div>
        {currentBatchWithReadySearch && (
          <>
            <div className="text-center p-2 bg-white/50 dark:bg-black/20 rounded">
              <div className="text-lg font-bold text-blue-600">{batchProgress.readySearchProcessed || 0}</div>
              <div className="text-xs text-muted-foreground">RS Matches</div>
            </div>
            <div className="text-center p-2 bg-white/50 dark:bg-black/20 rounded">
              <div className="text-lg font-bold text-orange-600">{batchProgress.readySearchFailed || 0}</div>
              <div className="text-xs text-muted-foreground">RS Failed</div>
            </div>
          </>
        )}
      </div>

      {/* Processing Details */}
      <div className="space-y-2 text-sm text-green-600 dark:text-green-400">
        <div className="flex items-center gap-2">
          <Timer className="h-3 w-3" />
          <span>Processing completed in {processingDuration}s</span>
        </div>
        <div className="flex items-center gap-2">
          <Database className="h-3 w-3" />
          <span>Results saved as .json and .txt files in each image folder</span>
        </div>
        <div className="flex items-center gap-2">
          <FileText className="h-3 w-3" />
          <span>
            {batchProgress.completed * 2} files generated 
            ({batchProgress.completed} JSON + {batchProgress.completed} TXT)
          </span>
        </div>
      </div>

      {/* Success Rate Progress Bar */}
      <div className="mb-4">
        <div className="flex items-center justify-between text-sm text-green-700 dark:text-green-300 mb-1">
          <span>Success Rate</span>
          <span className="font-bold">{successRate}%</span>
        </div>
        <div className="w-full bg-green-200 dark:bg-green-800 rounded-full h-2">
          <div 
            className="bg-green-600 dark:bg-green-400 h-2 rounded-full transition-all duration-300" 
            style={{ width: `${successRate}%` }}
          />
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap items-center gap-2 mt-4 pt-3 border-t border-green-200 dark:border-green-700">
        <Button
          variant="outline"
          size="sm"
          onClick={handleViewResults}
          className="flex items-center gap-2 text-green-700 border-green-300 hover:bg-green-100 dark:text-green-300 dark:border-green-600 dark:hover:bg-green-900/20"
        >
          {showDetails ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
          {showDetails ? 'Hide Details' : 'View Details'}
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => openImageFolder(selectedImages[0])}
          className="flex items-center gap-2 text-green-700 border-green-300 hover:bg-green-100 dark:text-green-300 dark:border-green-600 dark:hover:bg-green-900/20"
        >
          <FolderOpen className="h-3 w-3" />
          Copy Folder Path
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={handleDownloadReport}
          className="flex items-center gap-2 text-green-700 border-green-300 hover:bg-green-100 dark:text-green-300 dark:border-green-600 dark:hover:bg-green-900/20"
        >
          <FileText className="h-3 w-3" />
          Download Report
        </Button>
      </div>

      {/* Detailed Results */}
      {showDetails && (
        <div className="mt-4 p-3 bg-white/70 dark:bg-black/30 rounded border">
          <h4 className="font-medium text-sm mb-3 text-green-700 dark:text-green-300">
            Processing Results by Image
          </h4>
          <div className="space-y-2 max-h-64 overflow-y-auto">
            {selectedImages.map((imageId, index) => {
              const image = images.find(img => img.id === imageId);
              const isSuccess = index < batchProgress.completed;
              
              return (
                <div 
                  key={imageId} 
                  className="flex items-center justify-between p-2 bg-white/50 dark:bg-black/20 rounded text-xs"
                >
                  <div className="flex items-center gap-2">
                    {isSuccess ? (
                      <CheckCircle2 className="h-3 w-3 text-green-500" />
                    ) : (
                      <AlertCircle className="h-3 w-3 text-red-500" />
                    )}
                    <span className="font-medium">{image?.filename || 'Unknown'}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {isSuccess && (
                      <>
                        <Badge variant="secondary" className="text-xs px-1 py-0">
                          JSON
                        </Badge>
                        <Badge variant="secondary" className="text-xs px-1 py-0">
                          TXT
                        </Badge>
                      </>
                    )}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openImageFolder(imageId)}
                      className="h-6 w-6 p-0"
                      title="Open containing folder"
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
