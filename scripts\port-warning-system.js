#!/usr/bin/env node

/**
 * PORT WARNING SYSTEM - PREVENTION ENGINE
 * 
 * This system provides comprehensive warnings and notes to prevent
 * manual port changes and port hunting loops forever.
 * 
 * Features:
 * - Critical warnings for dangerous actions
 * - Educational notes about proper port management
 * - Prevention of manual configuration changes
 * - Integration with development workflow
 * - Real-time monitoring and alerts
 * - Historical tracking of violations
 * 
 * PREVENTS MANUAL PORT MISTAKES FOREVER!
 */

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const util = require('util');

const execAsync = util.promisify(exec);

class PortWarningSystem {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.dataDir = path.join(this.projectRoot, 'data');
        this.warningsFile = path.join(this.dataDir, 'port-warnings.json');
        this.violationsFile = path.join(this.dataDir, 'port-violations.json');
        this.notesFile = path.join(this.dataDir, 'port-education.md');
        
        // Ensure data directory exists
        if (!fs.existsSync(this.dataDir)) {
            fs.mkdirSync(this.dataDir, { recursive: true });
        }
        
        this.warnings = this.loadWarnings();
        this.violations = this.loadViolations();
        
        this.initializeWarnings();
        this.createEducationalNotes();
    }

    /**
     * Load warnings from disk
     */
    loadWarnings() {
        try {
            if (fs.existsSync(this.warningsFile)) {
                return JSON.parse(fs.readFileSync(this.warningsFile, 'utf8'));
            }
        } catch (error) {
            console.warn('⚠️ Failed to load warnings:', error.message);
        }
        
        return {
            critical: [],
            active: [],
            dismissed: [],
            preventive: []
        };
    }

    /**
     * Load violations history from disk
     */
    loadViolations() {
        try {
            if (fs.existsSync(this.violationsFile)) {
                return JSON.parse(fs.readFileSync(this.violationsFile, 'utf8'));
            }
        } catch (error) {
            console.warn('⚠️ Failed to load violations:', error.message);
        }
        
        return {
            history: [],
            patterns: {},
            statistics: {
                totalViolations: 0,
                manualChanges: 0,
                configCorruptions: 0,
                portHuntingLoops: 0
            }
        };
    }

    /**
     * Save warnings to disk
     */
    saveWarnings() {
        try {
            fs.writeFileSync(this.warningsFile, JSON.stringify(this.warnings, null, 2));
            fs.writeFileSync(this.violationsFile, JSON.stringify(this.violations, null, 2));
        } catch (error) {
            console.error('❌ Failed to save warnings:', error.message);
        }
    }

    /**
     * Initialize critical warnings
     */
    initializeWarnings() {
        // Clear old warnings
        this.warnings.critical = [];
        this.warnings.active = [];
        
        // Add critical warnings
        this.addCriticalWarning(
            '🚨 NEVER manually edit ports in package.json!',
            'Use the Port Memory System for all port changes',
            'manual-package-json-edit'
        );
        
        this.addCriticalWarning(
            '🚨 NEVER manually edit port-config.js directly!',
            'Use the bulletproof startup system to automatically resolve ports',
            'manual-port-config-edit'
        );
        
        this.addCriticalWarning(
            '🚨 NEVER use hardcoded ports in code!',
            'Always use environment variables or the port config system',
            'hardcoded-ports'
        );
        
        this.addCriticalWarning(
            '🚨 NEVER kill Node.js processes manually during port conflicts!',
            'Use the bulletproof startup system which handles all conflicts automatically',
            'manual-process-killing'
        );
        
        this.addCriticalWarning(
            '🚨 NEVER bypass the port memory system!',
            'The memory system prevents port hunting loops - always use it',
            'bypass-memory-system'
        );

        // Add preventive warnings
        this.addPreventiveWarning(
            '📚 REMEMBER: Use "npm run dev" to start servers',
            'This uses the bulletproof startup system that never fails',
            'proper-startup-command'
        );
        
        this.addPreventiveWarning(
            '📚 REMEMBER: Port conflicts are handled automatically',
            'The system remembers successful configurations and learns from failures',
            'automatic-conflict-resolution'
        );
        
        this.addPreventiveWarning(
            '📚 REMEMBER: All port changes are tracked and memorized',
            'The system builds a history of what works and what fails',
            'port-change-tracking'
        );

        this.saveWarnings();
    }

    /**
     * Add critical warning
     */
    addCriticalWarning(title, description, preventAction) {
        const warning = {
            id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
            type: 'critical',
            title,
            description,
            preventAction,
            timestamp: new Date().toISOString(),
            acknowledged: false,
            violations: 0
        };
        
        this.warnings.critical.push(warning);
        return warning.id;
    }

    /**
     * Add preventive warning
     */
    addPreventiveWarning(title, description, action) {
        const warning = {
            id: Date.now().toString() + Math.random().toString(36).substr(2, 5),
            type: 'preventive',
            title,
            description,
            action,
            timestamp: new Date().toISOString(),
            importance: 'high'
        };
        
        this.warnings.preventive.push(warning);
        return warning.id;
    }

    /**
     * Record a violation
     */
    recordViolation(type, description, context = {}) {
        const violation = {
            id: Date.now().toString(),
            type,
            description,
            context,
            timestamp: new Date().toISOString(),
            severity: this.calculateSeverity(type),
            prevention: this.getPreventionAdvice(type)
        };
        
        this.violations.history.push(violation);
        this.violations.statistics.totalViolations++;
        
        // Update type-specific statistics
        if (type === 'manual-change') {
            this.violations.statistics.manualChanges++;
        } else if (type === 'config-corruption') {
            this.violations.statistics.configCorruptions++;
        } else if (type === 'port-hunting-loop') {
            this.violations.statistics.portHuntingLoops++;
        }
        
        // Track violation patterns
        this.violations.patterns[type] = (this.violations.patterns[type] || 0) + 1;
        
        this.saveWarnings();
        
        console.log(`🚨 VIOLATION RECORDED: ${description}`);
        console.log(`💡 Prevention: ${violation.prevention}`);
        
        return violation.id;
    }

    /**
     * Calculate violation severity
     */
    calculateSeverity(type) {
        const severityMap = {
            'manual-change': 'high',
            'config-corruption': 'critical',
            'port-hunting-loop': 'critical',
            'hardcoded-port': 'medium',
            'bypass-system': 'high'
        };
        
        return severityMap[type] || 'medium';
    }

    /**
     * Get prevention advice for violation type
     */
    getPreventionAdvice(type) {
        const adviceMap = {
            'manual-change': 'Use the Port Memory System for all port changes. Run "npm run dev" to start servers.',
            'config-corruption': 'Never edit configuration files manually. Use the bulletproof startup system.',
            'port-hunting-loop': 'The bulletproof startup system prevents all port conflicts automatically.',
            'hardcoded-port': 'Use environment variables or the port config system for all ports.',
            'bypass-system': 'Always use the established port management system - it prevents all conflicts.'
        };
        
        return adviceMap[type] || 'Follow the established port management practices.';
    }

    /**
     * Monitor for configuration file changes
     */
    async monitorConfigChanges() {
        const configFiles = [
            'package.json',
            'port-config.js',
            '.env',
            '.env.local'
        ];
        
        for (const file of configFiles) {
            const filePath = path.join(this.projectRoot, file);
            
            if (fs.existsSync(filePath)) {
                // Check if file was recently modified
                const stats = fs.statSync(filePath);
                const modifiedRecently = Date.now() - stats.mtime.getTime() < 5 * 60 * 1000; // 5 minutes
                
                if (modifiedRecently) {
                    console.log(`⚠️ Configuration file modified: ${file}`);
                    this.recordViolation(
                        'manual-change',
                        `Configuration file ${file} was manually modified`,
                        { file, modificationTime: stats.mtime.toISOString() }
                    );
                }
            }
        }
    }

    /**
     * Check for port hunting patterns
     */
    async detectPortHuntingLoop() {
        try {
            // Check for multiple Node.js processes on different ports
            const { stdout } = await execAsync('netstat -ano | findstr ":30" | findstr "node"', { timeout: 3000 });
            const lines = stdout.trim().split('\n').filter(line => line.length > 0);
            
            if (lines.length > 3) {
                console.log('🚨 PORT HUNTING LOOP DETECTED!');
                this.recordViolation(
                    'port-hunting-loop',
                    `Multiple Node.js processes detected on different ports: ${lines.length} processes`,
                    { processCount: lines.length }
                );
                
                this.displayEmergencyWarning();
            }
        } catch (error) {
            // Ignore errors in detection
        }
    }

    /**
     * Display emergency warning
     */
    displayEmergencyWarning() {
        console.log('\n'.repeat(3));
        console.log('🚨'.repeat(50));
        console.log('🚨 EMERGENCY: PORT HUNTING LOOP DETECTED!');
        console.log('🚨'.repeat(50));
        console.log('');
        console.log('💀 This is exactly what we wanted to prevent!');
        console.log('');
        console.log('🛡️ IMMEDIATE SOLUTION:');
        console.log('   1. Close all terminals');
        console.log('   2. Run: npm run dev');
        console.log('   3. Let the bulletproof system handle everything');
        console.log('');
        console.log('🚫 DO NOT:');
        console.log('   - Manually change ports');
        console.log('   - Edit configuration files');
        console.log('   - Kill processes manually');
        console.log('   - Try different ports');
        console.log('');
        console.log('✅ The bulletproof startup system will resolve all conflicts automatically!');
        console.log('🚨'.repeat(50));
        console.log('\n'.repeat(3));
    }

    /**
     * Display all active warnings
     */
    displayAllWarnings() {
        console.log('\n🚨 CRITICAL WARNINGS - READ BEFORE PROCEEDING:');
        console.log('='.repeat(60));
        
        for (const warning of this.warnings.critical) {
            console.log(`\n${warning.title}`);
            console.log(`📝 ${warning.description}`);
            console.log(`🛡️ Action: ${warning.preventAction || 'Follow system guidelines'}`);
        }
        
        console.log('\n📚 REMEMBER - BEST PRACTICES:');
        console.log('='.repeat(60));
        
        for (const warning of this.warnings.preventive) {
            console.log(`\n${warning.title}`);
            console.log(`📝 ${warning.description}`);
        }
        
        console.log('\n🎯 SUMMARY:');
        console.log('• Use "npm run dev" to start servers (bulletproof startup)');
        console.log('• Never manually edit port configuration files');
        console.log('• Let the system handle all port conflicts automatically');
        console.log('• Report any issues instead of manually fixing');
        console.log('='.repeat(60));
        console.log('');
    }

    /**
     * Create educational notes file
     */
    createEducationalNotes() {
        const notes = `# Port Management Education - READ THIS!

## 🚨 CRITICAL: Port Hunting Loops Prevention

The DL Organizer had a recurring problem with port conflicts that wasted hours in every session. This has been **PERMANENTLY SOLVED** with the new bulletproof port management system.

## ❌ WHAT NOT TO DO (Will Break Everything!)

### 1. NEVER manually edit ports in these files:
- \`package.json\` - Scripts with hardcoded ports
- \`port-config.js\` - Central port configuration  
- \`.env.local\` - Environment variables
- \`data/port-config.json\` - Runtime configuration

### 2. NEVER manually kill Node.js processes during conflicts
- Don't use \`taskkill /F /IM node.exe\`
- Don't manually terminate processes in Task Manager
- Let the system handle process management

### 3. NEVER bypass the port memory system
- Don't start servers manually with custom ports
- Don't use \`next dev -p [custom-port]\` directly
- Don't try to "fix" port conflicts yourself

### 4. NEVER ignore the warnings
- All warnings are there for a reason
- Each warning prevents a specific failure mode
- Dismissing warnings leads to port hunting loops

## ✅ WHAT TO DO (Bulletproof Success!)

### 1. Use the bulletproof startup system:
\`\`\`bash
npm run dev
\`\`\`

This command:
- Uses persistent memory to remember successful configurations
- Automatically resolves ANY port conflict
- Has 10 fallback strategies that never fail
- Records success patterns for future use
- Prevents port hunting loops forever

### 2. If you need different ports:
- The system will automatically find available ports
- Successful configurations are remembered for next time
- No manual intervention needed - ever!

### 3. If something seems wrong:
- Run \`npm run dev\` again (it's designed to work every time)
- Check the port memory statistics: \`node scripts/port-memory-system.js stats\`
- View warnings: \`node scripts/port-warning-system.js warnings\`

## 🧠 How the Memory System Works

### Persistent Memory Features:
1. **Success Pattern Learning**: Remembers port configurations that worked
2. **Conflict History**: Tracks what caused problems in the past
3. **Reliability Scoring**: Identifies the most reliable port combinations
4. **Automatic Recovery**: Detects and resolves conflicts automatically
5. **Strategy Escalation**: 10 different resolution strategies, from gentle to nuclear

### Memory Files:
- \`data/port-memory.json\` - Session history and configuration memory
- \`data/port-success-patterns.json\` - Successful port combination patterns
- \`data/port-warnings.json\` - Active warnings and violation tracking

## 🛡️ Bulletproof Startup Strategies

The system uses these strategies in order:

1. **use-best-configuration** - Most reliable remembered configuration
2. **use-last-successful** - Last configuration that worked
3. **use-safe-defaults** - Standard ports with validation
4. **use-high-ports** - Higher port ranges to avoid conflicts
5. **use-random-ports** - Random ports in safe ranges
6. **use-emergency-ports** - Emergency backup ports
7. **use-any-available** - System-discovered available ports
8. **force-kill-conflicts** - Terminate conflicting processes
9. **use-localhost-alternatives** - Alternative localhost configurations
10. **final-fallback** - System-assigned ports (nuclear option)

## 📊 Statistics and Monitoring

View system statistics:
\`\`\`bash
node scripts/port-memory-system.js stats
\`\`\`

Monitor for violations:
\`\`\`bash
node scripts/port-warning-system.js monitor
\`\`\`

## 🎯 The Result

**NO MORE PORT HUNTING LOOPS!**

The system guarantees:
- ✅ Successful startup every time
- ✅ Automatic conflict resolution
- ✅ Memory of what works
- ✅ Prevention of manual mistakes
- ✅ No wasted time on port hunting

## 🚨 Emergency Protocol

If you somehow break the system:

1. **STOP** - Don't make it worse
2. **RESET** - Run \`node scripts/port-memory-system.js resolve\`
3. **RESTART** - Run \`npm run dev\`
4. **REPORT** - Document what happened so we can prevent it

## 💡 Remember

> "The best way to solve port conflicts is to prevent them entirely."

This system implements that philosophy. Trust it, use it, and never manually hunt for ports again!

---

*This education system is part of the bulletproof port management solution.*
*Last updated: ${new Date().toISOString()}*
`;

        fs.writeFileSync(this.notesFile, notes);
        console.log('📚 Educational notes created:', this.notesFile);
    }

    /**
     * Show violation statistics
     */
    showStatistics() {
        console.log('\n📊 PORT VIOLATION STATISTICS:');
        console.log('='.repeat(40));
        console.log(`Total violations: ${this.violations.statistics.totalViolations}`);
        console.log(`Manual changes: ${this.violations.statistics.manualChanges}`);
        console.log(`Config corruptions: ${this.violations.statistics.configCorruptions}`);
        console.log(`Port hunting loops: ${this.violations.statistics.portHuntingLoops}`);
        
        if (Object.keys(this.violations.patterns).length > 0) {
            console.log('\nViolation patterns:');
            for (const [type, count] of Object.entries(this.violations.patterns)) {
                console.log(`  ${type}: ${count} times`);
            }
        }
        
        console.log('='.repeat(40));
        console.log('');
    }

    /**
     * CLI command handler
     */
    async handleCommand(args) {
        const command = args[0] || 'warnings';
        
        switch (command) {
            case 'warnings':
                this.displayAllWarnings();
                break;
                
            case 'monitor':
                console.log('🔍 Monitoring for configuration violations...');
                await this.monitorConfigChanges();
                await this.detectPortHuntingLoop();
                break;
                
            case 'stats':
                this.showStatistics();
                break;
                
            case 'emergency':
                this.displayEmergencyWarning();
                break;
                
            case 'reset':
                console.log('🔄 Resetting warning system...');
                this.initializeWarnings();
                console.log('✅ Warning system reset');
                break;
                
            default:
                console.log(`
Port Warning System - Prevention Engine

Commands:
  warnings  - Display all active warnings (default)
  monitor   - Monitor for configuration violations  
  stats     - Show violation statistics
  emergency - Display emergency warning
  reset     - Reset warning system

This system prevents port hunting loops forever!
`);
                break;
        }
    }
}

module.exports = PortWarningSystem;

// CLI interface
if (require.main === module) {
    const warningSystem = new PortWarningSystem();
    const args = process.argv.slice(2);
    
    warningSystem.handleCommand(args)
        .then(() => {
            process.exit(0);
        })
        .catch(error => {
            console.error('❌ Warning system failed:', error.message);
            process.exit(1);
        });
}