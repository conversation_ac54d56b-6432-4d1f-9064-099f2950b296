@echo off
REM Test Qwen CLI with OpenRouter + Qwen3-Coder

cd /d "C:\claude\dl-organizer"

REM Set environment variables
set OPENAI_API_KEY=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
set OPENAI_BASE_URL=https://openrouter.ai/api/v1

echo ====================================
echo Testing Qwen CLI with OpenRouter
echo ====================================
echo API Key: %OPENAI_API_KEY:~0,20%...
echo Base URL: %OPENAI_BASE_URL%
echo Model: qwen/qwen3-coder:free
echo ====================================

REM Test free model first
echo.
echo [1/3] Testing qwen/qwen3-coder:free...
qwen --model "qwen/qwen3-coder:free" --prompt "Just say 'Hello from Qwen3-Coder Free!' and nothing else"

echo.
echo [2/3] Testing qwen/qwen3-coder (paid)...
qwen --model "qwen/qwen3-coder" --prompt "Just say 'Hello from Qwen3-Coder Paid!' and nothing else"

echo.
echo [3/3] Testing interactive mode...
echo Type 'exit' to quit
qwen --model "qwen/qwen3-coder:free"

pause
