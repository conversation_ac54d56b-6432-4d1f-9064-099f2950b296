@echo off
echo ===============================================================================
echo EMERGENCY NODE PROCESS CLEANUP
echo ===============================================================================
echo This will terminate all Node.js processes to free up memory
echo WARNING: This will stop ALL Node.js applications running on your system
echo ===============================================================================
echo.

echo Current Node.js processes:
tasklist /FI "IMAGENAME eq node.exe" /FO TABLE

echo.
echo Press any key to continue with cleanup, or Ctrl+C to cancel...
pause >nul

echo.
echo 🧹 Cleaning up Node.js processes...

:: Method 1: Kill by process name
taskkill /F /IM node.exe /T >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Node.js processes terminated
) else (
    echo ℹ️ No Node.js processes found or already terminated
)

:: Method 2: Kill by port (backup cleanup)
for %%P in (3030 3031 3003 3004 3268 3966 4040) do (
    for /f "tokens=5" %%A in ('netstat -ano ^| findstr :%%P') do (
        taskkill /F /PID %%A >nul 2>&1
    )
)

echo.
echo 🔍 Checking remaining Node.js processes...
tasklist /FI "IMAGENAME eq node.exe" /FO TABLE 2>nul | findstr node.exe
if %ERRORLEVEL% NEQ 0 (
    echo ✅ All Node.js processes have been cleaned up
) else (
    echo ⚠️ Some Node.js processes may still be running
)

echo.
echo 💾 Memory should now be freed up
echo You can now try running the launcher again
echo.
pause