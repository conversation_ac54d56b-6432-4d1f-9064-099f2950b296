import { NextRequest, NextResponse } from "next/server";
import fs from 'fs/promises';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const { folderPath, recursive = false } = await request.json();
    
    if (!folderPath) {
      return NextResponse.json({ error: 'Folder path is required' }, { status: 400 });
    }

    console.log('Debug: Testing folder access for:', folderPath);

    // Test if we can access the folder directly
    try {
      const stats = await fs.stat(folderPath);
      if (!stats.isDirectory()) {
        return NextResponse.json({ error: 'Path is not a directory' }, { status: 400 });
      }
      console.log('Debug: Folder exists and is accessible');
    } catch (error) {
      console.log('Debug: Folder access error:', error);
      return NextResponse.json({ error: 'Path does not exist or is not accessible' }, { status: 400 });
    }

    // Try to read the folder contents
    try {
      const files = await fs.readdir(folderPath, { withFileTypes: true });
      console.log('Debug: Found', files.length, 'items in folder');
      
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.jfif'];
      const images = [];
      
      for (const file of files) {
        if (file.isFile()) {
          const ext = path.extname(file.name).toLowerCase();
          console.log('Debug: Checking file:', file.name, 'extension:', ext);
          
          if (imageExtensions.includes(ext)) {
            console.log('Debug: Found image:', file.name);
            const fullPath = path.join(folderPath, file.name);
            
            try {
              const stats = await fs.stat(fullPath);
              images.push({
                id: Buffer.from(fullPath).toString('base64'),
                filename: file.name,
                path: fullPath,
                relativePath: file.name,
                lastModified: stats.mtime,
                fileSize: stats.size,
                width: 0,
                height: 0,
                thumbnailUrl: `/api/thumbnails/${Buffer.from(fullPath).toString('base64')}.jpg`,
                previewUrl: `/api/previews/${Buffer.from(fullPath).toString('base64')}.jpg`
              });
            } catch (statError) {
              console.log('Debug: Error getting file stats for:', file.name, statError);
            }
          }
        }
      }
      
      console.log('Debug: Final image count:', images.length);
      
      return NextResponse.json({
        success: true,
        folderPath,
        imageCount: images.length,
        recursive,
        images,
        debug: {
          totalFiles: files.length,
          imageFiles: images.length
        }
      });
      
    } catch (readError) {
      console.log('Debug: Error reading folder:', readError);
      return NextResponse.json({ error: 'Error reading folder contents' }, { status: 500 });
    }

  } catch (error) {
    console.error("Debug API Error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Debug API error",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}