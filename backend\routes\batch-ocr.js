const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');
const OCRService = require('../services/ocr-service');

// Exponential backoff helper for rate limit handling
async function withExponentialBackoff(fn, maxRetries = 3, baseDelay = 1000) {
  let attempt = 0;
  
  while (attempt < maxRetries) {
    try {
      const result = await fn();
      return result;
    } catch (error) {
      // Check if this is a rate limit error
      const isRateLimit = error.status === 429 || 
                         (error.response && error.response.status === 429) ||
                         error.message.includes('rate limit') ||
                         error.message.includes('429');
      
      if (!isRateLimit || attempt === maxRetries - 1) {
        throw error; // Not a rate limit error, or final attempt
      }
      
      // Calculate exponential backoff delay: baseDelay * 2^attempt + jitter
      const delay = baseDelay * Math.pow(2, attempt) + Math.random() * 1000;
      console.log(`Rate limit hit, retrying in ${Math.round(delay)}ms (attempt ${attempt + 1}/${maxRetries})`);
      
      await new Promise(resolve => setTimeout(resolve, delay));
      attempt++;
    }
  }
}

// Initialize OCR service
const ocrService = new OCRService();

// Batch OCR processing endpoint
router.post('/batch-process', async (req, res) => {
  try {
    const { folderIds, mode = 'auto-detect', exportFormats = ['json', 'txt'], includeSelfiDescription = false, txtFileOption = 'new' } = req.body;
    
    if (!folderIds || !Array.isArray(folderIds) || folderIds.length === 0) {
      return res.status(400).json({ error: 'folderIds array is required' });
    }

    console.log(`Starting batch OCR processing for ${folderIds.length} folders`);
    
    const results = [];
    const processingStats = {
      totalFolders: folderIds.length,
      processedFolders: 0,
      totalImages: 0,
      processedImages: 0,
      successfulExtractions: 0,
      failedExtractions: 0,
      exportedFiles: 0
    };

    for (const folderId of folderIds) {
      try {
        // Get folder path from database or storage
        const folderPath = await getFolderPath(folderId);
        if (!folderPath) {
          console.warn(`Folder not found: ${folderId}`);
          continue;
        }

        console.log(`Processing folder: ${folderPath}`);
        
        // Scan for images in the folder
        const images = await scanFolderForImages(folderPath);
        processingStats.totalImages += images.length;
        
        const folderResults = [];
        
        for (const imagePath of images) {
          try {
            processingStats.processedImages++;
            
            // Load and analyze the image
            const imageBuffer = await fs.readFile(imagePath);
            
            // Use auto-detection mode if specified, otherwise use the provided mode
            const extractionType = mode === 'auto-detect' ? 'auto_detect' : mode;
            
            const ocrResult = await ocrService.processImage(imageBuffer, {
              extractionType: extractionType,
              includeSelfiDescription: includeSelfiDescription
            });
            
            if (ocrResult && ocrResult.success) {
              // Extract document type from the result if using auto-detection
              const detectedType = ocrResult.result?.documentType || extractionType;
              
              folderResults.push({
                imagePath,
                documentType: detectedType,
                data: ocrResult.result || ocrResult.data,
                confidence: ocrResult.confidence || ocrResult.result?.confidence || 0.9
              });
              processingStats.successfulExtractions++;
            } else {
              processingStats.failedExtractions++;
              console.warn(`Failed to process image: ${imagePath}`, ocrResult?.error);
            }
          } catch (imageError) {
            processingStats.failedExtractions++;
            console.error(`Error processing image ${imagePath}:`, imageError);
          }
        }
        
        // Export results to the folder
        if (folderResults.length > 0) {
          await exportResultsToFolder(folderPath, folderResults, exportFormats, txtFileOption);
          processingStats.exportedFiles += exportFormats.length;
        }
        
        results.push({
          folderId,
          folderPath,
          results: folderResults,
          imageCount: images.length,
          successCount: folderResults.length
        });
        
        processingStats.processedFolders++;
        
      } catch (folderError) {
        console.error(`Error processing folder ${folderId}:`, folderError);
        results.push({
          folderId,
          error: folderError.message,
          imageCount: 0,
          successCount: 0
        });
      }
    }

    console.log('Batch OCR processing completed:', processingStats);
    
    res.json({
      success: true,
      results,
      stats: processingStats,
      message: `Processed ${processingStats.processedFolders} folders with ${processingStats.successfulExtractions} successful extractions`
    });
    
  } catch (error) {
    console.error('Batch OCR processing error:', error);
    res.status(500).json({ 
      error: 'Batch OCR processing failed',
      details: error.message 
    });
  }
});

// Helper function to get folder path from ID
async function getFolderPath(folderId) {
  try {
    // Folder IDs are base64 encoded paths
    const decodedPath = Buffer.from(folderId, 'base64').toString('utf8');
    
    // Verify the path exists
    const stats = await fs.stat(decodedPath);
    if (stats.isDirectory()) {
      return decodedPath;
    }
    
    return null;
  } catch (error) {
    console.warn(`Could not resolve folder path for ID ${folderId}:`, error.message);
    return null;
  }
}

// Helper function to scan folder for images
async function scanFolderForImages(folderPath) {
  try {
    const files = await fs.readdir(folderPath);
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'];
    
    const images = files
      .filter(file => imageExtensions.includes(path.extname(file).toLowerCase()))
      .map(file => path.join(folderPath, file));
    
    return images;
  } catch (error) {
    console.error(`Error scanning folder ${folderPath}:`, error);
    return [];
  }
}

// Helper function to auto-detect document type
async function autoDetectDocumentType(imagePath) {
  try {
    // For now, use simple heuristics based on image analysis
    // In a real implementation, this would use AI to analyze the image
    const filename = path.basename(imagePath).toLowerCase();
    
    if (filename.includes('selfie') || filename.includes('photo')) {
      return 'selfie';
    } else if (filename.includes('passport')) {
      return 'passport';
    } else if (filename.includes('license') || filename.includes('dl') || filename.includes('id')) {
      return 'driver_license';
    }
    
    // Default to driver's license for unknown types
    return 'driver_license';
  } catch (error) {
    console.error('Error auto-detecting document type:', error);
    return 'driver_license';
  }
}

// Helper function to find existing TXT files in the folder
async function findExistingTxtFile(folderPath) {
  try {
    const files = await fs.readdir(folderPath);
    const txtFiles = files.filter(file => 
      path.extname(file).toLowerCase() === '.txt' && 
      !file.startsWith('ocr-results-') // Exclude our own OCR result files
    );
    
    if (txtFiles.length > 0) {
      // Return the first non-OCR txt file found
      return path.join(folderPath, txtFiles[0]);
    }
    
    return null;
  } catch (error) {
    console.error('Error finding existing TXT files:', error);
    return null;
  }
}

// Helper function to export results to folder
async function exportResultsToFolder(folderPath, results, exportFormats, txtFileOption = 'new') {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    if (exportFormats.includes('json')) {
      const jsonPath = path.join(folderPath, `ocr-results-${timestamp}.json`);
      await fs.writeFile(jsonPath, JSON.stringify(results, null, 2));
      console.log(`Exported JSON to: ${jsonPath}`);
    }
    
    if (exportFormats.includes('txt')) {
      const txtContent = generateTextReport(results);
      
      if (txtFileOption === 'append') {
        // Look for existing TXT files in the folder
        const existingTxtFile = await findExistingTxtFile(folderPath);
        
        if (existingTxtFile) {
          // Append to existing file
          const separator = '\n\n' + '='.repeat(60) + '\n';
          const appendContent = separator + 'OCR Results Added: ' + new Date().toLocaleString() + '\n' + '='.repeat(60) + '\n\n' + txtContent;
          await fs.appendFile(existingTxtFile, appendContent);
          console.log(`Appended TXT results to: ${existingTxtFile}`);
        } else {
          // No existing file found, create new one
          const txtPath = path.join(folderPath, `ocr-results-${timestamp}.txt`);
          await fs.writeFile(txtPath, txtContent);
          console.log(`Created new TXT file: ${txtPath}`);
        }
      } else {
        // Create new file (default behavior)
        const txtPath = path.join(folderPath, `ocr-results-${timestamp}.txt`);
        await fs.writeFile(txtPath, txtContent);
        console.log(`Exported TXT to: ${txtPath}`);
      }
    }
    
  } catch (error) {
    console.error('Error exporting results:', error);
    throw error;
  }
}

// Helper function to generate text report
function generateTextReport(results) {
  const lines = [];
  lines.push('OCR Processing Results');
  lines.push('='.repeat(50));
  lines.push(`Generated: ${new Date().toLocaleString()}`);
  lines.push(`Total Images Processed: ${results.length}`);
  lines.push('');
  
  results.forEach((result, index) => {
    lines.push(`Image ${index + 1}: ${path.basename(result.imagePath)}`);
    lines.push(`Document Type: ${result.documentType}`);
    lines.push(`Confidence: ${Math.round(result.confidence * 100)}%`);
    lines.push('');
    
    if (result.documentType === 'selfie') {
      lines.push('Selfie Description:');
      lines.push(result.data.description || 'No description available');
      if (result.data.gender) lines.push(`Gender: ${result.data.gender}`);
      if (result.data.estimatedAge) lines.push(`Estimated Age: ${result.data.estimatedAge}`);
      if (result.data.details) {
        lines.push('Details:');
        if (result.data.details.hairColor) lines.push(`  Hair Color: ${result.data.details.hairColor}`);
        if (result.data.details.clothing) lines.push(`  Clothing: ${result.data.details.clothing}`);
        if (result.data.details.accessories) lines.push(`  Accessories: ${result.data.details.accessories}`);
        if (result.data.details.setting) lines.push(`  Setting: ${result.data.details.setting}`);
      }
    } else if (result.documentType === 'driver_license') {
      lines.push('Driver\'s License Information:');
      lines.push(`Name: ${result.data.firstName || ''} ${result.data.lastName || ''}`);
      if (result.data.dateOfBirth) lines.push(`Date of Birth: ${result.data.dateOfBirth}`);
      if (result.data.licenseNumber) lines.push(`License Number: ${result.data.licenseNumber}`);
      if (result.data.state) lines.push(`State: ${result.data.state}`);
      if (result.data.expirationDate) lines.push(`Expiration Date: ${result.data.expirationDate}`);
      if (result.data.address) lines.push(`Address: ${result.data.address}`);
    } else if (result.documentType === 'passport') {
      lines.push('Passport Information:');
      lines.push(`Name: ${result.data.firstName || ''} ${result.data.lastName || ''}`);
      if (result.data.dateOfBirth) lines.push(`Date of Birth: ${result.data.dateOfBirth}`);
      if (result.data.passportNumber) lines.push(`Passport Number: ${result.data.passportNumber}`);
      if (result.data.nationality) lines.push(`Nationality: ${result.data.nationality}`);
      if (result.data.country) lines.push(`Country: ${result.data.country}`);
      if (result.data.expirationDate) lines.push(`Expiration Date: ${result.data.expirationDate}`);
      if (result.data.placeOfBirth) lines.push(`Place of Birth: ${result.data.placeOfBirth}`);
    } else if (result.documentType === 'id_card') {
      lines.push('ID Card Information:');
      lines.push(`Name: ${result.data.firstName || ''} ${result.data.lastName || ''}`);
      if (result.data.dateOfBirth) lines.push(`Date of Birth: ${result.data.dateOfBirth}`);
      if (result.data.idNumber) lines.push(`ID Number: ${result.data.idNumber}`);
      if (result.data.address) lines.push(`Address: ${result.data.address}`);
      if (result.data.extractedText) lines.push(`Extracted Text: ${result.data.extractedText}`);
    } else {
      lines.push('Document Information:');
      lines.push(`Document Type: ${result.documentType}`);
      if (result.data.extractedText) {
        lines.push('Extracted Text:');
        lines.push(result.data.extractedText);
      }
    }
    
    lines.push('');
    lines.push('-'.repeat(30));
    lines.push('');
  });
  
  return lines.join('\n');
}

// Batch OCR processing for individual images with folder-mode support
router.post('/process-batch-folders', async (req, res) => {
  try {
    const { images, mode = 'auto-detect', regionContext = 'us' } = req.body;
    
    if (!images || !Array.isArray(images) || images.length === 0) {
      return res.status(400).json({ error: 'Images array is required' });
    }

    console.log(`Starting batch OCR processing for ${images.length} images in folder mode`);
    
    const CHUNK_SIZE = 10; // Process in chunks to respect rate limits
    const results = [];
    const processingStats = {
      totalImages: images.length,
      processedImages: 0,
      successfulExtractions: 0,
      failedExtractions: 0,
      savedFiles: 0
    };
    
    const failedPaths = [];
    
    // Process images in chunks
    for (let i = 0; i < images.length; i += CHUNK_SIZE) {
      const chunk = images.slice(i, i + CHUNK_SIZE);
      
      const chunkResults = await Promise.all(
        chunk.map(async (image) => {
          try {
            processingStats.processedImages++;
            
            // Read the image file
            const imageBuffer = await fs.readFile(image.path);
            
            // Determine extraction mode based on auto-detect and region
            let extractionType = mode;
            if (mode === 'auto-detect') {
              // Use region-aware auto-detection
              extractionType = regionContext === 'australian' ? 'aus_driver_license' : 'us_driver_license';
            }
            
            // Process the image with exponential backoff for rate limits
            const ocrResult = await withExponentialBackoff(async () => {
              return await ocrService.processImage(imageBuffer, {
                extractionType: extractionType,
                imagePath: image.path
              });
            });
            
            if (ocrResult && ocrResult.success) {
              processingStats.successfulExtractions++;
              
              // Extract the OCR data
              const ocrData = ocrResult.result || ocrResult.data;
              
              // Save both JSON and TXT files next to the source image
              const basePath = path.dirname(image.path);
              const basename = path.basename(image.path, path.extname(image.path));
              
              // Save JSON file
              const jsonPath = path.join(basePath, `${basename}_ocr.json`);
              await fs.writeFile(
                jsonPath,
                JSON.stringify(ocrData, null, 2),
                'utf8'
              );
              
              // Format and save TXT file
              const txtContent = formatOCRResultAsText(ocrData, extractionType);
              const txtPath = path.join(basePath, `${basename}_ocr.txt`);
              await fs.writeFile(
                txtPath,
                txtContent,
                'utf8'
              );
              
              processingStats.savedFiles += 2;
              
              return {
                success: true,
                imagePath: image.path,
                documentType: ocrData.documentType || extractionType,
                data: ocrData,
                savedFiles: [jsonPath, txtPath]
              };
            } else {
              processingStats.failedExtractions++;
              failedPaths.push({
                path: image.path,
                error: ocrResult?.error || 'OCR processing failed'
              });
              return {
                success: false,
                imagePath: image.path,
                error: ocrResult?.error || 'OCR processing failed'
              };
            }
          } catch (error) {
            processingStats.failedExtractions++;
            failedPaths.push({
              path: image.path,
              error: error.message
            });
            console.error(`Error processing image ${image.path}:`, error);
            return {
              success: false,
              imagePath: image.path,
              error: error.message
            };
          }
        })
      );
      
      results.push(...chunkResults);
      
      // Only add delay between chunks if processing many images to avoid overwhelming the API
      // The exponential backoff will handle actual rate limit responses
      if (i + CHUNK_SIZE < images.length && images.length > 50) {
        await new Promise(resolve => setTimeout(resolve, 200)); // Minimal delay for large batches
      }
    }
    
    res.json({
      success: true,
      results,
      stats: processingStats,
      failedPaths,
      message: `Processed ${processingStats.processedImages} images with ${processingStats.successfulExtractions} successful extractions`
    });
    
  } catch (error) {
    console.error('Batch folder OCR processing error:', error);
    res.status(500).json({
      error: 'Failed to process batch OCR',
      details: error.message
    });
  }
});

// Batch process individual images endpoint
router.post('/process-images', async (req, res) => {
  try {
    const { images, modelId = 'gpt-4o-mini', extractionType = 'auto_detect', mode = 'us', exportFormats = ['json', 'txt'] } = req.body;
    
    if (!images || !Array.isArray(images) || images.length === 0) {
      return res.status(400).json({ error: 'images array is required' });
    }

    console.log(`Starting batch OCR processing for ${images.length} images`);
    
    const results = [];
    const failedPaths = [];
    const processingStats = {
      totalImages: images.length,
      processedImages: 0,
      successfulExtractions: 0,
      failedExtractions: 0,
      savedFiles: 0
    };

    // Process images in chunks to avoid overwhelming the API
    const CHUNK_SIZE = 3;
    
    for (let i = 0; i < images.length; i += CHUNK_SIZE) {
      const chunk = images.slice(i, i + CHUNK_SIZE);
      
      const chunkResults = await Promise.all(
        chunk.map(async (image) => {
          try {
            processingStats.processedImages++;
            
            // Read the image file using the base64 encoded path
            const imagePath = Buffer.from(image.id, 'base64').toString('utf-8');
            
            // Read the image file first
            const imageBuffer = await fs.readFile(imagePath);
            
            // Use exponential backoff for rate limit handling
            const ocrResult = await withExponentialBackoff(async () => {
              return await ocrService.processImage(imageBuffer, {
                modelId,
                extractionType,
                mode,
                cardSide: null,
                forceRefresh: false
              });
            });
            
            if (ocrResult && ocrResult.success) {
              processingStats.successfulExtractions++;
              
              // Extract the OCR data
              const ocrData = ocrResult.result || ocrResult.data;
              
              // Save both JSON and TXT files next to the source image
              const basePath = path.dirname(imagePath);
              const basename = path.basename(imagePath, path.extname(imagePath));
              
              const savedFiles = [];
              
              if (exportFormats.includes('json')) {
                // Save JSON file
                const jsonPath = path.join(basePath, `${basename}_ocr_results.json`);
                await fs.writeFile(
                  jsonPath,
                  JSON.stringify({
                    ...ocrData,
                    imagePath: imagePath,
                    processedAt: new Date().toISOString(),
                    modelUsed: modelId,
                    extractionType,
                    mode
                  }, null, 2),
                  'utf8'
                );
                savedFiles.push(jsonPath);
                processingStats.savedFiles++;
              }
              
              if (exportFormats.includes('txt')) {
                // Format and save TXT file
                const txtContent = formatOCRResultAsText(ocrData, extractionType);
                const txtPath = path.join(basePath, `${basename}_ocr_results.txt`);
                await fs.writeFile(
                  txtPath,
                  txtContent,
                  'utf8'
                );
                savedFiles.push(txtPath);
                processingStats.savedFiles++;
              }
              
              return {
                success: true,
                imagePath: imagePath,
                imageId: image.id,
                filename: image.filename,
                documentType: ocrData.documentType || extractionType,
                data: ocrData,
                savedFiles
              };
            } else {
              processingStats.failedExtractions++;
              failedPaths.push({
                path: imagePath,
                filename: image.filename,
                error: ocrResult?.error || 'OCR processing failed'
              });
              return {
                success: false,
                imagePath: imagePath,
                imageId: image.id,
                filename: image.filename,
                error: ocrResult?.error || 'OCR processing failed'
              };
            }
          } catch (error) {
            processingStats.failedExtractions++;
            const imagePath = Buffer.from(image.id, 'base64').toString('utf-8');
            failedPaths.push({
              path: imagePath,
              filename: image.filename,
              error: error.message
            });
            console.error(`Error processing image ${image.filename}:`, error);
            return {
              success: false,
              imagePath: imagePath,
              imageId: image.id,
              filename: image.filename,
              error: error.message
            };
          }
        })
      );
      
      results.push(...chunkResults);
      
      // Add delay between chunks to avoid overwhelming the API
      if (i + CHUNK_SIZE < images.length) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
    
    res.json({
      success: true,
      results,
      stats: processingStats,
      failedPaths,
      successCount: processingStats.successfulExtractions,
      message: `Processed ${processingStats.processedImages} images with ${processingStats.successfulExtractions} successful extractions`
    });
    
  } catch (error) {
    console.error('Batch image OCR processing error:', error);
    res.status(500).json({
      error: 'Failed to process batch OCR for images',
      details: error.message
    });
  }
});

// Helper function to format OCR result as text
function formatOCRResultAsText(ocrData, documentType) {
  const lines = [];
  
  lines.push('=== OCR EXTRACTION RESULTS ===');
  lines.push(`Extraction Date: ${new Date().toISOString()}`);
  lines.push(`Document Type: ${documentType}`);
  lines.push('');
  
  if (documentType.includes('driver_license')) {
    lines.push('Driver License Information:');
    lines.push(`Name: ${ocrData.firstName || ''} ${ocrData.lastName || ''}`);
    if (ocrData.dateOfBirth) lines.push(`Date of Birth: ${ocrData.dateOfBirth}`);
    if (ocrData.licenseNumber) lines.push(`License Number: ${ocrData.licenseNumber}`);
    if (ocrData.expirationDate) lines.push(`Expiration Date: ${ocrData.expirationDate}`);
    if (ocrData.address) lines.push(`Address: ${ocrData.address}`);
    if (ocrData.state) lines.push(`State: ${ocrData.state}`);
  } else if (documentType === 'passport') {
    lines.push('Passport Information:');
    lines.push(`Name: ${ocrData.firstName || ''} ${ocrData.lastName || ''}`);
    if (ocrData.passportNumber) lines.push(`Passport Number: ${ocrData.passportNumber}`);
    if (ocrData.dateOfBirth) lines.push(`Date of Birth: ${ocrData.dateOfBirth}`);
    if (ocrData.expirationDate) lines.push(`Expiration Date: ${ocrData.expirationDate}`);
    if (ocrData.nationality) lines.push(`Nationality: ${ocrData.nationality}`);
  } else {
    lines.push('Extracted Information:');
    Object.entries(ocrData).forEach(([key, value]) => {
      if (value && key !== 'confidence' && key !== 'rawText') {
        lines.push(`${key}: ${value}`);
      }
    });
  }
  
  if (ocrData.confidence) {
    lines.push('');
    lines.push(`Confidence Score: ${(ocrData.confidence * 100).toFixed(1)}%`);
  }
  
  if (ocrData.rawText) {
    lines.push('');
    lines.push('=== RAW TEXT ===');
    lines.push(ocrData.rawText);
  }
  
  return lines.join('\n');
}

module.exports = router;// Force reload
