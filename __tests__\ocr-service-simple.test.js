// Focused tests for OCR service error handling and API fallback logic

// Mock dependencies before importing
jest.mock('openai', () => {
  return jest.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: jest.fn()
      }
    }
  }));
});

jest.mock('sharp', () => jest.fn(() => ({
  metadata: () => Promise.resolve({ width: 1024, height: 768, format: 'jpeg' }),
  resize: () => ({ 
    normalize: () => ({ 
      sharpen: () => ({ 
        gamma: () => ({ 
          toBuffer: () => Promise.resolve(Buffer.from('processed-image'))
        })
      })
    })
  })
})));

jest.mock('../backend/services/cost-tracker', () => jest.fn(() => ({
  initialize: () => Promise.resolve(),
  data: { transactions: [], statistics: { totalSpent: 0 } }
})));

jest.mock('../backend/services/file-manager');

const OCRService = require('../backend/services/ocr-service');
const OpenAI = require('openai');

describe('OCR Service Error Handling and Fallback Logic', () => {
  let ocrService;
  let mockOpenAIClient;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Silence console output for cleaner tests
    jest.spyOn(console, 'log').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
    
    // Create mock OpenAI client
    mockOpenAIClient = {
      chat: {
        completions: {
          create: jest.fn()
        }
      }
    };
    
    OpenAI.mockImplementation(() => mockOpenAIClient);
    
    // Create OCR service instance
    ocrService = new OCRService();
    ocrService.costTracker = {
      initialize: jest.fn().mockResolvedValue(),
      data: { transactions: [], statistics: { totalSpent: 0 } }
    };
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Error Handling Scenarios', () => {
    beforeEach(() => {
      // Configure OpenRouter provider for testing
      ocrService.configureProvider('openrouter', { apiKey: 'test-key' });
    });

    test('should handle API network errors with retries', async () => {
      const networkError = new Error('Network timeout');
      networkError.code = 'ECONNABORTED';
      
      mockOpenAIClient.chat.completions.create.mockRejectedValue(networkError);
      
      // Mock sleep to avoid actual delays in tests
      jest.spyOn(ocrService, 'sleep').mockResolvedValue();

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Network timeout');
      expect(result.retryCount).toBe(3); // Should have exhausted all retries
      expect(ocrService.sleep).toHaveBeenCalledTimes(2); // Should have retried with delays
    });

    test('should handle API authentication errors', async () => {
      const authError = new Error('Invalid API key provided');
      authError.status = 401;
      
      mockOpenAIClient.chat.completions.create.mockRejectedValue(authError);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid API key provided');
    });

    test('should handle API rate limiting', async () => {
      const rateLimitError = new Error('Rate limit exceeded');
      rateLimitError.status = 429;
      
      mockOpenAIClient.chat.completions.create.mockRejectedValue(rateLimitError);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Rate limit exceeded');
    });

    test('should handle malformed JSON responses gracefully', async () => {
      const mockResponse = {
        choices: [{ message: { content: '{"firstName": "John", invalid json' } }],
        usage: { prompt_tokens: 100, completion_tokens: 50, total_tokens: 150 }
      };
      
      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockResponse);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      // Should still return a result with error info
      expect(result.success).toBe(true);
      expect(result.result.parseError).toBeDefined();
      expect(result.result.firstName).toBe(''); // Fallback empty fields
    });
  });

  describe('Fallback Logic', () => {
    beforeEach(() => {
      ocrService.configureProvider('openrouter', { apiKey: 'test-key' });
    });

    test('should attempt fallback when primary model fails', async () => {
      // First call fails, second succeeds
      const primaryError = new Error('Primary model unavailable');
      mockOpenAIClient.chat.completions.create
        .mockRejectedValueOnce(primaryError)
        .mockResolvedValueOnce({
          choices: [{ message: { content: JSON.stringify({
            firstName: 'John',
            lastName: 'Doe',
            licenseNumber: '********',
            confidence: 0.9
          }) } }],
          usage: { prompt_tokens: 100, completion_tokens: 50, total_tokens: 150 }
        });

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(true);
      expect(result.result.firstName).toBe('John');
      expect(result.note).toMatch(/fallback/);
      expect(mockOpenAIClient.chat.completions.create).toHaveBeenCalledTimes(2);
    });

    test('should not use fallback when specific model is requested', async () => {
      const modelError = new Error('Requested model failed');
      mockOpenAIClient.chat.completions.create.mockRejectedValue(modelError);

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('Requested model failed');
      expect(result.note).toBeUndefined(); // No fallback attempted
    });

    test('should fail when all models are exhausted', async () => {
      const error = new Error('All models failed');
      mockOpenAIClient.chat.completions.create.mockRejectedValue(error);

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(false);
      expect(result.error).toContain('All models failed');
      expect(result.retryCount).toBe(3);
    });
  });

  describe('Model Configuration and Availability', () => {
    test('should return error for non-existent model', async () => {
      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'non-existent-model'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('not available');
    });

    test('should return error when provider is not configured', async () => {
      // Ensure provider is not configured
      const provider = ocrService.providers.get('openrouter');
      provider.isConfigured = false;

      const result = await ocrService.processImage(Buffer.from('test-image'), {
        modelOverride: 'google/gemini-flash-1.5'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('API configuration');
    });

    test('should validate provider configuration', () => {
      expect(() => {
        ocrService.configureProvider('invalid-provider', { apiKey: 'test' });
      }).toThrow('Provider invalid-provider not found');
    });
  });

  describe('Cost and Rate Limiting', () => {
    test('should block requests when cost limit is exceeded', async () => {
      // Mock cost limit exceeded
      jest.spyOn(ocrService, 'isCostLimitExceeded').mockReturnValue(true);

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(false);
      expect(result.error).toContain('cost limit exceeded');
    });

    test('should block requests when rate limited', async () => {
      // Mock rate limiting
      jest.spyOn(ocrService, 'isRateLimited').mockReturnValue(true);

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(false);
      expect(result.error).toContain('Rate limit exceeded');
    });
  });

  describe('Successful Processing', () => {
    beforeEach(() => {
      ocrService.configureProvider('openrouter', { apiKey: 'test-key' });
    });

    test('should successfully process valid image and response', async () => {
      const mockResponse = {
        choices: [{ message: { content: JSON.stringify({
          firstName: 'Jane',
          lastName: 'Smith',
          licenseNumber: '********',
          state: 'CA',
          confidence: 0.95
        }) } }],
        usage: { prompt_tokens: 150, completion_tokens: 75, total_tokens: 225 }
      };
      
      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockResponse);

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(true);
      expect(result.result.firstName).toBe('Jane');
      expect(result.result.lastName).toBe('Smith');
      expect(result.result.licenseNumber).toBe('********');
      expect(result.cost).toBeGreaterThan(0);
      expect(result.processingTime).toBeGreaterThan(0);
    });

    test('should handle partial JSON response with missing fields', async () => {
      const mockResponse = {
        choices: [{ message: { content: JSON.stringify({
          firstName: 'John',
          // Missing lastName and other fields
          confidence: 0.7
        }) } }],
        usage: { prompt_tokens: 100, completion_tokens: 50, total_tokens: 150 }
      };
      
      mockOpenAIClient.chat.completions.create.mockResolvedValue(mockResponse);

      const result = await ocrService.processImage(Buffer.from('test-image'));

      expect(result.success).toBe(true);
      expect(result.result.firstName).toBe('John');
      expect(result.result.lastName).toBe(''); // Should default to empty string
      expect(result.result.licenseNumber).toBe(''); // Should default to empty string
    });
  });
});