import { test, expect } from '@playwright/test';

// Test updated hover preview functionality with 200% larger images
test.describe('Updated Hover Preview Test', () => {
  test('should show 200% larger images in OCR Testing Playground', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Find the first image thumbnail
    const thumbnailImage = page.locator('img[width="48"]').first();
    await expect(thumbnailImage).toBeVisible();
    
    console.log('✅ Found thumbnail image (48x48)');
    
    // Get the parent group container
    const groupContainer = thumbnailImage.locator('..').locator('..');
    
    // Hover over the group container
    await groupContainer.hover();
    console.log('✅ Hovered over group container');
    
    // Wait for hover effect
    await page.waitForTimeout(1000);
    
    // Find the hover preview image
    const hoverPreview = page.locator('img[width="96"]');
    await expect(hoverPreview).toBeVisible();
    
    console.log('✅ Found hover preview image (96x96)');
    
    // Verify the hover preview dimensions
    const previewNaturalWidth = await hoverPreview.evaluate((img: HTMLImageElement) => img.naturalWidth);
    const previewNaturalHeight = await hoverPreview.evaluate((img: HTMLImageElement) => img.naturalHeight);
    
    console.log(`Preview image natural dimensions: ${previewNaturalWidth}x${previewNaturalHeight}`);
    
    // Verify the preview image is exactly 200% larger than thumbnail
    const previewWidth = await hoverPreview.getAttribute('width');
    const previewHeight = await hoverPreview.getAttribute('height');
    
    console.log(`Preview image display dimensions: ${previewWidth}x${previewHeight}`);
    
    expect(previewWidth).toBe('96');
    expect(previewHeight).toBe('96');
    
    // Check that the preview is 200% larger than the 48x48 thumbnail
    expect(Number(previewWidth)).toBe(48 * 2);
    expect(Number(previewHeight)).toBe(48 * 2);
    
    console.log('✅ Hover preview is correctly 200% larger than thumbnail');
    
    // Take screenshot with hover preview
    await page.screenshot({ path: 'ocr-testing-200percent-hover.png' });
    
    // Move mouse away to test hover out
    await page.mouse.move(0, 0);
    await page.waitForTimeout(1000);
    
    // Verify hover preview is hidden
    await expect(hoverPreview).toBeHidden();
    console.log('✅ Hover preview correctly hidden after hover out');
  });
  
  test('should show 200% larger images in main app', async ({ page }) => {
    // Navigate to main app
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    
    // Check if there's an existing project or create one
    const projectOverviewHeading = page.locator('text=DL Organizer Projects');
    if (await projectOverviewHeading.isVisible()) {
      console.log('On project overview page, need to create or select project');
      
      // Try to create a project first
      const createProjectButton = page.getByRole('button', { name: /create.*project/i });
      if (await createProjectButton.isVisible() && !await createProjectButton.isDisabled()) {
        console.log('Creating new project...');
        await createProjectButton.click();
        
        // Wait for project creation
        await page.waitForTimeout(2000);
        
        // Check if we're now in a project
        const ocrTestingLink = page.locator('text=OCR Testing');
        if (await ocrTestingLink.isVisible()) {
          console.log('✅ Project created, now in project view');
        }
      } else {
        console.log('❌ Cannot create project, skipping main app test');
        return;
      }
    }
    
    // Try to find folder tree and select a folder
    const folderTree = page.locator('[class*="folder"], [class*="tree"]');
    if (await folderTree.count() > 0) {
      console.log('Found folder tree, selecting first folder...');
      await folderTree.first().click();
      await page.waitForTimeout(2000);
    }
    
    // Look for image grid
    const imageGridCards = page.locator('[class*="card"]').filter({ hasText: /\.jpg|\.jpeg|\.png/ });
    const cardCount = await imageGridCards.count();
    
    console.log(`Found ${cardCount} image cards in main app`);
    
    if (cardCount > 0) {
      const firstCard = imageGridCards.first();
      
      // Get the grid size by checking the card style
      const cardStyle = await firstCard.getAttribute('style');
      console.log('Card style:', cardStyle);
      
      // Look for thumbnail images in the card
      const thumbnailImages = firstCard.locator('img');
      const thumbnailCount = await thumbnailImages.count();
      
      console.log(`Found ${thumbnailCount} thumbnail images in first card`);
      
      if (thumbnailCount > 0) {
        const firstThumbnail = thumbnailImages.first();
        
        // Get thumbnail dimensions
        const thumbnailWidth = await firstThumbnail.getAttribute('width');
        const thumbnailHeight = await firstThumbnail.getAttribute('height');
        
        console.log(`Thumbnail dimensions: ${thumbnailWidth}x${thumbnailHeight}`);
        
        // Hover over the card to trigger preview
        await firstCard.hover();
        console.log('✅ Hovered over image card');
        
        // Wait for hover preview (has 2 second delay)
        await page.waitForTimeout(3000);
        
        // Look for hover preview
        const hoverPreview = page.locator('div[class*="fixed"][class*="bg-background"]');
        if (await hoverPreview.isVisible()) {
          console.log('✅ Found hover preview popup');
          
          // Check preview image dimensions
          const previewImage = hoverPreview.locator('img');
          if (await previewImage.isVisible()) {
            const previewWidth = await previewImage.getAttribute('width');
            const previewHeight = await previewImage.getAttribute('height');
            
            console.log(`Preview image dimensions: ${previewWidth}x${previewHeight}`);
            
            // Calculate expected size (200% of thumbnail)
            const expectedWidth = Number(thumbnailWidth) * 2;
            const expectedHeight = Number(thumbnailHeight) * 2;
            
            console.log(`Expected preview size: ${expectedWidth}x${expectedHeight}`);
            
            // Verify preview is 200% larger
            expect(Number(previewWidth)).toBe(expectedWidth);
            expect(Number(previewHeight)).toBe(expectedHeight);
            
            console.log('✅ Main app hover preview is correctly 200% larger than thumbnail');
            
            // Take screenshot
            await page.screenshot({ path: 'main-app-200percent-hover.png' });
          } else {
            console.log('❌ No preview image found in hover popup');
          }
        } else {
          console.log('❌ No hover preview found in main app');
        }
      } else {
        console.log('❌ No thumbnail images found in main app');
      }
    } else {
      console.log('❌ No image cards found in main app');
    }
  });
  
  test('should verify hover preview sizing calculations', async ({ page }) => {
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Get all thumbnail images
    const thumbnailImages = page.locator('img[width="48"]');
    const thumbnailCount = await thumbnailImages.count();
    
    console.log(`Found ${thumbnailCount} thumbnail images`);
    
    // Test multiple hover previews
    for (let i = 0; i < Math.min(thumbnailCount, 3); i++) {
      const thumbnail = thumbnailImages.nth(i);
      const groupContainer = thumbnail.locator('..').locator('..');
      
      console.log(`Testing thumbnail ${i}...`);
      
      // Hover over the container
      await groupContainer.hover();
      await page.waitForTimeout(1000);
      
      // Find the corresponding hover preview
      const hoverPreview = page.locator('img[width="96"]').nth(i);
      if (await hoverPreview.isVisible()) {
        console.log(`✅ Hover preview ${i} is visible`);
        
        // Check dimensions
        const width = await hoverPreview.getAttribute('width');
        const height = await hoverPreview.getAttribute('height');
        
        console.log(`Preview ${i} dimensions: ${width}x${height}`);
        
        // Verify 200% scaling
        expect(width).toBe('96');
        expect(height).toBe('96');
        
        console.log(`✅ Preview ${i} correctly sized`);
      } else {
        console.log(`❌ Hover preview ${i} not visible`);
      }
      
      // Move mouse away
      await page.mouse.move(0, 0);
      await page.waitForTimeout(500);
    }
    
    console.log('✅ All hover preview size calculations verified');
  });
});