#!/usr/bin/env node

/**
 * Process Monitor - Prevent Node.js Process Leaks
 * 
 * This script monitors and prevents the accumulation of zombie Node.js processes
 * that can consume excessive memory and cause system instability.
 * 
 * Features:
 * - Real-time process count monitoring
 * - Automatic cleanup of zombie processes
 * - Early warning system
 * - Project-specific process tracking
 * - Emergency shutdown protocols
 */

const { exec } = require('child_process');
const util = require('util');
const path = require('path');

const execAsync = util.promisify(exec);

class ProcessMonitor {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.maxNodeProcesses = 15; // Warning threshold
        this.criticalNodeProcesses = 25; // Emergency threshold
        this.monitoringInterval = 30000; // 30 seconds
        this.isMonitoring = false;
    }

    /**
     * Start monitoring Node.js processes
     */
    async startMonitoring() {
        if (this.isMonitoring) {
            console.log('⚠️ Process monitor is already running');
            return;
        }

        console.log('🔍 Starting Node.js process monitor...');
        console.log(`📊 Thresholds: Warning=${this.maxNodeProcesses}, Critical=${this.criticalNodeProcesses}`);
        
        this.isMonitoring = true;
        this.monitor();
    }

    /**
     * Stop monitoring
     */
    stopMonitoring() {
        this.isMonitoring = false;
        console.log('🛑 Process monitor stopped');
    }

    /**
     * Main monitoring loop
     */
    async monitor() {
        while (this.isMonitoring) {
            try {
                const processCount = await this.getNodeProcessCount();
                const projectProcesses = await this.getProjectProcesses();
                
                console.log(`📊 Node processes: ${processCount.total} (Project: ${projectProcesses.length})`);
                
                if (processCount.total >= this.criticalNodeProcesses) {
                    console.log('🚨 CRITICAL: Too many Node.js processes detected!');
                    await this.emergencyCleanup();
                } else if (processCount.total >= this.maxNodeProcesses) {
                    console.log('⚠️ WARNING: High Node.js process count detected');
                    await this.cleanupZombieProcesses();
                }
                
                // Monitor memory usage
                if (processCount.memoryMB > 2000) {
                    console.log(`⚠️ WARNING: Node.js processes consuming ${processCount.memoryMB}MB memory`);
                }
                
            } catch (error) {
                console.error('❌ Error during monitoring:', error.message);
            }
            
            // Wait for next check
            await new Promise(resolve => setTimeout(resolve, this.monitoringInterval));
        }
    }

    /**
     * Get Node.js process count and memory usage
     */
    async getNodeProcessCount() {
        try {
            const { stdout } = await execAsync('tasklist /FI "IMAGENAME eq node.exe" /FO CSV | findstr /C:"node.exe"');
            const lines = stdout.trim().split('\n').filter(line => line.includes('node.exe'));
            
            let totalMemory = 0;
            lines.forEach(line => {
                const match = line.match(/"([0-9,]+)"/g);
                if (match && match.length >= 5) {
                    const memoryStr = match[4].replace(/"/g, '').replace(/,/g, '');
                    totalMemory += parseInt(memoryStr) || 0;
                }
            });
            
            return {
                total: lines.length,
                memoryKB: totalMemory,
                memoryMB: Math.round(totalMemory / 1024)
            };
        } catch (error) {
            return { total: 0, memoryKB: 0, memoryMB: 0 };
        }
    }

    /**
     * Get project-specific Node.js processes
     */
    async getProjectProcesses() {
        try {
            const { stdout } = await execAsync(
                `wmic process where "Name='node.exe'" get ProcessId,CommandLine /format:csv`,
                { timeout: 10000 }
            );
            
            const processes = [];
            const lines = stdout.split('\n').filter(line => line.includes('node.exe'));
            
            lines.forEach(line => {
                if (line.includes('dl-organizer') || 
                    line.includes('bulletproof-startup') ||
                    line.includes('start-dev-smart') ||
                    line.includes(this.projectRoot.replace(/\\/g, '/'))) {
                    
                    const parts = line.split(',');
                    if (parts.length >= 3) {
                        const pid = parts[2].trim();
                        if (pid && pid !== '0') {
                            processes.push({
                                pid: parseInt(pid),
                                commandLine: parts[1] || 'unknown'
                            });
                        }
                    }
                }
            });
            
            return processes;
        } catch (error) {
            console.warn('⚠️ Could not get project processes:', error.message);
            return [];
        }
    }

    /**
     * Clean up zombie processes (gentle approach)
     */
    async cleanupZombieProcesses() {
        console.log('🧹 Cleaning up zombie Node.js processes...');
        
        try {
            const projectProcesses = await this.getProjectProcesses();
            let cleanedCount = 0;
            
            for (const proc of projectProcesses) {
                try {
                    // Check if process is still running and seems stuck
                    const { stdout } = await execAsync(`tasklist /FI "PID eq ${proc.pid}" /FO CSV`);
                    
                    if (stdout.includes('node.exe')) {
                        // Gentle termination first
                        await execAsync(`taskkill /PID ${proc.pid}`, { timeout: 5000 });
                        console.log(`✅ Cleaned zombie process PID: ${proc.pid}`);
                        cleanedCount++;
                    }
                } catch (error) {
                    // Process might already be gone, which is fine
                }
            }
            
            if (cleanedCount > 0) {
                console.log(`✅ Cleaned ${cleanedCount} zombie processes`);
            } else {
                console.log('ℹ️ No zombie processes found to clean');
            }
            
        } catch (error) {
            console.warn('⚠️ Error during zombie cleanup:', error.message);
        }
    }

    /**
     * Emergency cleanup (aggressive approach)
     */
    async emergencyCleanup() {
        console.log('🚨 EMERGENCY CLEANUP: Stopping all project Node.js processes...');
        
        try {
            const projectProcesses = await this.getProjectProcesses();
            
            if (projectProcesses.length > 0) {
                console.log(`⚔️ Forcefully terminating ${projectProcesses.length} project processes...`);
                
                for (const proc of projectProcesses) {
                    try {
                        await execAsync(`taskkill /F /PID ${proc.pid}`, { timeout: 3000 });
                    } catch (error) {
                        // Ignore errors - process might already be gone
                    }
                }
                
                // Wait for processes to die
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                console.log('✅ Emergency cleanup completed');
            } else {
                console.log('ℹ️ No project processes found for cleanup');
            }
            
        } catch (error) {
            console.error('❌ Emergency cleanup failed:', error.message);
        }
    }

    /**
     * Manual process check (one-time)
     */
    async checkProcesses() {
        console.log('🔍 Checking Node.js processes...');
        
        const processCount = await this.getNodeProcessCount();
        const projectProcesses = await this.getProjectProcesses();
        
        console.log('\n📊 PROCESS REPORT:');
        console.log(`Total Node.js processes: ${processCount.total}`);
        console.log(`Memory usage: ${processCount.memoryMB} MB`);
        console.log(`Project-specific processes: ${projectProcesses.length}`);
        
        if (projectProcesses.length > 0) {
            console.log('\n🎯 PROJECT PROCESSES:');
            projectProcesses.forEach(proc => {
                console.log(`  PID: ${proc.pid} | ${proc.commandLine.substring(0, 80)}...`);
            });
        }
        
        // Recommendations
        if (processCount.total >= this.criticalNodeProcesses) {
            console.log('\n🚨 CRITICAL: Consider running emergency cleanup');
        } else if (processCount.total >= this.maxNodeProcesses) {
            console.log('\n⚠️ WARNING: Consider cleaning zombie processes');
        } else {
            console.log('\n✅ Process count is healthy');
        }
    }
}

// CLI interface
if (require.main === module) {
    const monitor = new ProcessMonitor();
    const command = process.argv[2] || 'check';
    
    switch (command) {
        case 'start':
            monitor.startMonitoring().catch(console.error);
            break;
            
        case 'check':
            monitor.checkProcesses().then(() => process.exit(0)).catch(console.error);
            break;
            
        case 'cleanup':
            monitor.cleanupZombieProcesses().then(() => process.exit(0)).catch(console.error);
            break;
            
        case 'emergency':
            monitor.emergencyCleanup().then(() => process.exit(0)).catch(console.error);
            break;
            
        default:
            console.log(`
Process Monitor - Prevent Node.js Process Leaks

Commands:
  check     - Check current process status (default)
  cleanup   - Clean up zombie processes (gentle)
  emergency - Emergency cleanup (aggressive)
  start     - Start continuous monitoring

Usage: node process-monitor.js [command]
`);
            break;
    }
}

module.exports = ProcessMonitor;