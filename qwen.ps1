#!/usr/bin/env pwsh
# Qwen Wrapper - Forces correct model and configuration

# Set working directory
Set-Location "C:\claude\dl-organizer"

# Set environment variables
$env:OPENAI_API_KEY = "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"
$env:OPENAI_BASE_URL = "https://openrouter.ai/api/v1"

# Force the model using different methods
$env:MODEL = "qwen/qwen3-coder"
$env:QWEN_MODEL = "qwen/qwen3-coder"
$env:DEFAULT_MODEL = "qwen/qwen3-coder"

# Parse arguments properly
if ($args.Count -eq 0) {
    # No arguments - start interactive mode with correct model
    Write-Host "🚀 Starting Qwen3-Coder (OpenRouter)..." -ForegroundColor Cyan
    & qwen --model "qwen/qwen3-coder"
} elseif ($args[0] -eq "--prompt" -or $args[0] -eq "-p") {
    # Handle prompt argument
    $promptText = $args[1..($args.Count-1)] -join " "
    & qwen --model "qwen/qwen3-coder" --prompt $promptText
} else {
    # Check if model is already specified
    $hasModel = $false
    for ($i = 0; $i -lt $args.Count; $i++) {
        if ($args[$i] -eq "--model" -or $args[$i] -eq "-m") {
            $hasModel = $true
            break
        }
    }
    
    if (-not $hasModel) {
        # Add model if not specified
        & qwen --model "qwen/qwen3-coder" @args
    } else {
        # User specified model, pass through
        & qwen @args
    }
}
