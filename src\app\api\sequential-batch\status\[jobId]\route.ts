import { NextRequest, NextResponse } from "next/server";
import { getBackendUrl } from "@/utils/port-config";

export async function GET(
  request: NextRequest,
  { params }: { params: { jobId: string } }
) {
  try {
    const { jobId } = params;

    // Get the backend URL
    const backendUrl = await getBackendUrl();

    // Forward the request to the backend
    const response = await fetch(`${backendUrl}/api/sequential-batch/status/${jobId}`, {
      method: "GET",
      headers: {
        "User-Agent": request.headers.get("User-Agent") || "",
      },
    });

    // Get response data
    const data = await response.text();

    // Return response with same status and headers
    return new NextResponse(data, {
      status: response.status,
      statusText: response.statusText,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("API Proxy Error (sequential-batch/status):", error);
    return NextResponse.json(
      {
        success: false,
        error: "Proxy error: Failed to get sequential batch status from backend",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}