# Test backend API with debug info
$body = @{
    folderPath = "C:\claude\dl-organizer\test-samples\i18"
    recursive = $true
    generateThumbnails = $false
} | ConvertTo-Json

Write-Host "Testing folder listing first..."

# First, test folder scanning
$folderBody = @{
    path = "C:\claude\dl-organizer\test-samples\i18"
    includeStats = $false
} | ConvertTo-Json

try {
    Write-Host "Testing folder scan API..."
    $folderResponse = Invoke-RestMethod -Uri "http://127.0.0.1:3574/api/filesystem/folders" -Method POST -Body $folderBody -ContentType "application/json"
    Write-Host "Folder scan response:"
    $folderResponse | ConvertTo-Json -Depth 3
} catch {
    Write-Host "Folder scan error: $_"
}

Write-Host "`n" + "="*50 + "`n"

Write-Host "Testing image API with body: $body"

try {
    $response = Invoke-RestMethod -Uri "http://127.0.0.1:3574/api/filesystem/images" -Method POST -Body $body -ContentType "application/json"
    Write-Host "Images API Response:"
    $response | ConvertTo-Json -Depth 5
} catch {
    Write-Host "Images API Error: $_"
    Write-Host "Response: $($_.Exception.Response)"
}

Write-Host "`n" + "="*50 + "`n"

# Let's also check what files are actually in that directory
Write-Host "Directory contents (PowerShell):"
Get-ChildItem "C:\claude\dl-organizer\test-samples\i18" | ForEach-Object {
    Write-Host "$($_.Name) - $($_.Extension.ToLower())"
}