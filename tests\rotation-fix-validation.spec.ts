import { test, expect } from '@playwright/test';

// Test the rotation fixes for NSW DL project
test.describe('Rotation Fix Validation Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
  });

  test('should load NSW DLs project and test rotation functionality', async ({ page }) => {
    // Load the NSW DLs project
    await page.getByRole('button', { name: /load project/i }).click();
    
    // Look for folder input and fill with NSW DLs path
    const folderInput = page.locator('input[placeholder*="folder"], input[placeholder*="directory"]');
    await folderInput.fill('C:\\claude\\dl-organizer\\files\\9172-NSW-DLs');
    
    // Submit or load the project
    await page.getByRole('button', { name: /load/i }).click();
    await page.waitForTimeout(3000); // Wait for project to load
    
    // Should see images in the grid
    await expect(page.locator('.grid').first()).toBeVisible();
    
    // Look for an image to test rotation on
    const firstImage = page.locator('[data-testid="image-card"], .grid img').first();
    await expect(firstImage).toBeVisible();
    
    // Hover to reveal rotation controls
    await firstImage.hover();
    
    // Find rotation button (clockwise)
    const rotateButton = page.locator('button[title*="clockwise"], button[title*="Rotate"]').first();
    if (await rotateButton.isVisible()) {
      // Click rotate button
      await rotateButton.click();
      
      // Should show processing indicator
      await expect(page.getByText(/rotating/i)).toBeVisible();
      
      // Wait for rotation to complete (should not take more than 10 seconds)
      await expect(page.getByText(/rotation completed/i)).toBeVisible({ timeout: 10000 });
      
      // Should not show "processing" spinner indefinitely
      await expect(page.locator('.animate-spin')).not.toBeVisible({ timeout: 5000 });
    }
  });

  test('should test OCR text file save with NSW state validation', async ({ page }) => {
    // Navigate to OCR testing page first to test state validation
    await page.goto('http://localhost:3001/ocr-testing');
    await page.waitForLoadState('networkidle');
    
    // Check if there's a form for testing OCR data
    const form = page.locator('form, .ocr-form').first();
    if (await form.isVisible()) {
      // Fill in NSW data to test state validation
      const firstNameInput = page.locator('input[name="firstName"], input[placeholder*="first name"]');
      if (await firstNameInput.isVisible()) {
        await firstNameInput.fill('Test');
      }
      
      const lastNameInput = page.locator('input[name="lastName"], input[placeholder*="last name"]');
      if (await lastNameInput.isVisible()) {
        await lastNameInput.fill('User');
      }
      
      const licenseInput = page.locator('input[name="licenseNumber"], input[placeholder*="license"]');
      if (await licenseInput.isVisible()) {
        await licenseInput.fill('123456789');
      }
      
      const stateInput = page.locator('input[name="state"], input[placeholder*="state"]');
      if (await stateInput.isVisible()) {
        await stateInput.fill('NSW');
        
        // Try to save/submit
        const saveButton = page.locator('button[type="submit"], button:has-text("Save")').first();
        if (await saveButton.isVisible()) {
          await saveButton.click();
          
          // Should NOT show the old strict validation error
          await expect(page.getByText(/State must be 2 characters \(e\.g\., CA, NY, TX\)/)).not.toBeVisible();
          
          // Should accept NSW as valid
          await expect(page.getByText(/saved successfully/i)).toBeVisible({ timeout: 5000 });
        }
      }
    }
  });

  test('should verify rotation API endpoint works correctly', async ({ page }) => {
    // Test the rotation API directly
    const imageId = Buffer.from('C:\\claude\\dl-organizer\\files\\9172-NSW-DLs\\alextelferdl.jpg').toString('base64');
    
    const response = await page.request.post(`http://localhost:3003/api/images/${imageId}/rotate`, {
      data: { degrees: 90 }
    });
    
    expect(response.status()).toBe(200);
    
    const data = await response.json();
    expect(data.success).toBe(true);
    expect(data.result.rotated).toBe(90);
  });

  test('should verify thumbnail/preview endpoints work after rotation', async ({ page }) => {
    // Test that thumbnail and preview generation work
    const imageId = Buffer.from('C:\\claude\\dl-organizer\\files\\9172-NSW-DLs\\alextelferdl.jpg').toString('base64');
    
    // Test thumbnail endpoint
    const thumbnailResponse = await page.request.get(`http://localhost:3003/api/images/${imageId}/thumbnail`);
    expect(thumbnailResponse.status()).toBe(200);
    expect(thumbnailResponse.headers()['content-type']).toContain('image');
    
    // Test preview endpoint
    const previewResponse = await page.request.get(`http://localhost:3003/api/images/${imageId}/preview`);
    expect(previewResponse.status()).toBe(200);
    expect(previewResponse.headers()['content-type']).toContain('image');
  });

  test('should verify no infinite processing spinners', async ({ page }) => {
    // Load the NSW DLs project and verify no stuck processing states
    await page.getByRole('button', { name: /load project/i }).click();
    
    const folderInput = page.locator('input[placeholder*="folder"], input[placeholder*="directory"]');
    await folderInput.fill('C:\\claude\\dl-organizer\\files\\9172-NSW-DLs');
    await page.getByRole('button', { name: /load/i }).click();
    await page.waitForTimeout(3000);
    
    // Should not have any spinning elements stuck indefinitely
    const spinners = page.locator('.animate-spin');
    const spinnerCount = await spinners.count();
    
    if (spinnerCount > 0) {
      // Wait a reasonable time for legitimate processing to complete
      await page.waitForTimeout(5000);
      
      // After 5 seconds, there should be no permanent spinners
      await expect(page.locator('.animate-spin')).toHaveCount(0);
    }
  });
});