"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Edit3, Save, RefreshCw, Plus, Trash2, AlertTriangle, Eye, FileText, Info } from 'lucide-react'
import { cn } from '@/lib/utils'

interface FieldTemplate {
  id: string
  name: string
  type: 'text' | 'date' | 'number' | 'select'
  required: boolean
  description: string
  placeholder?: string
  options?: string[]
  validation?: string
}

interface DocumentTemplate {
  id: string
  name: string
  description: string
  fields: FieldTemplate[]
  aiPrompt: string
  examples: string[]
}

interface PromptEditorProps {
  templateType: 'us-driver' | 'aus-driver' | 'passport' | 'auto-detect'
  isOpen: boolean
  onOpenChange: (open: boolean) => void
}

const defaultTemplates: Record<string, DocumentTemplate> = {
  'us-driver': {
    id: 'us-driver',
    name: 'US Driver License',
    description: 'Standard US driver license format with first, middle, last name structure',
    fields: [
      { id: 'firstName', name: 'First Name', type: 'text', required: true, description: 'Given first name' },
      { id: 'middleName', name: 'Middle Name', type: 'text', required: false, description: 'Middle name or initial (optional)' },
      { id: 'lastName', name: 'Last Name', type: 'text', required: true, description: 'Family/surname' },
      { id: 'dateOfBirth', name: 'Date of Birth', type: 'date', required: true, description: 'Date of birth in YYYY-MM-DD format' },
      { id: 'address', name: 'Address', type: 'text', required: false, description: 'Full residential address' },
      { id: 'licenseNumber', name: 'License Number', type: 'text', required: true, description: 'Driver license number' },
      { id: 'state', name: 'State', type: 'text', required: true, description: 'Two-letter state code (e.g., CA, NY, TX)' },
      { id: 'issueDate', name: 'Issue Date', type: 'date', required: false, description: 'License issue date' },
      { id: 'expirationDate', name: 'Expiration Date', type: 'date', required: false, description: 'License expiration date' },
      { id: 'cardSide', name: 'Card Side', type: 'select', required: true, description: 'Front or back side detection', options: ['front', 'back'] }
    ],
    aiPrompt: `Analyze this US driver's license image and extract the following information into a JSON object.

IMPORTANT: Also determine if this is the FRONT or BACK side of the license:
- FRONT side typically contains: photo, name, address, DOB, license number, issue/expiration dates
- BACK side typically contains: restrictions, endorsements, donor info, barcodes, conditions

{
  "firstName": "extracted first name",
  "middleName": "extracted middle name (if present)",
  "lastName": "extracted last name", 
  "dateOfBirth": "YYYY-MM-DD format",
  "address": "full address",
  "licenseNumber": "license number",
  "expirationDate": "YYYY-MM-DD format",
  "issueDate": "YYYY-MM-DD format",
  "state": "two-letter state code (e.g., CA, NY, TX)",
  "cardSide": "front or back - determine which side of the license this is",
  "confidence": 0.0-1.0,
  "rawText": "all visible text from the image",
  "mode": "us"
}

Important notes:
- Use exact formatting for dates (YYYY-MM-DD)
- Extract complete address including city, state, zip code
- Be precise with license numbers (include all digits/letters)
- Extract middle name if present, otherwise use empty string
- Set confidence based on text clarity and completeness (0.0-1.0)
- Include ALL visible text in rawText field
- If any field is unclear or missing, use empty string ""
- State should be 2-letter code (e.g., CA, NY, TX, FL)
- CRITICAL: Return ONLY the JSON object, no additional text or explanations`,
    examples: [
      'Standard US driver license with all fields clearly visible',
      'Partially occluded license with some fields missing',
      'Back side of license with restrictions and endorsements'
    ]
  },
  'aus-driver': {
    id: 'aus-driver',
    name: 'Australian Driver License',
    description: 'Australian driver license format with surname/given names structure and card numbers',
    fields: [
      { id: 'surname', name: 'Surname', type: 'text', required: true, description: 'Family/last name' },
      { id: 'givenNames', name: 'Given Names', type: 'text', required: true, description: 'First and middle names combined' },
      { id: 'firstName', name: 'First Name', type: 'text', required: true, description: 'First name only (derived from given names)' },
      { id: 'middleName', name: 'Middle Name', type: 'text', required: false, description: 'Middle name(s) if present' },
      { id: 'lastName', name: 'Last Name', type: 'text', required: true, description: 'Family name (same as surname)' },
      { id: 'dateOfBirth', name: 'Date of Birth', type: 'date', required: true, description: 'Date of birth (DOB)' },
      { id: 'address', name: 'Address', type: 'text', required: false, description: 'Full residential address' },
      { id: 'licenseNumber', name: 'License Number', type: 'text', required: true, description: 'Driver license number (DL #)' },
      { id: 'cardNumber', name: 'Card Number', type: 'text', required: false, description: 'Card number if present (state-dependent)' },
      { id: 'expirationDate', name: 'Expiration Date', type: 'date', required: false, description: 'Expiration date (EXP DATE)' },
      { id: 'state', name: 'State/Territory', type: 'text', required: true, description: 'Australian state/territory (NSW, VIC, QLD, etc.)' },
      { id: 'cardSide', name: 'Card Side', type: 'select', required: true, description: 'Front or back side detection', options: ['front', 'back'] }
    ],
    aiPrompt: `Analyze this Australian driver license image and extract the following information into a JSON object:

{
  "surname": "family/last name",
  "givenNames": "given names (first and middle names combined)",
  "firstName": "first name only (derived from given names)",
  "middleName": "middle name(s) if present (derived from given names)",
  "lastName": "family name (same as surname)",
  "dateOfBirth": "YYYY-MM-DD format (DOB)",
  "address": "full residential address",
  "licenseNumber": "driver license number (DL #)",
  "cardNumber": "card number if present (state-dependent)",
  "expirationDate": "YYYY-MM-DD format (EXP DATE)",
  "state": "Australian state/territory (NSW, VIC, QLD, WA, SA, TAS, NT, ACT)",
  "confidence": 0.0-1.0,
  "rawText": "all visible text from the image",
  "mode": "australian",
  "cardSide": "front or back (determine based on content)"
}

Australian driver license field guide:
1. SURNAME: Family name (usually prominent on front)
2. GIVEN NAMES: First and middle names combined (on front)
3. ADDRESS: Residential address (often on back side in NSW, QLD, WA, SA, NT, ACT)
4. DOB: Date of birth (front side)
5. DL #: Driver license number (front side)
6. CARD #: Card number (state-dependent, can be front/back/both)
7. EXP DATE: Expiration date (front side)
8. STATE: NSW, VIC, QLD, WA, SA, TAS, NT, or ACT

State-specific notes:
- NSW: Address typically on back, card number on front or back
- VIC: Address usually on front, card number on back
- QLD: Address on back, card number on front
- WA, SA, NT, ACT: Address on back, card number on front
- TAS: Address and card number usually on front

Important instructions:
- Use exact formatting for dates (YYYY-MM-DD)
- Split given names: first word → firstName, remaining → middleName
- Map surname → lastName for compatibility
- Extract complete address (street, suburb, state, postcode)
- Identify card side based on visible fields
- Set confidence based on text clarity and completeness
- If address is missing and this appears to be front side, note in rawText
- Include ALL visible text in rawText field
- If any field is unclear or missing, use empty string ""
- CRITICAL: Return ONLY the JSON object, no additional text or explanations`,
    examples: [
      'NSW license with address on back side',
      'VIC license with address on front side',
      'QLD license with card number on front'
    ]
  },
  'auto-detect': {
    id: 'auto-detect',
    name: 'Auto-Detect Document',
    description: 'Intelligent auto-detection for both US and Australian documents with adaptive field extraction',
    fields: [
      { id: 'firstName', name: 'First Name', type: 'text', required: true, description: 'Given first name' },
      { id: 'middleName', name: 'Middle Name', type: 'text', required: false, description: 'Middle name or initial (optional)' },
      { id: 'lastName', name: 'Last Name', type: 'text', required: true, description: 'Family/surname' },
      { id: 'surname', name: 'Surname', type: 'text', required: false, description: 'Family name (Australian format)' },
      { id: 'givenNames', name: 'Given Names', type: 'text', required: false, description: 'Combined first and middle names (Australian format)' },
      { id: 'dateOfBirth', name: 'Date of Birth', type: 'date', required: false, description: 'Date of birth in YYYY-MM-DD format' },
      { id: 'address', name: 'Address', type: 'text', required: false, description: 'Full residential address' },
      { id: 'licenseNumber', name: 'License Number', type: 'text', required: false, description: 'Driver license number (US format)' },
      { id: 'cardNumber', name: 'Card Number', type: 'text', required: false, description: 'Card number (Australian format)' },
      { id: 'state', name: 'State/Territory', type: 'text', required: false, description: 'State/territory code' },
      { id: 'issueDate', name: 'Issue Date', type: 'date', required: false, description: 'License issue date' },
      { id: 'expirationDate', name: 'Expiration Date', type: 'date', required: false, description: 'License expiration date' },
      { id: 'cardSide', name: 'Card Side', type: 'select', required: true, description: 'Front or back side detection', options: ['front', 'back'] },
      { id: 'documentType', name: 'Document Type', type: 'text', required: true, description: 'Detected document type (US DL, AUS DL, etc.)' },
      { id: 'mode', name: 'Detection Mode', type: 'select', required: true, description: 'Detected document mode', options: ['us', 'australian', 'auto_detect'] }
    ],
    aiPrompt: `INTELLIGENT AUTO-DETECTION: Analyze this document image and determine if it's a US or Australian driver's license, then extract information accordingly.

STEP 1: DOCUMENT TYPE DETECTION
First, identify the document type by looking for these indicators:
- US Driver License: "Driver License", state names (California, Texas, etc.), US state abbreviations (CA, TX, NY, FL, etc.)
- Australian Driver License: "Driver Licence", state/territory names (New South Wales, Victoria, etc.), AU codes (NSW, VIC, QLD, WA, SA, TAS, NT, ACT)

STEP 2: ADAPTIVE FIELD EXTRACTION
Based on detected type, extract fields using the appropriate format:

FOR US DRIVER LICENSES:
{
  "firstName": "first name",
  "middleName": "middle name (if present)", 
  "lastName": "last name",
  "dateOfBirth": "YYYY-MM-DD format",
  "address": "full address",
  "licenseNumber": "license number",
  "state": "two-letter state code (CA, NY, TX, etc.)",
  "issueDate": "YYYY-MM-DD format",
  "expirationDate": "YYYY-MM-DD format",
  "cardSide": "front or back",
  "documentType": "US DL",
  "mode": "us",
  "confidence": 0.0-1.0,
  "rawText": "all visible text"
}

FOR AUSTRALIAN DRIVER LICENSES:
{
  "surname": "family/last name",
  "givenNames": "combined first and middle names",
  "firstName": "first name only (derived from given names)",
  "middleName": "middle names (derived from given names)", 
  "lastName": "family name (same as surname)",
  "dateOfBirth": "YYYY-MM-DD format",
  "address": "full address",
  "licenseNumber": "license number if present",
  "cardNumber": "card number if present",
  "state": "state/territory code (NSW, VIC, QLD, etc.)",
  "expirationDate": "YYYY-MM-DD format",
  "cardSide": "front or back",
  "documentType": "AUS DL", 
  "mode": "australian",
  "confidence": 0.0-1.0,
  "rawText": "all visible text"
}

CRITICAL INSTRUCTIONS:
1. ALWAYS determine document type first before field extraction
2. Use exact date formatting (YYYY-MM-DD) regardless of source format
3. Set confidence based on text clarity and successful type detection
4. Include ALL visible text in rawText field
5. If uncertain about type, default to "auto_detect" mode and extract whatever fields are available
6. For missing fields, use empty string ""
7. RETURN ONLY the JSON object, no additional text or explanations

EXAMPLES OF DETECTION INDICATORS:
- US: "Driver License", "DL", "State of California", "TX", "NY", US formatting
- Australian: "Driver Licence", "NSW", "VIC", "QLD", "New South Wales", Australian formatting`,
    examples: [
      'US license with standard formatting detected',
      'Australian license with state-specific layout detected',
      'Ambiguous document requiring careful type detection',
      'International license requiring fallback extraction'
    ]
  },
  'passport': {
    id: 'passport',
    name: 'Passport Document',
    description: 'International passport document format with MRZ and biographical data',
    fields: [
      { id: 'firstName', name: 'First Name', type: 'text', required: true, description: 'Given first name' },
      { id: 'middleName', name: 'Middle Name', type: 'text', required: false, description: 'Middle name(s) if present' },
      { id: 'lastName', name: 'Last Name', type: 'text', required: true, description: 'Family/surname' },
      { id: 'dateOfBirth', name: 'Date of Birth', type: 'date', required: true, description: 'Date of birth in YYYY-MM-DD format' },
      { id: 'placeOfBirth', name: 'Place of Birth', type: 'text', required: false, description: 'City/country of birth' },
      { id: 'nationality', name: 'Nationality', type: 'text', required: false, description: 'Nationality or citizenship' },
      { id: 'passportNumber', name: 'Passport Number', type: 'text', required: true, description: 'Passport document number' },
      { id: 'issuingCountry', name: 'Issuing Country', type: 'text', required: true, description: 'Country code that issued passport' },
      { id: 'issueDate', name: 'Issue Date', type: 'date', required: false, description: 'Passport issue date' },
      { id: 'expirationDate', name: 'Expiration Date', type: 'date', required: true, description: 'Passport expiration date' },
      { id: 'sex', name: 'Sex', type: 'select', required: false, description: 'Gender marker', options: ['M', 'F', 'X'] },
      { id: 'cardSide', name: 'Card Side', type: 'select', required: true, description: 'Front or back side detection', options: ['front', 'back'] }
    ],
    aiPrompt: `Analyze this passport document image and extract the following information into a JSON object.

PASSPORT FIELD IDENTIFICATION:
- Look for biographical page (usually page with photo)
- Machine Readable Zone (MRZ) at bottom (two lines of characters)
- Official stamps, seals, and security features

{
  "firstName": "given first name",
  "middleName": "middle name(s) if present",
  "lastName": "family name/surname",
  "dateOfBirth": "YYYY-MM-DD format (date of birth)",
  "placeOfBirth": "city/country of birth",
  "nationality": "nationality/citizenship",
  "passportNumber": "passport document number",
  "issuingCountry": "three-letter country code (e.g., USA, AUS, GBR)",
  "issueDate": "YYYY-MM-DD format (date of issue)",
  "expirationDate": "YYYY-MM-DD format (date of expiry)",
  "sex": "M, F, or X (gender marker)",
  "cardSide": "front or back (determine based on content)",
  "confidence": 0.0-1.0,
  "rawText": "all visible text from the page",
  "mode": "passport"
}

PASSPORT-SPECIFIC NOTES:
1. Names may appear in multiple formats (given names, surnames)
2. Dates can be in various formats - convert to YYYY-MM-DD
3. MRZ contains encoded information - decode if visible
4. Country codes should be 3-letter ISO codes (USA, AUS, CAN, GBR, etc.)
5. Look for official passport terminology and layout
6. Security features indicate authenticity
7. Multiple languages may be present

IMPORTANT INSTRUCTIONS:
- Use exact formatting for dates (YYYY-MM-DD)
- Extract complete names including all given names
- Identify issuing country from passport cover or header
- Set confidence based on text clarity and document authenticity indicators
- Include ALL visible text in rawText field
- If any field is unclear or missing, use empty string ""
- CRITICAL: Return ONLY the JSON object, no additional text or explanations`,
    examples: [
      'US passport biographical page with MRZ',
      'Australian passport with security features',
      'European passport with multiple language text',
      'Damaged or partially visible passport page'
    ]
  },
  'folder-analysis': {
    id: 'folder-analysis',
    name: 'Folder Analysis',
    description: 'AI prompt for analyzing image folders and providing smart filtering suggestions',
    fields: [
      { id: 'imageTypes', name: 'Image Types', type: 'text', required: false, description: 'Types of images detected (e.g., documents, selfies, etc.)' },
      { id: 'documentSides', name: 'Document Sides', type: 'text', required: false, description: 'Front/back classification for documents' },
      { id: 'qualityCategories', name: 'Quality Categories', type: 'text', required: false, description: 'Image quality classifications' },
      { id: 'filenamePatterns', name: 'Filename Patterns', type: 'text', required: false, description: 'Common filename patterns found' },
      { id: 'sizeCategories', name: 'Size Categories', type: 'text', required: false, description: 'File size groupings' },
      { id: 'resolutionCategories', name: 'Resolution Categories', type: 'text', required: false, description: 'Image resolution groupings' }
    ],
    aiPrompt: `Analyze this folder of images and provide intelligent categorization for smart filtering.

For each image, determine:
1. Image type (document, selfie, photo, screenshot, etc.)
2. If document: classify as front/back side
3. Quality assessment (high/medium/low)
4. Notable filename patterns
5. File size and resolution patterns

Provide summary statistics and suggest useful filtering categories that would help users organize and find specific images quickly.

Focus on practical categorization that enables smart selection features.

Return a JSON object with categorization results and smart filter suggestions.`,
    examples: [
      'Mixed folder with driver license photos and selfies',
      'Batch of document scans with front/back pairs',
      'Screenshots and photos mixed together',
      'Organized folder with consistent naming patterns'
    ]
  }
}

export default function PromptEditor({ templateType, isOpen, onOpenChange }: PromptEditorProps) {
  const [template, setTemplate] = useState<DocumentTemplate>(defaultTemplates[templateType])
  const [editedPrompt, setEditedPrompt] = useState('')
  const [customFields, setCustomFields] = useState<FieldTemplate[]>([])
  const [removedStandardFields, setRemovedStandardFields] = useState<string[]>([])
  const [showDeleteConfirm, setShowDeleteConfirm] = useState<string | null>(null)
  const [newField, setNewField] = useState<Partial<FieldTemplate>>({
    name: '',
    type: 'text',
    required: false,
    description: ''
  })
  const [isAddingField, setIsAddingField] = useState(false)
  const [previewMode, setPreviewMode] = useState(false)

  useEffect(() => {
    if (defaultTemplates[templateType]) {
      setTemplate(defaultTemplates[templateType])
      setEditedPrompt(defaultTemplates[templateType].aiPrompt)
    }
  }, [templateType])

  const handleSavePrompt = () => {
    // Here you would typically save to backend/localStorage
    console.log('Saving prompt for', templateType, editedPrompt)
    // For now, just update local state
    setTemplate(prev => ({ ...prev, aiPrompt: editedPrompt }))
    // Show success message or close dialog
  }

  const handleAddCustomField = () => {
    if (!newField.name) return

    const field: FieldTemplate = {
      id: newField.name.toLowerCase().replace(/\s+/g, '_'),
      name: newField.name,
      type: newField.type || 'text',
      required: newField.required || false,
      description: newField.description || '',
      placeholder: newField.placeholder,
      options: newField.options
    }

    setCustomFields(prev => [...prev, field])
    setNewField({ name: '', type: 'text', required: false, description: '' })
    setIsAddingField(false)
  }

  const handleRemoveCustomField = (fieldId: string) => {
    setCustomFields(prev => prev.filter(f => f.id !== fieldId))
  }

  const handleRemoveStandardField = (fieldId: string) => {
    setRemovedStandardFields(prev => [...prev, fieldId])
    setShowDeleteConfirm(null)
  }

  const handleRestoreStandardField = (fieldId: string) => {
    setRemovedStandardFields(prev => prev.filter(id => id !== fieldId))
  }

  const getActiveStandardFields = () => {
    return template.fields.filter(field => !removedStandardFields.includes(field.id))
  }

  const getRemovedStandardFields = () => {
    return template.fields.filter(field => removedStandardFields.includes(field.id))
  }

  const resetToDefaults = () => {
    setEditedPrompt(defaultTemplates[templateType].aiPrompt)
    setCustomFields([])
  }

  if (!template) {
    return (
      <Dialog open={isOpen} onOpenChange={onOpenChange}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Template Not Found</DialogTitle>
          </DialogHeader>
          <div className="p-4">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                The requested template type &quot;{templateType}&quot; is not available.
              </AlertDescription>
            </Alert>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit3 className="h-5 w-5" />
            Edit {template?.name || 'Document'} Template
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Left Column - Field Configuration */}
          <div className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Field Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium mb-3 text-gray-800 dark:text-gray-200">Standard Fields</h4>
                  <div className="space-y-2">
                    {getActiveStandardFields().map((field) => (
                      <div key={field.id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg border border-gray-200 dark:border-gray-700">
                        <div className="flex items-center gap-2">
                          <Badge variant={field.required ? 'default' : 'outline'} className="text-xs bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300">
                            {field.name}
                          </Badge>
                          <span className="text-xs text-gray-500 dark:text-gray-400">{field.type}</span>
                          {field.required && <span className="text-xs text-red-500">*</span>}
                        </div>
                        <div className="flex items-center gap-1">
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            title={field.description}
                            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                          >
                            <Info className="h-3 w-3" />
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => setShowDeleteConfirm(field.id)}
                            className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
                            title="Remove field from template"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  {/* Removed Fields Section */}
                  {getRemovedStandardFields().length > 0 && (
                    <div className="mt-4">
                      <h5 className="text-sm font-medium mb-2 text-gray-600 dark:text-gray-400">Removed Fields</h5>
                      <div className="space-y-2">
                        {getRemovedStandardFields().map((field) => (
                          <div key={field.id} className="flex items-center justify-between p-2 bg-red-50 dark:bg-red-950/20 rounded border border-red-200 dark:border-red-800">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="text-xs text-red-600 border-red-300">
                                {field.name}
                              </Badge>
                              <span className="text-xs text-gray-500 line-through">{field.type}</span>
                            </div>
                            <Button 
                              variant="ghost" 
                              size="sm"
                              onClick={() => handleRestoreStandardField(field.id)}
                              className="text-green-600 hover:text-green-700 hover:bg-green-50 dark:hover:bg-green-950/20"
                              title="Restore field to template"
                            >
                              <Plus className="h-3 w-3" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h4 className="font-medium text-gray-800 dark:text-gray-200">Custom Fields</h4>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsAddingField(true)}
                      className="border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Add Field
                    </Button>
                  </div>

                  {customFields.length === 0 ? (
                    <p className="text-sm text-gray-500 dark:text-gray-400 italic">No custom fields defined</p>
                  ) : (
                    <div className="space-y-2">
                      {customFields.map((field) => (
                        <div key={field.id} className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                          <div className="flex items-center gap-2">
                            <Badge variant="info-outline" className="text-xs">
                              {field.name}
                            </Badge>
                            <span className="text-xs text-gray-500 dark:text-gray-400">{field.type}</span>
                            {field.required && <span className="text-xs text-red-500">*</span>}
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveCustomField(field.id)}
                            className="text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-950/20"
                            title="Remove custom field"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}

                  {isAddingField && (
                    <Card className="mt-4">
                      <CardContent className="pt-4 space-y-3">
                        <div className="grid grid-cols-2 gap-3">
                          <div>
                            <Label htmlFor="fieldName">Field Name</Label>
                            <Input
                              id="fieldName"
                              value={newField.name || ''}
                              onChange={(e) => setNewField(prev => ({ ...prev, name: e.target.value }))}
                              placeholder="e.g., Card Number"
                            />
                          </div>
                          <div>
                            <Label htmlFor="fieldType">Field Type</Label>
                            <Select
                              value={newField.type}
                              onValueChange={(value) => setNewField(prev => ({ ...prev, type: value as any }))}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="text">Text</SelectItem>
                                <SelectItem value="date">Date</SelectItem>
                                <SelectItem value="number">Number</SelectItem>
                                <SelectItem value="select">Select</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        <div>
                          <Label htmlFor="fieldDescription">Description</Label>
                          <Input
                            id="fieldDescription"
                            value={newField.description || ''}
                            onChange={(e) => setNewField(prev => ({ ...prev, description: e.target.value }))}
                            placeholder="What this field contains"
                          />
                        </div>
                        <div className="flex items-center gap-2">
                          <input
                            type="checkbox"
                            id="fieldRequired"
                            checked={newField.required || false}
                            onChange={(e) => setNewField(prev => ({ ...prev, required: e.target.checked }))}
                          />
                          <Label htmlFor="fieldRequired">Required field</Label>
                        </div>
                        <div className="flex gap-2">
                          <Button size="sm" onClick={handleAddCustomField}>
                            Add Field
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => setIsAddingField(false)}
                          >
                            Cancel
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Prompt Editor */}
          <div className="space-y-4">
            <Card>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">AI Prompt Template</CardTitle>
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPreviewMode(!previewMode)}
                    >
                      <Eye className="h-3 w-3 mr-1" />
                      {previewMode ? 'Edit' : 'Preview'}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={resetToDefaults}
                    >
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Reset
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {previewMode ? (
                  <div className="p-4 bg-muted/30 rounded-lg">
                    <h5 className="font-medium mb-2">Preview</h5>
                    <pre className="text-xs whitespace-pre-wrap text-muted-foreground">
                      {editedPrompt}
                    </pre>
                  </div>
                ) : (
                  <div>
                    <Label htmlFor="promptEditor">Prompt Template</Label>
                    <Textarea
                      id="promptEditor"
                      value={editedPrompt}
                      onChange={(e) => setEditedPrompt(e.target.value)}
                      rows={20}
                      className="font-mono text-sm"
                      placeholder="Enter your AI prompt template..."
                    />
                  </div>
                )}

                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Changes to prompts will affect all future OCR processing for this document type.
                    Test thoroughly before using in production.
                  </AlertDescription>
                </Alert>

                <div className="flex gap-2">
                  <Button onClick={handleSavePrompt}>
                    <Save className="h-4 w-4 mr-2" />
                    Save Changes
                  </Button>
                  <Button variant="outline" onClick={() => onOpenChange(false)}>
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        
        {/* Delete Confirmation Dialog */}
        {showDeleteConfirm && (
          <Dialog open={!!showDeleteConfirm} onOpenChange={() => setShowDeleteConfirm(null)}>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2 text-red-600">
                  <AlertTriangle className="h-5 w-5" />
                  Remove Standard Field
                </DialogTitle>
              </DialogHeader>
              <div className="space-y-4">
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Are you sure you want to remove the <strong>{template.fields.find(f => f.id === showDeleteConfirm)?.name}</strong> field from this template?
                </p>
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    This field will no longer be extracted from documents. You can restore it later if needed.
                  </AlertDescription>
                </Alert>
                <div className="flex gap-2 justify-end">
                  <Button 
                    variant="outline" 
                    onClick={() => setShowDeleteConfirm(null)}
                    className="border-gray-300 dark:border-gray-600"
                  >
                    Cancel
                  </Button>
                  <Button 
                    variant="destructive" 
                    onClick={() => handleRemoveStandardField(showDeleteConfirm)}
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Remove Field
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </DialogContent>
    </Dialog>
  )
}