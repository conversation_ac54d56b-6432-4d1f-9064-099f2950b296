# 🎨 DL Organizer Styling Guidelines

## **CRITICAL MEMORY:** Prevent Ugly Design Patterns

This document serves as a **PERMANENT MEMORY** to prevent recurring ugly design issues in the DL Organizer application.

## ⚠️ **NEVER DO THESE THINGS:**

### ❌ **Poor Contrast Code Bubbles**
```css
/* NEVER USE */
.bg-gray-100 {
  background: light gray; /* Poor contrast */
}
```

### ❌ **Hard-to-Read Info Panels**
```css
/* NEVER USE */
.bg-blue-50.text-blue-700 {
  /* Poor contrast combination */
}
```

### ❌ **Generic Badge Colors**
```css
/* NEVER USE */
.variant-secondary
.variant-default
/* Use semantic color classes instead */
```

## ✅ **ALWAYS USE THESE PATTERNS:**

### 🎯 **High-Contrast Code Bubbles**
```css
/* Modern, legible code display */
.code-bubble {
  @apply font-mono text-sm px-3 py-1.5 rounded-md border;
  background: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border-color: hsl(var(--border));
  font-weight: 500;
}

.code-bubble-dark {
  @apply font-mono text-sm px-3 py-1.5 rounded-md;
  background: hsl(var(--foreground));
  color: hsl(var(--background));
  font-weight: 500;
}
```

### 🎨 **Semantic Badge Colors**
```css
/* Green for free/safe items */
.badge-free {
  @apply bg-green-100 text-green-800 border-green-200;
}

/* Orange for paid/warning items */
.badge-paid {
  @apply bg-orange-100 text-orange-800 border-orange-200;
}

/* Purple for premium/custom items */
.badge-premium {
  @apply bg-purple-100 text-purple-800 border-purple-200;
}
```

### 📋 **Accessible Info Panels**
```css
/* Modern info panels with proper contrast */
.info-panel {
  @apply p-4 rounded-lg border;
  background: hsl(var(--card));
  border-color: hsl(var(--border));
  color: hsl(var(--card-foreground));
}

.info-panel-blue {
  @apply bg-blue-50 border-blue-200 text-blue-900;
}

.info-panel-green {
  @apply bg-green-50 border-green-200 text-green-900;
}

.info-panel-orange {
  @apply bg-orange-50 border-orange-200 text-orange-900;
}
```

### 🏗️ **Model Cards with Modern Styling**
```css
.model-card {
  @apply p-4 border rounded-lg bg-card text-card-foreground hover:bg-accent hover:text-accent-foreground transition-colors;
  border-color: hsl(var(--border));
}

.model-card-selected {
  @apply ring-2 ring-primary ring-offset-2;
}
```

## 🔤 **Typography Standards**

### **Primary Font: Roboto**
```css
/* Always use Roboto for body text */
font-family: 'Roboto', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
```

### **Monospace Font: JetBrains Mono**
```css
/* Always use JetBrains Mono for code */
font-family: 'JetBrains Mono', ui-monospace, 'SFMono-Regular', Monaco, Consolas, monospace;
```

## 🎨 **Color Semantics**

### **Tier Color System**
- 🟢 **Green**: Free, Safe, Success
- 🟠 **Orange**: Paid, Warning, Cost
- 🟣 **Purple**: Premium, Custom, Special
- 🔵 **Blue**: Information, System, Default
- 🔴 **Red**: Error, Danger, Delete

### **Usage Examples**
```jsx
// ✅ CORRECT
<Badge className="badge-free">Free</Badge>
<Badge className="badge-paid">$0.15/1K</Badge>
<Badge className="badge-premium">Custom</Badge>

// ❌ WRONG
<Badge variant="secondary">Free</Badge>
<Badge variant="default">$0.15/1K</Badge>
<Badge variant="outline">Custom</Badge>
```

## 📦 **Component Patterns**

### **Benefits Lists**
```jsx
// ✅ Modern benefits list with proper styling
<div className="benefits-list">
  <div className="benefits-item">
    <div className="benefits-bullet"></div>
    <span>Benefit description</span>
  </div>
</div>
```

### **Model Identifier Display**
```jsx
// ✅ High-contrast model ID display
<div className="code-bubble-dark">
  {model.id}
</div>

// ❌ Poor contrast (NEVER USE)
<code className="bg-gray-100 px-1 rounded">
  {model.id}
</code>
```

## 🎯 **Interactive Elements**

### **Hover States**
```css
/* Always include smooth transitions */
.transition-colors {
  transition: background-color 0.2s ease, color 0.2s ease;
}
```

### **Focus States**
```css
/* Ensure accessible focus indicators */
.focus-visible:focus {
  @apply outline-none ring-2 ring-ring ring-offset-2;
}
```

## 📱 **Responsive Design**

### **Grid Systems**
```css
/* Use semantic grid classes */
.grid.grid-cols-1.lg:grid-cols-2.gap-6
```

### **Spacing**
```css
/* Consistent spacing scale */
.space-y-2  /* 8px */
.space-y-3  /* 12px */
.space-y-4  /* 16px */
.space-y-6  /* 24px */
```

## 🔧 **Implementation Checklist**

When creating new components, always:

- [ ] Use semantic color classes (`.badge-free`, `.badge-paid`, etc.)
- [ ] Ensure high contrast for text readability
- [ ] Use Roboto font for body text
- [ ] Use JetBrains Mono for code display
- [ ] Include proper hover and focus states
- [ ] Test in both light and dark modes
- [ ] Validate accessibility contrast ratios
- [ ] Use consistent spacing from the design system

## 🚫 **Code Review Red Flags**

If you see any of these in a PR, **REJECT IMMEDIATELY**:

- `bg-gray-100` with poor contrast
- `bg-blue-50 text-blue-700` combinations
- Generic `variant="secondary"` badges
- Missing semantic color classes
- Comic Sans or system fonts instead of Roboto
- Poor contrast ratios below 4.5:1
- Missing hover/focus states

## 📋 **Quick Reference**

| Element Type | Class Pattern | Example |
|--------------|---------------|---------|
| Code Display | `.code-bubble-dark` | Model IDs |
| Free Items | `.badge-free` | Free models |
| Paid Items | `.badge-paid` | Paid models |
| Premium Items | `.badge-premium` | Custom features |
| Info Panels | `.info-panel-blue` | Information boxes |
| Benefits Lists | `.benefits-list` | Feature lists |
| Model Cards | `.model-card` | Interactive cards |

---

## 🎯 **MEMORY ENFORCEMENT**

**This document must be referenced for ALL future UI work.**

When Claude Code encounters styling requests, it should:

1. **First** check this document for patterns
2. **Always** use the approved classes
3. **Never** create new styling without updating this guide
4. **Enforce** these patterns in code reviews

**This is not optional - this is the law of our codebase.**

---

*Last Updated: ${new Date().toISOString().split('T')[0]}*
*Version: 1.0*
*Status: ACTIVE ENFORCEMENT*