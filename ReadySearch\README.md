# ReadySearch

Professional name search automation tool for ReadySearch.com.au with intelligent batch processing and multiple user interfaces.

## 🚀 Quick Start

```bash
# Easy Launcher - Choose GUI or CLI mode
launcher.bat

# Direct CLI access (unified)
python readysearch_cli.py "<PERSON>"

# Direct GUI access
python readysearch_gui.py
```

## ✨ Key Features

- **Multiple Interfaces**: CLI, GUI, and API options
- **Intelligent Chunking**: Automatic optimization for large batches (11+ records)
- **Export Capabilities**: JSON, CSV, TXT formats with metadata
- **Session Management**: Continuous searching without restart
- **Error Recovery**: Robust error handling and retry mechanisms
- **100% Backward Compatible**: All existing functionality preserved

## 📊 Performance

| Batch Size | Tool | Duration | Improvement |
|------------|------|----------|-------------|
| 1-10 names | Enhanced CLI v2.0 | 8-80s | Optimal |
| 11-25 names | Enhanced CLI v3.0 | 45-120s | 48% faster |
| 26-50 names | Enhanced CLI v3.0 | 90-300s | 53% faster |
| 51+ names | Enhanced CLI v3.0 | 180-600s | 53% faster |

## 📋 Input Formats

```bash
# Single name
"John Smith"

# Name with birth year
"<PERSON>,1990"

# Multiple names (semicolon separated)
"John Smith;Jane Doe,1985;Bob Jones"
```

## 🔧 Installation

```bash
# Standard installation
pip install -r requirements.txt
playwright install chromium

# Enhanced installation (v3.0 features)
pip install -r requirements_enhanced.txt
```

## 📚 Documentation

For complete documentation, usage examples, troubleshooting, and advanced features, see:

**[📖 ReadySearch Complete Guide](READYSEARCH_GUIDE.md)**

## 🔗 Quick Links

- **[Complete Guide](READYSEARCH_GUIDE.md)** - Comprehensive documentation
- **[Changelog](CHANGELOG.md)** - Version history and updates
- **[License](LICENSE)** - Software license information

---

Ready to search with intelligence and style! 🔍✨