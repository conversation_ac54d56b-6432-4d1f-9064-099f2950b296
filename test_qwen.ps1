#!/usr/bin/env pwsh
# Qwen Code Test Script
# This script helps test and configure Qwen Code CLI

Write-Host "🚀 Qwen Code Setup & Test Script" -ForegroundColor Cyan
Write-Host "=================================" -ForegroundColor Cyan

# Change to the correct directory
Set-Location "C:\claude\dl-organizer"
Write-Host "📁 Working directory: $(Get-Location)" -ForegroundColor Green

# Check if .env file exists
if (Test-Path ".env") {
    Write-Host "✅ Found .env file" -ForegroundColor Green
} else {
    Write-Host "❌ No .env file found" -ForegroundColor Red
}

# Test qwen installation
Write-Host "`n🔍 Testing Qwen Code installation..." -ForegroundColor Yellow
try {
    $version = & qwen --version 2>$null
    if ($version) {
        Write-Host "✅ Qwen Code installed: v$version" -ForegroundColor Green
    } else {
        Write-Host "❌ Qwen Code not found in PATH" -ForegroundColor Red
        Write-Host "   Trying full path..." -ForegroundColor Yellow
        $version = & "C:\Users\<USER>\.mcp-modules\qwen.cmd" --version 2>$null
        if ($version) {
            Write-Host "✅ Qwen Code found at full path: v$version" -ForegroundColor Green
        }
    }
} catch {
    Write-Host "❌ Error testing Qwen Code: $_" -ForegroundColor Red
}

# Check for API keys
Write-Host "`n🔑 Checking for API keys..." -ForegroundColor Yellow
$envVars = @("OPENAI_API_KEY", "ANTHROPIC_API_KEY", "QWEN_API_KEY")
foreach ($var in $envVars) {
    $value = [Environment]::GetEnvironmentVariable($var)
    if ($value) {
        $masked = $value.Substring(0, [Math]::Min(8, $value.Length)) + "..."
        Write-Host "✅ Found ${var}: $masked" -ForegroundColor Green
    } else {
        Write-Host "❌ ${var} not set" -ForegroundColor Red
    }
}

# Explain the main issue
Write-Host "`n🐛 MAIN ISSUE EXPLANATION:" -ForegroundColor Magenta
Write-Host "=========================" -ForegroundColor Magenta
Write-Host "Your Qwen Code is failing because:" -ForegroundColor White
Write-Host "1. 🚫 OpenRouter FREE tier doesn't support tool calling" -ForegroundColor Red  
Write-Host "2. 🔧 Qwen Code REQUIRES function calling to work" -ForegroundColor Red
Write-Host "3. 💰 You need a PAID API that supports tools" -ForegroundColor Red

Write-Host "`n💡 SOLUTIONS:" -ForegroundColor Green
Write-Host "=============" -ForegroundColor Green
Write-Host "Option A: Get OpenAI API key (most reliable)" -ForegroundColor Cyan
Write-Host "  • Go to: https://platform.openai.com/" -ForegroundColor White
Write-Host "  • Add: OPENAI_API_KEY=sk-your-key to .env" -ForegroundColor White

Write-Host "`nOption B: Get Alibaba Cloud Qwen API key" -ForegroundColor Cyan  
Write-Host "  • Go to: https://modelstudio.console.alibabacloud.com/" -ForegroundColor White
Write-Host "  • Add: QWEN_API_KEY=your-key to .env" -ForegroundColor White

Write-Host "`nOption C: Use Claude Desktop (you're already here!)" -ForegroundColor Cyan
Write-Host "  • I can help you with coding tasks directly" -ForegroundColor White
Write-Host "  • No additional setup required" -ForegroundColor White
Write-Host "  • Full shell access via desktop-commander" -ForegroundColor White

# Try a simple test if API key is available
if ($env:OPENAI_API_KEY) {
    Write-Host "`n🧪 Testing Qwen Code with OpenAI..." -ForegroundColor Yellow
    try {
        & qwen --prompt "Just say 'Hello from Qwen!' and nothing else" --model "gpt-3.5-turbo"
    } catch {
        Write-Host "❌ Test failed: $_" -ForegroundColor Red
    }
}

Write-Host "`n📝 NEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. Get a proper API key from OpenAI or Alibaba Cloud" -ForegroundColor White
Write-Host "2. Add it to your .env file" -ForegroundColor White  
Write-Host "3. Test with: qwen --prompt 'Hello test'" -ForegroundColor White
Write-Host "4. Or just use me (Claude Desktop) for your coding needs! 🤖" -ForegroundColor Green

Write-Host "`nWhat would you like to work on? I can help you code right now! 💻" -ForegroundColor Magenta
