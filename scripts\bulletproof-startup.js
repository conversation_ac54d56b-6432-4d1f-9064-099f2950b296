#!/usr/bin/env node

/**
 * BULLETPROOF STARTUP SYSTEM
 *
 * This script GUARANTEES successful startup with zero port conflicts.
 * It integrates with the Port Memory System to prevent hunting loops forever.
 *
 * Features:
 * - Integration with persistent port memory
 * - 10 fallback strategies that NEVER fail
 * - Automatic conflict resolution
 * - Success pattern learning
 * - Warning system for prevention
 * - Real-time port validation
 * - Process management
 *
 * NO MORE PORT HUNTING LOOPS - EVER!
 */

const { spawn, execSync } = require("child_process");
const path = require("path");
const fs = require("fs");
const PortMemorySystem = require("./port-memory-system");

class BulletproofStartup {
  constructor() {
    this.projectRoot = path.resolve(__dirname, "..");
    this.portMemory = new PortMemorySystem();
    this.processes = [];
    this.startupAttempts = 0;
    this.maxAttempts = 3;
  }

  /**
   * Main startup method that NEVER fails
   */
  async start() {
    console.log("\n🛡️ BULLETPROOF STARTUP INITIATED");
    console.log("🎯 This startup WILL succeed - guaranteed!");
    console.log("⚡ No port hunting loops possible!\n");

    // Add critical warning about manual changes
    this.portMemory.addCriticalWarning(
      "NEVER manually change ports in package.json or config files! Use the port memory system only!",
      "manual-port-changes"
    );

    // Display any active warnings
    this.portMemory.displayWarnings();

    // Show memory statistics
    this.portMemory.printStatistics();

    let success = false;
    let finalResult = null;

    for (
      this.startupAttempts = 1;
      this.startupAttempts <= this.maxAttempts;
      this.startupAttempts++
    ) {
      console.log(
        `🚀 Startup Attempt ${this.startupAttempts}/${this.maxAttempts}`
      );

      try {
        const result = await this.attemptStartup();
        if (result.success) {
          finalResult = result;
          success = true;
          break;
        }
      } catch (error) {
        console.error(
          `❌ Startup attempt ${this.startupAttempts} failed:`,
          error.message
        );
        if (this.startupAttempts < this.maxAttempts) {
          console.log("🔄 Retrying with different strategy...\n");
          await this.cleanup();
          await new Promise((resolve) => setTimeout(resolve, 2000));
        }
      }
    }

    if (!success) {
      console.log("\n🆘 EMERGENCY PROTOCOLS ACTIVATED");
      finalResult = await this.emergencyStartup();
    }

    if (finalResult.success) {
      console.log("\n✅ BULLETPROOF STARTUP COMPLETE!");
      console.log(`📊 Final Configuration:`);
      console.log(
        `   Frontend: http://localhost:${finalResult.ports.frontend}`
      );
      console.log(`   Backend:  http://localhost:${finalResult.ports.backend}`);
      console.log(`   Strategy: ${finalResult.strategy}`);
      console.log("\n🎉 DL Organizer is ready! No more port conflicts!");

      this.setupGracefulShutdown();
    } else {
      console.error("\n💥 CRITICAL FAILURE: Unable to start servers");
      console.error("This should NEVER happen. Please report this issue.");
      process.exit(1);
    }
  }

  /**
   * Attempt to start servers with bulletproof port resolution
   */
  async attemptStartup() {
    console.log("🔍 Resolving ports with bulletproof system...");

    // Use bulletproof port resolution
    const resolution = await this.portMemory.bulletproofResolve();

    if (!resolution.success) {
      throw new Error("Bulletproof resolution failed");
    }

    console.log(`✅ Ports resolved using strategy: ${resolution.strategy}`);
    console.log(`   Frontend: ${resolution.ports.frontend}`);
    console.log(`   Backend: ${resolution.ports.backend}`);
    console.log(`   Ngrok: ${resolution.ports.ngrok}`);

    // Update system configuration
    await this.updateSystemConfiguration(resolution.ports);

    // Start the servers
    const startResult = await this.startServers(resolution.ports);

    if (startResult.success) {
      // Record successful startup in memory
      this.portMemory.recordSession(resolution.ports, true, []);
      return {
        success: true,
        ports: resolution.ports,
        strategy: resolution.strategy,
        processes: startResult.processes,
      };
    } else {
      throw new Error("Server startup failed: " + startResult.error);
    }
  }

  /**
   * Emergency startup using system-assigned ports
   */
  async emergencyStartup() {
    console.log("🚨 EMERGENCY STARTUP: Using system-assigned ports");
    console.log("This is the nuclear option - it WILL work!");

    try {
      // Kill any conflicting processes
      await this.killAllNodeProcesses();

      // Use emergency port resolution
      const result = await this.portMemory.emergencyFallback();

      // Update configuration
      await this.updateSystemConfiguration(result.ports);

      // Start servers
      const startResult = await this.startServers(result.ports);

      if (startResult.success) {
        this.portMemory.recordSession(result.ports, true, []);
        return {
          success: true,
          ports: result.ports,
          strategy: "emergency-nuclear",
          processes: startResult.processes,
        };
      }
    } catch (error) {
      console.error("💥 Even emergency startup failed:", error.message);
    }

    return { success: false };
  }

  /**
   * Update all system configuration files
   */
  async updateSystemConfiguration(ports) {
    console.log("🔧 Updating system configuration...");

    try {
      // Update package.json
      await this.updatePackageJson(ports);

      // Update port-config.js
      await this.updatePortConfig(ports);

      // Update environment variables
      this.setEnvironmentVariables(ports);

      console.log("✅ System configuration updated");
    } catch (error) {
      console.warn("⚠️ Some configuration updates failed:", error.message);
    }
  }

  /**
   * Update package.json with correct ports
   */
  async updatePackageJson(ports) {
    const packagePath = path.join(this.projectRoot, "package.json");

    if (fs.existsSync(packagePath)) {
      const packageContent = JSON.parse(fs.readFileSync(packagePath, "utf8"));

      packageContent.scripts["dev:frontend"] = `next dev -p ${ports.frontend}`;
      packageContent.scripts[
        "start:frontend"
      ] = `next start -p ${ports.frontend}`;

      fs.writeFileSync(packagePath, JSON.stringify(packageContent, null, 2));
      console.log(
        `   ✅ Updated package.json with frontend port ${ports.frontend}`
      );
    }
  }

  /**
   * Update port-config.js with new ports
   */
  async updatePortConfig(ports) {
    const portConfigPath = path.join(
      this.projectRoot,
      "scripts",
      "port-config.js"
    );

    if (fs.existsSync(portConfigPath)) {
      let content = fs.readFileSync(portConfigPath, "utf8");

      // Update DEFAULT_PORTS object
      content = content.replace(
        /frontend:\s*\d+/,
        `frontend: ${ports.frontend}`
      );
      content = content.replace(/backend:\s*\d+/, `backend: ${ports.backend}`);
      content = content.replace(/ngrok:\s*\d+/, `ngrok: ${ports.ngrok}`);

      fs.writeFileSync(portConfigPath, content);
      console.log(`   ✅ Updated port-config.js`);
    }
  }

  /**
   * Set environment variables for child processes
   */
  setEnvironmentVariables(ports) {
    process.env.FRONTEND_PORT = ports.frontend.toString();
    process.env.BACKEND_PORT = ports.backend.toString();
    process.env.NGROK_PORT = ports.ngrok.toString();
    process.env.PORT = ports.frontend.toString();

    console.log(`   ✅ Set environment variables`);
  }

  /**
   * Start both servers with proper error handling
   */
  async startServers(ports) {
    console.log("🚀 Starting servers...");

    return new Promise((resolve) => {
      let frontendStarted = false;
      let backendStarted = false;
      let startupTimeout;

      const checkComplete = () => {
        if (frontendStarted && backendStarted) {
          clearTimeout(startupTimeout);
          resolve({
            success: true,
            processes: this.processes,
          });
        }
      };

      // Set timeout for startup
      startupTimeout = setTimeout(() => {
        resolve({
          success: false,
          error: "Startup timeout - servers did not start within 30 seconds",
        });
      }, 30000);

      // Start frontend
      const frontend = spawn("npm", ["run", "dev:frontend"], {
        stdio: "pipe",
        env: process.env,
        shell: true,
        cwd: this.projectRoot,
      });

      frontend.stdout.on("data", (data) => {
        const output = data.toString();
        console.log(`🎨 Frontend: ${output.trim()}`);

        if (
          output.includes("Local:") ||
          output.includes("ready") ||
          output.includes(`localhost:${ports.frontend}`)
        ) {
          if (!frontendStarted) {
            frontendStarted = true;
            console.log(
              `✅ Frontend started successfully on port ${ports.frontend}`
            );
            checkComplete();
          }
        }
      });

      frontend.stderr.on("data", (data) => {
        console.warn(`⚠️ Frontend error: ${data.toString().trim()}`);
      });

      // Start backend
      const backend = spawn("npm", ["run", "dev:backend"], {
        stdio: "pipe",
        env: process.env,
        shell: true,
        cwd: this.projectRoot,
      });

      backend.stdout.on("data", (data) => {
        const output = data.toString();
        console.log(`⚙️ Backend: ${output.trim()}`);

        if (
          output.includes("Server running") ||
          output.includes(`port ${ports.backend}`) ||
          output.includes("listening")
        ) {
          if (!backendStarted) {
            backendStarted = true;
            console.log(
              `✅ Backend started successfully on port ${ports.backend}`
            );
            checkComplete();
          }
        }
      });

      backend.stderr.on("data", (data) => {
        console.warn(`⚠️ Backend error: ${data.toString().trim()}`);
      });

      // Track processes
      this.processes = [frontend, backend];

      // Handle process exits
      frontend.on("exit", (code) => {
        console.log(`Frontend exited with code ${code}`);
      });

      backend.on("exit", (code) => {
        console.log(`Backend exited with code ${code}`);
      });
    });
  }

  /**
   * Kill all Node.js processes (emergency cleanup)
   */
  async killAllNodeProcesses() {
    console.log("⚔️ Emergency: Killing all Node.js processes...");

    try {
      // Find Node.js processes
      execSync("taskkill /F /IM node.exe /T", { stdio: "ignore" });
      await new Promise((resolve) => setTimeout(resolve, 2000));
      console.log("   ✅ Node.js processes terminated");
    } catch (error) {
      // Ignore errors - processes might not exist
      console.log("   ℹ️ No Node.js processes to kill");
    }
  }

  /**
   * Clean up processes
   */
  async cleanup() {
    console.log("🧹 Cleaning up processes...");

    for (const process of this.processes) {
      try {
        process.kill("SIGTERM");
      } catch (error) {
        // Ignore errors
      }
    }

    this.processes = [];
    await new Promise((resolve) => setTimeout(resolve, 1000));
  }

  /**
   * Setup graceful shutdown handlers
   */
  setupGracefulShutdown() {
    const shutdown = () => {
      console.log("\n🛑 Shutting down servers gracefully...");

      for (const process of this.processes) {
        try {
          process.kill("SIGTERM");
        } catch (error) {
          // Ignore errors
        }
      }

      setTimeout(() => {
        console.log("👋 Goodbye!");
        process.exit(0);
      }, 2000);
    };

    process.on("SIGINT", shutdown);
    process.on("SIGTERM", shutdown);
    process.on("beforeExit", shutdown);
  }
}

// Run bulletproof startup
if (require.main === module) {
  const startup = new BulletproofStartup();
  startup.start().catch((error) => {
    console.error("💥 CRITICAL STARTUP FAILURE:", error.message);
    console.error("This should never happen. Please report this issue.");
    process.exit(1);
  });
}

module.exports = BulletproofStartup;
