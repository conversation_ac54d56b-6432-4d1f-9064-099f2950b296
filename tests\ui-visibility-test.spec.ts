import { test, expect } from '@playwright/test';

test.describe('UI Element Visibility and Layout Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3001');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(2000);
  });

  test('should test all UI elements visibility in light mode', async ({ page }) => {
    console.log('Testing UI elements in light mode...');
    
    // Ensure we're in light mode
    await page.evaluate(() => {
      document.documentElement.classList.remove('dark');
    });
    await page.waitForTimeout(500);

    // Test main navigation elements
    const mainTitle = page.locator('h1');
    await expect(mainTitle).toBeVisible();
    
    const newProjectButton = page.locator('button:has-text("New Project")');
    await expect(newProjectButton).toBeVisible();
    
    const themeToggle = page.locator('button[role="switch"]');
    await expect(themeToggle).toBeVisible();

    // Test project cards if they exist
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    console.log(`Found ${cardCount} project cards`);
    
    for (let i = 0; i < Math.min(cardCount, 3); i++) {
      const card = projectCards.nth(i);
      await expect(card).toBeVisible();
      
      // Check for text overflow
      const cardText = card.locator('p, h3');
      const textCount = await cardText.count();
      for (let j = 0; j < textCount; j++) {
        const text = cardText.nth(j);
        const boundingBox = await text.boundingBox();
        const parentBox = await card.boundingBox();
        
        if (boundingBox && parentBox) {
          // Text should not extend beyond card boundaries
          expect(boundingBox.x + boundingBox.width).toBeLessThanOrEqual(parentBox.x + parentBox.width + 5); // 5px tolerance
        }
      }
    }
    
    console.log('Light mode UI visibility test completed');
  });

  test('should test all UI elements visibility in dark mode', async ({ page }) => {
    console.log('Testing UI elements in dark mode...');
    
    // Switch to dark mode
    await page.evaluate(() => {
      document.documentElement.classList.add('dark');
    });
    await page.waitForTimeout(500);

    // Test the same elements in dark mode
    const mainTitle = page.locator('h1');
    await expect(mainTitle).toBeVisible();
    
    const newProjectButton = page.locator('button:has-text("New Project")');
    await expect(newProjectButton).toBeVisible();
    
    // Check contrast and visibility
    const backgroundColor = await page.evaluate(() => {
      return window.getComputedStyle(document.body).backgroundColor;
    });
    
    console.log(`Dark mode background color: ${backgroundColor}`);
    
    // Test dropdown visibility in dark mode
    const settingsButton = page.locator('button:has-text("Settings")');
    if (await settingsButton.isVisible()) {
      await settingsButton.click();
      await page.waitForTimeout(500);
      
      const dialog = page.locator('[role="dialog"]');
      await expect(dialog).toBeVisible();
      
      // Test dropdown in settings
      const ocrModeSelect = page.locator('text="OCR Processing Mode"').locator('..');
      if (await ocrModeSelect.isVisible()) {
        const selectTrigger = ocrModeSelect.locator('[role="combobox"]');
        if (await selectTrigger.isVisible()) {
          await selectTrigger.click();
          await page.waitForTimeout(300);
          
          const selectContent = page.locator('[role="listbox"]');
          await expect(selectContent).toBeVisible();
          
          // Check z-index and visibility
          const zIndex = await selectContent.evaluate((el) => {
            return window.getComputedStyle(el).zIndex;
          });
          console.log(`Dropdown z-index in dark mode: ${zIndex}`);
          expect(parseInt(zIndex)).toBeGreaterThan(50);
        }
      }
      
      // Close dialog
      const closeButton = page.locator('button:has-text("Close")');
      if (await closeButton.isVisible()) {
        await closeButton.click();
      }
    }
    
    console.log('Dark mode UI visibility test completed');
  });

  test('should test settings dropdown visibility and positioning', async ({ page }) => {
    console.log('Testing settings dropdown visibility...');
    
    const settingsButton = page.locator('button:has-text("Settings")');
    if (await settingsButton.isVisible()) {
      await settingsButton.click();
      await page.waitForTimeout(500);
      
      // Test model dropdown
      const modelSelect = page.locator('text="Vision Model"').locator('..');
      if (await modelSelect.isVisible()) {
        const selectTrigger = modelSelect.locator('[role="combobox"]');
        if (await selectTrigger.isVisible()) {
          await selectTrigger.click();
          await page.waitForTimeout(300);
          
          const selectContent = page.locator('[role="listbox"]');
          await expect(selectContent).toBeVisible();
          
          // Check if dropdown content is positioned correctly
          const triggerBox = await selectTrigger.boundingBox();
          const contentBox = await selectContent.boundingBox();
          
          if (triggerBox && contentBox) {
            // Dropdown should be positioned near the trigger
            const verticalDistance = Math.abs(contentBox.y - (triggerBox.y + triggerBox.height));
            expect(verticalDistance).toBeLessThan(50); // Should be close vertically
            
            console.log(`Dropdown positioning: trigger at y=${triggerBox.y}, content at y=${contentBox.y}`);
          }
          
          // Click away to close dropdown
          await page.click('body');
        }
      }
      
      // Test OCR mode dropdown
      const ocrModeSelect = page.locator('text="OCR Processing Mode"').locator('..');
      if (await ocrModeSelect.isVisible()) {
        const selectTrigger = ocrModeSelect.locator('[role="combobox"]');
        if (await selectTrigger.isVisible()) {
          await selectTrigger.click();
          await page.waitForTimeout(300);
          
          const selectContent = page.locator('[role="listbox"]');
          await expect(selectContent).toBeVisible();
          
          // Test selecting different modes
          const usOption = page.locator('text="US Driver License / ID"');
          const australianOption = page.locator('text="Australian Driver License"');
          
          if (await usOption.isVisible()) {
            await usOption.click();
            await page.waitForTimeout(500);
            
            // Check if US features description is visible
            const usFeatures = page.locator('text="US Mode Features:"');
            await expect(usFeatures).toBeVisible();
            
            // Switch to Australian mode
            await selectTrigger.click();
            await page.waitForTimeout(300);
            
            if (await australianOption.isVisible()) {
              await australianOption.click();
              await page.waitForTimeout(500);
              
              // Check if Australian features description is visible
              const australianFeatures = page.locator('text="Australian Mode Features:"');
              await expect(australianFeatures).toBeVisible();
            }
          }
        }
      }
      
      // Close settings
      const closeButton = page.locator('button:has-text("Close")');
      if (await closeButton.isVisible()) {
        await closeButton.click();
      }
    }
    
    console.log('Settings dropdown visibility test completed');
  });

  test('should test responsive design and element wrapping', async ({ page }) => {
    console.log('Testing responsive design...');
    
    // Test different viewport sizes
    const viewports = [
      { width: 1920, height: 1080, name: 'Desktop Large' },
      { width: 1366, height: 768, name: 'Desktop Medium' },
      { width: 1024, height: 768, name: 'Desktop Small' },
      { width: 768, height: 1024, name: 'Tablet' },
      { width: 375, height: 667, name: 'Mobile' }
    ];
    
    for (const viewport of viewports) {
      console.log(`Testing ${viewport.name} (${viewport.width}x${viewport.height})`);
      
      await page.setViewportSize({ width: viewport.width, height: viewport.height });
      await page.waitForTimeout(500);
      
      // Check if main elements are still visible and properly positioned
      const mainTitle = page.locator('h1');
      if (await mainTitle.isVisible()) {
        const titleBox = await mainTitle.boundingBox();
        if (titleBox) {
          // Title should not extend beyond viewport
          expect(titleBox.x + titleBox.width).toBeLessThanOrEqual(viewport.width);
        }
      }
      
      // Check project cards layout
      const projectCards = page.locator('[data-testid="project-card"]');
      const cardCount = await projectCards.count();
      
      for (let i = 0; i < Math.min(cardCount, 2); i++) {
        const card = projectCards.nth(i);
        if (await card.isVisible()) {
          const cardBox = await card.boundingBox();
          if (cardBox) {
            // Card should not extend beyond viewport
            expect(cardBox.x + cardBox.width).toBeLessThanOrEqual(viewport.width + 10); // 10px tolerance for scrollbars
          }
        }
      }
    }
    
    // Reset to standard viewport
    await page.setViewportSize({ width: 1366, height: 768 });
    
    console.log('Responsive design test completed');
  });

  test('should test text truncation and overflow handling', async ({ page }) => {
    console.log('Testing text truncation and overflow...');
    
    // Look for elements that might have long text
    const textElements = page.locator('p, span, h1, h2, h3, h4, h5, h6');
    const elementCount = await textElements.count();
    
    for (let i = 0; i < Math.min(elementCount, 20); i++) {
      const element = textElements.nth(i);
      
      if (await element.isVisible()) {
        // Check if element has proper overflow handling
        const styles = await element.evaluate((el) => {
          const computedStyle = window.getComputedStyle(el);
          return {
            overflow: computedStyle.overflow,
            textOverflow: computedStyle.textOverflow,
            whiteSpace: computedStyle.whiteSpace,
            wordWrap: computedStyle.wordWrap,
            maxWidth: computedStyle.maxWidth,
            width: computedStyle.width
          };
        });
        
        const text = await element.textContent();
        if (text && text.length > 50) {
          console.log(`Long text element: "${text.substring(0, 50)}..." - Styles:`, styles);
          
          // Check if element has proper overflow handling for long text
          const hasOverflowHandling = 
            styles.overflow === 'hidden' ||
            styles.textOverflow === 'ellipsis' ||
            styles.wordWrap === 'break-word' ||
            styles.whiteSpace === 'nowrap';
            
          if (!hasOverflowHandling && text.length > 100) {
            console.warn(`Element with long text may need overflow handling: "${text.substring(0, 30)}..."`);
          }
        }
      }
    }
    
    console.log('Text truncation test completed');
  });
});