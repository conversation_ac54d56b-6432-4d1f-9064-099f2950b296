const express = require('express');
const path = require('path');
const { SmartAnalyzer } = require('../services/smart-analyzer');
const { getImagesRecursive } = require('./filesystem');
const DatabaseManager = require('../config/database');

// Initialize database manager
const dbManager = new DatabaseManager();

// Security: Define base allowed root paths to prevent directory traversal
const BASE_ALLOWED_ROOTS = [
  path.resolve(__dirname, '../../data'),
  path.resolve(__dirname, '../../uploads'),
  'C:\\', // Windows drive roots for DL Organizer
];

// Get allowed roots including user project paths
async function getAllowedRoots() {
  try {
    // Initialize database connection if not already done
    if (!dbManager.isInitialized) {
      await dbManager.initialize();
    }
    
    const projects = await dbManager.all('SELECT root_path FROM projects');
    const projectRoots = projects.map(p => path.normalize(p.root_path));
    
    // Also allow the current folder path if it's being analyzed
    // This ensures that any folder opened in the UI is automatically allowed
    const allRoots = [...BASE_ALLOWED_ROOTS, ...projectRoots];
    
    console.log('Smart Analyzer: Allowed roots:', allRoots);
    return allRoots;
  } catch (error) {
    console.warn('Smart Analyzer: Could not fetch project paths, using base allowed roots only:', error.message);
    return BASE_ALLOWED_ROOTS;
  }
}

// Rate limiting: Simple in-memory rate limiter to prevent DoS
const rateLimitMap = new Map();
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 5; // Max 5 analysis requests per minute per IP

function checkRateLimit(req, res, next) {
  const clientIP = req.ip || req.connection.remoteAddress || '127.0.0.1';
  const now = Date.now();
  const windowStart = now - RATE_LIMIT_WINDOW;
  
  // Clean old entries
  if (rateLimitMap.has(clientIP)) {
    const requests = rateLimitMap.get(clientIP).filter(timestamp => timestamp > windowStart);
    rateLimitMap.set(clientIP, requests);
  }
  
  const requests = rateLimitMap.get(clientIP) || [];
  
  if (requests.length >= RATE_LIMIT_MAX_REQUESTS) {
    return res.status(429).json({ 
      error: 'Rate limit exceeded', 
      details: `Maximum ${RATE_LIMIT_MAX_REQUESTS} requests per minute allowed` 
    });
  }
  
  // Add current request
  requests.push(now);
  rateLimitMap.set(clientIP, requests);
  
  next();
}

const router = express.Router({ mergeParams: false });

// Initialize smart analyzer service
let smartAnalyzer = null;

const initializeSmartAnalyzer = () => {
  if (!smartAnalyzer) {
    const OCRService = require('../services/ocr-service');
    const ocrService = new OCRService();
    smartAnalyzer = new SmartAnalyzer(ocrService);
  }
  return smartAnalyzer;
};

/**
 * POST /api/smart-analyzer/analyze
 * body: { folderPath: string, forceRefresh?: boolean }
 * returns: { jobId: string, totalImages: number }
 */
router.post('/analyze', checkRateLimit, async (req, res) => {
  console.log('🔍 Smart Analyzer: Received analyze request');
  console.log('🔍 Smart Analyzer: Request body:', JSON.stringify(req.body, null, 2));
  
  const { folderPath, forceRefresh = false } = req.body || {};
  
  if (!folderPath) {
    console.error('❌ Smart Analyzer: No folderPath provided');
    return res.status(400).json({ error: 'folderPath required' });
  }
  
  console.log(`🔍 Smart Analyzer: Processing folderPath: "${folderPath}"`);
  console.log(`🔍 Smart Analyzer: forceRefresh: ${forceRefresh}`);

  // Security: Validate folder path to prevent directory traversal
  if (!path.isAbsolute(folderPath)) {
    return res.status(400).json({ error: 'Invalid folderPath: must be absolute' });
  }
  
  const normalizedPath = path.normalize(folderPath);
  const allowedRoots = await getAllowedRoots();
  
  // Check if the path is within allowed roots
  let isAllowed = allowedRoots.some(root => {
    const normalizedRoot = path.normalize(root);
    return normalizedPath === normalizedRoot || normalizedPath.startsWith(normalizedRoot + path.sep);
  });
  
  // If not allowed, but it's a valid Windows path and not a system directory,
  // auto-allow it and suggest creating a project for it
  if (!isAllowed && process.platform === 'win32') {
    const originalPath = folderPath; // Keep original for checking
    const isSystemPath = normalizedPath.toLowerCase().includes('windows') || 
                        normalizedPath.toLowerCase().includes('program files') ||
                        normalizedPath.toLowerCase().includes('system32');
    
    // Check for Windows drive path with either forward or backslashes
    const isWindowsPath = originalPath.match(/^[A-Z]:[\/\\].*$/) || normalizedPath.match(/^[A-Z]:[\/\\].*$/);
    
    if (!isSystemPath && isWindowsPath) {
      console.log('Smart Analyzer: Auto-allowing non-system Windows path:', normalizedPath);
      isAllowed = true;
    }
  }
  
  if (!isAllowed) {
    console.warn('Smart Analyzer: Access denied for path:', normalizedPath);
    console.warn('Smart Analyzer: Allowed roots:', allowedRoots);
    return res.status(403).json({ 
      error: 'Access denied: folderPath outside allowed directories',
      details: 'Path must be within a project root directory or create a new project first'
    });
  }

  try {
    const analyzer = initializeSmartAnalyzer();
    
    // Get images from the folder
    const { images } = await getImagesRecursive(folderPath);
    
    if (!images || images.length === 0) {
      return res.status(400).json({ error: 'No images found in folder' });
    }

    // Start analysis job
    console.log(`🚀 Smart Analyzer: Creating analysis job for ${images.length} images`);
    const job = await analyzer.createAnalysisJob({
      images,
      folderPath,
      forceRefresh
    });

    console.log(`✅ Smart Analyzer: Job ${job.id} created successfully, status: ${job.status}`);

    // Return job ID for client to connect to SSE stream
    res.json({
      jobId: job.id,
      totalImages: images.length,
      status: 'started'
    });

  } catch (error) {
    console.error('Smart analyzer error:', error);
    res.status(500).json({ 
      error: 'Failed to start analysis', 
      details: error.message 
    });
  }
});

/**
 * GET /api/smart-analyzer/stream/:jobId
 * SSE stream for job progress updates
 */
router.get('/stream/:jobId', (req, res) => {
  const { jobId } = req.params;
  
  console.log(`🔌 Smart Analyzer: SSE connection request for job ${jobId}`);
  
  if (!jobId) {
    console.error('❌ Smart Analyzer: No jobId provided for SSE stream');
    return res.status(400).json({ error: 'jobId required' });
  }

  const analyzer = initializeSmartAnalyzer();
  const job = analyzer.getJob(jobId);
  
  if (!job) {
    console.error(`❌ Smart Analyzer: Job ${jobId} not found for SSE stream`);
    return res.status(404).json({ error: 'Job not found' });
  }
  
  console.log(`✅ Smart Analyzer: Job ${jobId} found, status: ${job.status}, setting up SSE stream`);

  // Set up SSE headers
  res.set({
    'Content-Type': 'text/event-stream',
    'Connection': 'keep-alive',
    'Cache-Control': 'no-cache',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control'
  });

  // Send initial status
  res.write(`data:${JSON.stringify({ 
    jobId: job.id, 
    status: job.status, 
    progress: job.progress,
    totalImages: job.totalImages,
    processedImages: job.processedImages
  })}\n\n`);

  // If job is already complete, send final result and close
  if (job.status === 'completed' && job.manifest) {
    res.write(`data:${JSON.stringify({
      jobId: job.id,
      done: true,
      status: 'completed',
      manifest: job.manifest
    })}\n\n`);
    res.end();
    return;
  }

  // If job failed, send error and close
  if (job.status === 'failed') {
    res.write(`data:${JSON.stringify({
      jobId: job.id,
      done: true,
      status: 'failed',
      error: job.error
    })}\n\n`);
    res.end();
    return;
  }

  // Listen for progress updates
  const progressListener = (data) => {
    console.log(`📡 Smart Analyzer: Progress event received for job ${jobId}:`, data);
    if (data.jobId === jobId) {
      console.log(`📤 Smart Analyzer: Sending SSE data for job ${jobId}:`, data);
      res.write(`data:${JSON.stringify(data)}\n\n`);
      
      // Close connection when done
      if (data.done || data.error) {
        console.log(`🏁 Smart Analyzer: Closing SSE connection for job ${jobId}`);
        cleanup();
        res.end();
      }
    }
  };

  // Defensive cleanup to prevent memory leaks
  const cleanup = () => {
    analyzer.removeListener('progress', progressListener);
  };

  analyzer.on('progress', progressListener);

  // Handle client disconnect and errors
  req.on('close', cleanup);
  req.on('error', cleanup);
});

/**
 * POST /api/smart-analyzer/reset
 * Force reset processing state (for debugging)
 */
router.post('/reset', (req, res) => {
  try {
    const analyzer = initializeSmartAnalyzer();
    
    console.log('🔄 Smart Analyzer: Manual reset requested');
    
    // Force reset processing state
    analyzer.isProcessing = false;
    analyzer.activeJobs = 0;
    analyzer.lastProcessingTime = null;
    analyzer.queue = [];
    
    // Mark all processing jobs as failed
    let resetJobs = 0;
    for (const [jobId, job] of analyzer.jobs.entries()) {
      if (job.status === 'processing' || job.status === 'pending') {
        job.status = 'failed';
        job.error = 'Manual reset - job was cancelled';
        job.endTime = new Date().toISOString();
        resetJobs++;
        
        analyzer.emit('progress', {
          jobId: job.id,
          done: true,
          status: 'failed',
          error: job.error
        });
      }
    }
    
    res.json({ 
      success: true, 
      message: `Processing state reset, ${resetJobs} jobs cancelled`,
      resetJobs
    });
    
  } catch (error) {
    console.error('Smart analyzer reset error:', error);
    res.status(500).json({ error: 'Failed to reset', details: error.message });
  }
});

/**
 * GET /api/smart-analyzer/status/:jobId
 * Get current status of an analysis job
 */
router.get('/status/:jobId', (req, res) => {
  try {
    const analyzer = initializeSmartAnalyzer();
    const job = analyzer.getJob(req.params.jobId);
    
    if (!job) {
      return res.status(404).json({ error: 'Job not found' });
    }
    
    res.json({
      id: job.id,
      status: job.status,
      progress: job.progress,
      totalImages: job.totalImages,
      processedImages: job.processedImages,
      manifest: job.manifest,
      error: job.error
    });
  } catch (error) {
    console.error('Status check error:', error);
    res.status(500).json({ error: 'Failed to get job status' });
  }
});

/**
 * DELETE /api/smart-analyzer/cancel/:jobId
 * Cancel a running analysis job
 */
router.delete('/cancel/:jobId', async (req, res) => {
  try {
    const analyzer = initializeSmartAnalyzer();
    const result = await analyzer.cancelJob(req.params.jobId);
    res.json(result);
  } catch (error) {
    console.error('Cancel job error:', error);
    res.status(500).json({ error: 'Failed to cancel job' });
  }
});

module.exports = router;