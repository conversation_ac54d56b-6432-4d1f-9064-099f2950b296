@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&family=JetBrains+Mono:ital,wght@0,400;0,500;0,700;1,400;1,500;1,700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
  }

  .gray {
    --background: 220 13% 13%;
    --foreground: 220 9% 92%;
    --card: 220 13% 18%;
    --card-foreground: 220 9% 95%;
    --popover: 220 13% 18%;
    --popover-foreground: 220 9% 95%;
    --primary: 220 9% 80%;
    --primary-foreground: 220 13% 13%;
    --secondary: 220 13% 23%;
    --secondary-foreground: 220 9% 92%;
    --muted: 220 13% 23%;
    --muted-foreground: 220 9% 72%;
    --accent: 220 13% 25%;
    --accent-foreground: 220 9% 95%;
    --destructive: 0 62.8% 50%;
    --destructive-foreground: 220 9% 95%;
    --border: 220 13% 28%;
    --input: 220 13% 28%;
    --ring: 220 9% 70%;
    --radius: 0.5rem;
  }
}

/* Enhanced hover states for all themes */
.hover-enhanced {
  transition: all 0.2s ease-in-out;
}

.hover-enhanced:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px hsl(var(--foreground) / 0.15);
}

.dark .hover-enhanced:hover {
  box-shadow: 0 4px 12px hsl(var(--foreground) / 0.25);
}

.gray .hover-enhanced:hover {
  box-shadow: 0 4px 12px hsl(var(--foreground) / 0.20);
}

/* Image grid specific hover fixes */
.image-grid-item {
  transition: all 0.2s ease-in-out;
}

.image-grid-item:hover {
  transform: scale(1.02) translateZ(0);
  box-shadow: 0 4px 12px hsl(var(--foreground) / 0.15);
}

/* Consistent theme-aware hover states */
.hover\:bg-muted:hover {
  background-color: hsl(var(--muted));
}

.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}

.hover\:text-foreground:hover {
  color: hsl(var(--foreground));
}

/* Fix button hover states across all themes */
.btn-hover:hover {
  background-color: hsl(var(--primary));
  color: hsl(var(--primary-foreground));
}

/* Card hover states */
.card-hover:hover {
  background-color: hsl(var(--card));
  border-color: hsl(var(--border));
  box-shadow: 0 4px 12px hsl(var(--foreground) / 0.1);
}

.dark .image-grid-item:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
}

.gray .image-grid-item:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.5);
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground antialiased;
    font-family: 'Roboto', ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif;
    font-weight: 400;
    letter-spacing: 0.02em;
    line-height: 1.6;
    
    /* Global 20% size reduction - base font size */
    font-size: 0.8rem; /* Reduce from default 1rem (16px) to 0.8rem (12.8px) */
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-medium tracking-tight;
    /* Scale heading sizes down by 20% */
    font-size: calc(var(--font-size) * 0.8);
  }
  
  button {
    @apply font-medium;
    /* Scale button text down by 20% */
    font-size: 0.8em;
  }
  
  /* Improve card contrast */
  .card {
    @apply shadow-sm;
  }
  
  /* Ensure proper contrast for all text */
  .text-muted-foreground {
    @apply opacity-90;
  }
  
  /* ReadySearch Panel Styling - Maximum Contrast Text */
  .readysearch-panel {
    @apply bg-card border border-border;
    color: hsl(var(--foreground)) !important;
  }
  
  /* High-contrast base text color for ReadySearch, while still allowing utility text-color classes (e.g. text-red-600) to win */
  .readysearch-panel {
    color: hsl(var(--foreground));
  }

  /* Preserve the intended colours of utility classes by NOT overriding their colour */
  .readysearch-panel button {
    color: inherit;
  }
  
  /* ReadySearch specific button styling */
  .readysearch-panel .btn-outline {
    @apply bg-background hover:bg-accent hover:text-accent-foreground;
  }
  
  /* JSON output area styling */
  .readysearch-panel pre {
    @apply font-mono text-sm leading-relaxed;
    word-wrap: break-word;
    white-space: pre-wrap;
  }
  
  /* Scrollbar styling for JSON output */
  .readysearch-panel .scroll-area {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--border)) transparent;
  }
  
  .readysearch-panel .scroll-area::-webkit-scrollbar {
    width: 8px;
  }
  
  .readysearch-panel .scroll-area::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .readysearch-panel .scroll-area::-webkit-scrollbar-thumb {
    background-color: hsl(var(--border));
    border-radius: 4px;
  }
  
  .readysearch-panel .scroll-area::-webkit-scrollbar-thumb:hover {
    background-color: hsl(var(--muted-foreground));
  }
}

/* ========================================
   MODERN STYLING GUIDELINES & COMPONENTS
   ======================================== */

/* High-Contrast Code Bubbles */
.code-bubble {
  @apply font-mono text-sm px-3 py-1.5 rounded-md border;
  background: hsl(var(--card));
  color: hsl(var(--card-foreground));
  border-color: hsl(var(--border));
  font-weight: 500;
}

.code-bubble-dark {
  @apply font-mono text-sm px-3 py-1.5 rounded-md;
  background: hsl(var(--foreground));
  color: hsl(var(--background));
  font-weight: 500;
}

/* Tier Badge Styling */
.badge-free {
  @apply bg-green-100 text-green-800 border-green-200;
}

.badge-paid {
  @apply bg-orange-100 text-orange-800 border-orange-200;
}

.badge-premium {
  @apply bg-purple-100 text-purple-800 border-purple-200;
}

/* Info Panel with Better Contrast */
.info-panel {
  @apply p-4 rounded-lg border;
  background: hsl(var(--card));
  border-color: hsl(var(--border));
  color: hsl(var(--card-foreground));
}

.info-panel-blue {
  @apply bg-blue-50 border-blue-200 text-blue-900;
}

.info-panel-green {
  @apply bg-green-50 border-green-200 text-green-900;
}

.info-panel-orange {
  @apply bg-orange-50 border-orange-200 text-orange-900;
}

/* Model Cards with Modern Styling */
.model-card {
  @apply p-4 border rounded-lg bg-card text-card-foreground hover:bg-accent hover:text-accent-foreground transition-colors;
  border-color: hsl(var(--border));
}

.model-card-selected {
  @apply ring-2 ring-primary ring-offset-2;
}

/* Benefits List Styling */
.benefits-list {
  @apply space-y-2;
}

.benefits-item {
  @apply flex items-start gap-2 text-sm;
}

.benefits-bullet {
  @apply w-1.5 h-1.5 rounded-full bg-current mt-2 flex-shrink-0;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/20 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/30;
}

/* Custom focus styles */
.focus-visible:focus {
  @apply outline-none ring-2 ring-ring ring-offset-2;
}

/* Animation classes */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Image grid responsive styles */
.image-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fill, minmax(var(--grid-size, 200px), 1fr));
}

/* Hover preview styles */
.hover-preview {
  position: fixed;
  z-index: 9999;
  background: hsl(var(--card));
  border: 1px solid hsl(var(--border));
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  padding: 8px;
  transition: opacity 0.2s ease;
  opacity: 0;
  pointer-events: none;
  color: hsl(var(--card-foreground));
}

.hover-preview.visible {
  opacity: 1;
}

.hover-preview img {
  max-width: min(400px, 80vw);
  max-height: min(300px, 80vh);
  border-radius: 4px;
  transform: scale(2.0);
  transform-origin: center;
}

/* Loading animation */
.loading-spinner {
  border: 2px solid hsl(var(--muted));
  border-top: 2px solid hsl(var(--primary));
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* File input styles */
.file-input {
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.file-input input[type=file] {
  position: absolute;
  left: -9999px;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
}

@media (max-width: 480px) {
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
}

/* Enhanced hover states for all themes */
.group:hover .group-hover\:opacity-100 {
  opacity: 1 !important;
}

.group:hover .group-hover\:scale-110 {
  transform: scale(1.1) !important;
}

/* Ensure hover works in all themes */
[data-theme] .hover\:bg-gray-100:hover,
.hover\:bg-gray-100:hover {
  background-color: rgba(156, 163, 175, 0.1);
}

.dark .hover\:bg-gray-100:hover {
  background-color: rgba(75, 85, 99, 0.2);
}

/* Fix card hover effects */
.hover\:shadow-md:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.dark .hover\:shadow-md:hover {
  box-shadow: 0 4px 6px -1px rgba(255, 255, 255, 0.1), 0 2px 4px -1px rgba(255, 255, 255, 0.06);
}


/* Force hover effects to work reliably */
.group:hover .opacity-0 {
  opacity: 1 !important;
  transition: opacity 0.2s ease !important;
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1 !important;
}

/* Ensure motion components hover properly */
.group:hover {
  transform: scale(1.02) !important;
  transition: transform 0.2s ease !important;
}

/* Fix rotation button hover states */
.group:hover button {
  background-color: rgba(0, 0, 0, 0.7) !important;
}

/* Better shadow on hover for all themes */
.group:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.dark .group:hover {
  box-shadow: 0 10px 15px -3px rgba(255, 255, 255, 0.1), 0 4px 6px -2px rgba(255, 255, 255, 0.05) !important;
}

/* ========================================
   UI SCALING - 20% SIZE REDUCTION
   ======================================== */

/* Scale down all text elements globally */
.text-xs { font-size: 0.6rem !important; }    /* was 0.75rem */
.text-sm { font-size: 0.7rem !important; }    /* was 0.875rem */
.text-base { font-size: 0.8rem !important; }  /* was 1rem */
.text-lg { font-size: 0.9rem !important; }    /* was 1.125rem */
.text-xl { font-size: 0.96rem !important; }   /* was 1.2rem */
.text-2xl { font-size: 1.2rem !important; }   /* was 1.5rem */
.text-3xl { font-size: 1.44rem !important; }  /* was 1.8rem */

/* Scale down spacing classes */
.p-1 { padding: 0.2rem !important; }    /* was 0.25rem */
.p-2 { padding: 0.4rem !important; }    /* was 0.5rem */
.p-3 { padding: 0.6rem !important; }    /* was 0.75rem */
.p-4 { padding: 0.8rem !important; }    /* was 1rem */
.p-6 { padding: 1.2rem !important; }    /* was 1.5rem */
.p-8 { padding: 1.6rem !important; }    /* was 2rem */

.px-1 { padding-left: 0.2rem !important; padding-right: 0.2rem !important; }
.px-2 { padding-left: 0.4rem !important; padding-right: 0.4rem !important; }
.px-3 { padding-left: 0.6rem !important; padding-right: 0.6rem !important; }
.px-4 { padding-left: 0.8rem !important; padding-right: 0.8rem !important; }
.px-6 { padding-left: 1.2rem !important; padding-right: 1.2rem !important; }

.py-1 { padding-top: 0.2rem !important; padding-bottom: 0.2rem !important; }
.py-2 { padding-top: 0.4rem !important; padding-bottom: 0.4rem !important; }
.py-3 { padding-top: 0.6rem !important; padding-bottom: 0.6rem !important; }
.py-4 { padding-top: 0.8rem !important; padding-bottom: 0.8rem !important; }
.py-6 { padding-top: 1.2rem !important; padding-bottom: 1.2rem !important; }

.m-1 { margin: 0.2rem !important; }    /* was 0.25rem */
.m-2 { margin: 0.4rem !important; }    /* was 0.5rem */
.m-3 { margin: 0.6rem !important; }    /* was 0.75rem */
.m-4 { margin: 0.8rem !important; }    /* was 1rem */
.m-6 { margin: 1.2rem !important; }    /* was 1.5rem */
.m-8 { margin: 1.6rem !important; }    /* was 2rem */

.mx-1 { margin-left: 0.2rem !important; margin-right: 0.2rem !important; }
.mx-2 { margin-left: 0.4rem !important; margin-right: 0.4rem !important; }
.mx-3 { margin-left: 0.6rem !important; margin-right: 0.6rem !important; }
.mx-4 { margin-left: 0.8rem !important; margin-right: 0.8rem !important; }
.mx-6 { margin-left: 1.2rem !important; margin-right: 1.2rem !important; }

.my-1 { margin-top: 0.2rem !important; margin-bottom: 0.2rem !important; }
.my-2 { margin-top: 0.4rem !important; margin-bottom: 0.4rem !important; }
.my-3 { margin-top: 0.6rem !important; margin-bottom: 0.6rem !important; }
.my-4 { margin-top: 0.8rem !important; margin-bottom: 0.8rem !important; }
.my-6 { margin-top: 1.2rem !important; margin-bottom: 1.2rem !important; }

.gap-1 { gap: 0.2rem !important; }     /* was 0.25rem */
.gap-2 { gap: 0.4rem !important; }     /* was 0.5rem */
.gap-3 { gap: 0.6rem !important; }     /* was 0.75rem */
.gap-4 { gap: 0.8rem !important; }     /* was 1rem */
.gap-6 { gap: 1.2rem !important; }     /* was 1.5rem */
.gap-8 { gap: 1.6rem !important; }     /* was 2rem */

.space-y-1 > * + * { margin-top: 0.2rem !important; }
.space-y-2 > * + * { margin-top: 0.4rem !important; }
.space-y-3 > * + * { margin-top: 0.6rem !important; }
.space-y-4 > * + * { margin-top: 0.8rem !important; }
.space-y-6 > * + * { margin-top: 1.2rem !important; }

.space-x-1 > * + * { margin-left: 0.2rem !important; }
.space-x-2 > * + * { margin-left: 0.4rem !important; }
.space-x-3 > * + * { margin-left: 0.6rem !important; }
.space-x-4 > * + * { margin-left: 0.8rem !important; }
.space-x-6 > * + * { margin-left: 1.2rem !important; }

/* Scale down icon sizes */
.h-3 { height: 0.6rem !important; }    /* was 0.75rem */
.h-4 { height: 0.8rem !important; }    /* was 1rem */
.h-5 { height: 1rem !important; }      /* was 1.25rem */
.h-6 { height: 1.2rem !important; }    /* was 1.5rem */
.h-8 { height: 1.6rem !important; }    /* was 2rem */
.h-10 { height: 2rem !important; }     /* was 2.5rem */
.h-12 { height: 2.4rem !important; }   /* was 3rem */

.w-3 { width: 0.6rem !important; }     /* was 0.75rem */
.w-4 { width: 0.8rem !important; }     /* was 1rem */
.w-5 { width: 1rem !important; }       /* was 1.25rem */
.w-6 { width: 1.2rem !important; }     /* was 1.5rem */
.w-8 { width: 1.6rem !important; }     /* was 2rem */
.w-10 { width: 2rem !important; }      /* was 2.5rem */
.w-12 { width: 2.4rem !important; }    /* was 3rem */

/* Scale down border radius */
.rounded { border-radius: 0.2rem !important; }     /* was 0.25rem */
.rounded-md { border-radius: 0.24rem !important; } /* was 0.3rem */
.rounded-lg { border-radius: 0.32rem !important; } /* was 0.4rem */

/* Scale down component-specific sizes */
.btn-sm { 
  font-size: 0.7rem !important; 
  padding: 0.3rem 0.6rem !important; 
}

.btn { 
  font-size: 0.8rem !important; 
  padding: 0.4rem 0.8rem !important; 
}

.btn-lg { 
  font-size: 0.9rem !important; 
  padding: 0.5rem 1rem !important; 
}

/* Scale down form inputs */
input, textarea, select {
  font-size: 0.8rem !important;
  padding: 0.4rem 0.6rem !important;
}

/* Scale down badges */
.badge {
  font-size: 0.6rem !important;
  padding: 0.1rem 0.4rem !important;
}

/* Folder tree specific scaling */
.folder-tree {
  font-size: 0.8rem !important;
}

.folder-tree .folder-item {
  padding: 0.3rem 0.6rem !important;
  gap: 0.4rem !important;
}

.folder-tree .folder-name {
  font-size: 0.8rem !important;
}

/* Enhanced image grid scaling */
.enhanced-image-grid .grid-header {
  font-size: 0.8rem !important;
  padding: 0.8rem !important;
}

.enhanced-image-grid .image-card {
  padding: 0.6rem !important;
}

.enhanced-image-grid .image-info {
  font-size: 0.7rem !important;
  padding: 0.4rem !important;
}

/* OCR panel scaling */
.ocr-panel {
  font-size: 0.8rem !important;
}

.ocr-panel .panel-header {
  font-size: 0.9rem !important;
  padding: 0.8rem !important;
}

.ocr-panel .form-field {
  font-size: 0.8rem !important;
  margin-bottom: 0.6rem !important;
}

.ocr-panel label {
  font-size: 0.8rem !important;
  margin-bottom: 0.3rem !important;
}

/* Card scaling */
.card {
  padding: 0.8rem !important;
}

.card-header {
  padding: 0.8rem !important;
}

.card-content {
  padding: 0.8rem !important;
}

.card-title {
  font-size: 0.9rem !important;
}

/* Button group scaling */
.btn-group button {
  font-size: 0.7rem !important;
  padding: 0.3rem 0.6rem !important;
}

/* Tab scaling */
.tabs-trigger {
  font-size: 0.8rem !important;
  padding: 0.4rem 0.8rem !important;
}

.tabs-content {
  padding: 0.8rem !important;
}

/* Dialog scaling */
.dialog-content {
  font-size: 0.8rem !important;
}

.dialog-header {
  padding: 0.8rem !important;
}

.dialog-title {
  font-size: 0.9rem !important;
}
