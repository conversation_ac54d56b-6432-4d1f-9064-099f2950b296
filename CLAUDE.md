# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

DL Organizer is a comprehensive AI-powered driver's license OCR processing system built as a hybrid web application. It combines a Next.js frontend with Express.js backend to provide folder management, image processing, and multi-provider OCR capabilities for extracting structured data from driver's license images.

**Current Version**: v1.3.1 with Smart Filter Analyzer and Power-Filter Workbench System

**Key Features**:
- **Smart Filter Analyzer**: AI-powered folder analysis with license side detection and pattern recognition
- **Power-Filter Workbench**: Professional filtering system with stackable filters, smart selections, and advanced pagination
- **Multi-Provider OCR**: OpenAI GPT-4o, OpenRouter models with real-time model validation
- **Windows-Optimized**: Native Windows filesystem integration with advanced port management
- **ReadySearch Integration**: Australian DL database search and verification (v3.0)

## Core Commands

### Development
```bash
# Start development environment (bulletproof startup with port management)
npm run dev

# Individual servers (ports auto-resolved)
npm run dev:frontend     # Next.js (port 3031)
npm run dev:backend      # Express (dynamic port)

# Smart port management (manual port sync + startup)
npm run dev:smart

# Legacy concurrent startup (if bulletproof fails)
npm run dev:legacy
```

### Testing
```bash
npm run test                  # Jest unit tests
npm run test:watch           # Jest in watch mode
npm run test:e2e             # Playwright E2E tests
npm run test:integration     # Integration tests only
npm run lint                 # ESLint
npm run typecheck            # TypeScript checking

# Run specific E2E test
npm run test:e2e -- --grep "image processing"
```

### Production & Maintenance
```bash
npm run build               # Build for production
npm run start               # Start production servers
npm run setup               # Initial production setup
npm run setup:windows       # Windows-specific setup
npm run health              # System health check
npm run backup              # Create database backup
npm run backup:list         # List available backups
npm run backup:restore      # Restore from backup
npm run maintenance         # Run maintenance tasks
npm run service:install     # Install as Windows service
npm run service:uninstall   # Uninstall Windows service
```

### Utility & Port Management Commands
```bash
# Advanced port management system
npm run ports:check         # Check port availability with health report
npm run ports:resolve       # Resolve port conflicts automatically 
npm run ports:sync          # Sync ports across all components
npm run ports:reset         # Reset port configuration from scratch
npm run ports:memory        # View port memory and success patterns
npm run ports:warnings      # Display port-related warnings
npm run ports:bulletproof   # Run bulletproof startup system

# Process monitoring and cleanup
npm run process:check       # Check running processes
npm run process:cleanup     # Clean up orphaned processes
npm run process:emergency   # Emergency process cleanup

# Advanced launcher (with border validation)
.\launcher.ps1              # PowerShell launcher with full menu
launcher.bat                # Batch launcher (calls PowerShell)
```

## Architecture Overview

### Technology Stack
- **Frontend**: Next.js 14.2+ (App Router) with TypeScript 5.8+
- **Styling**: Tailwind CSS + Radix UI components
- **State Management**: React state with custom hooks + filter reducers
- **Backend**: Express.js with middleware stack + EventEmitter for real-time updates
- **Database**: SQLite with synchronous API + intelligent caching
- **Image Processing**: Sharp with memory management and concurrency controls
- **AI Integration**: Multi-provider OCR (OpenAI GPT-4o, OpenRouter models) + side detection
- **Testing**: Jest (unit), Playwright (E2E)
- **Windows Integration**: Native filesystem APIs, drive detection, port management
- **Real-time Features**: Server-Sent Events (SSE) for live progress tracking

### Advanced Systems

#### Port Management Architecture
- **Bulletproof Startup**: `scripts/bulletproof-startup.js` guarantees successful startup
- **Port Sync Manager**: `scripts/port-sync-manager.js` handles dynamic port resolution
- **Port Memory System**: `scripts/port-memory-system.js` learns from successful patterns
- **Configuration Sync**: Automatic updates across package.json, PowerShell, and API routes
- **Conflict Resolution**: Multi-level fallback strategies prevent port hunting loops
- **Health Monitoring**: Real-time port health checking with process identification

#### Smart Analyzer System  
- **AI-Powered Analysis**: License side detection (front/back/selfie/unknown) using OCR
- **Pattern Recognition**: Filename clusters, size distributions, resolution buckets
- **Real-time Streaming**: Server-Sent Events for live progress updates
- **Intelligent Caching**: Persistent analysis results with cache invalidation
- **Filter Integration**: Generated filter chips integrate with Power-Filter Workbench

### Core Components

#### Backend Architecture (`backend/`)

**Main Server**
- `server.js` - Express server with comprehensive middleware stack, error handling, and graceful shutdown

**Services Layer** (`backend/services/`)
- `ocr-service.js` - Multi-provider OCR orchestration with fallback strategies
- `model-validator.js` - Real-time model availability checking with caching
- `batch-processor.js` - Queue-based bulk processing with progress tracking
- `smart-analyzer.js` - **NEW** AI-powered folder analysis with license side detection
- `cost-tracker.js` - API usage monitoring with spending limits
- `file-manager.js` - File organization and naming strategies
- `local-model-service.js` - Local model integration support

**Utilities** (`backend/utils/`)
- `windows-fs.js` - Windows-specific filesystem operations
- `image-processor.js` - Sharp-based processing with format conversion
- `detect-license-side.js` - **NEW** AI-powered license side detection (front/back/selfie)
- `build-manifest.js` - **NEW** Analysis result manifest builder
- `file-logger.js` - **NEW** Debug logging for complex operations
- `logger.js` - Winston logger with rotation
- `database.js` - SQLite connection management

**Routes** (`backend/routes/`)
- `ocr.js` - OCR endpoints with validation
- `batch-ocr.js` - Batch processing endpoints
- `smart-analyzer.js` - **NEW** Smart folder analysis endpoints with SSE streaming
- `filesystem.js` - File system operations
- `folders.js` - Folder management
- `images.js` - Image operations
- `projects.js` - Project CRUD
- `model-validation.js` - Model checking endpoints

#### Frontend Architecture (`src/`)

**Components** (`src/components/dl-organizer/`)
- `project-overview.tsx` - Main dashboard with statistics
- `folder-tree.tsx` - Recursive folder navigation
- `enhanced-image-grid.tsx` - **ENHANCED** Virtualized image grid with Power-Filter system
- `processing-mode-tabs.tsx` - **NEW** Smart Filter Analyzer and batch processing tabs
- `ocr-panel.tsx` - Multi-provider OCR interface
- `batch-mode-panel.tsx` - Bulk processing UI with filter integration
- `ocr-testing-playground.tsx` - Model testing and validation
- `text-editor.tsx` - In-app file editing
- `settings-panel.tsx` - Configuration management
- `model-validation-panel.tsx` - Real-time model checking

**API Integration** (`src/app/api/`)
- Next.js API routes for frontend-backend communication
- Proxy endpoints for OCR providers
- Image serving endpoints

**Utilities** (`src/lib/`)
- `utils.ts` - Common utilities and helpers
- `storage-utils.ts` - LocalStorage abstraction
- `australian-ocr-merger.ts` - AU-specific OCR logic

**Type Definitions** (`src/types/`)
- Comprehensive TypeScript interfaces for all data models

### Key APIs

#### OCR Operations
```typescript
POST /api/ocr/analyze               // Single image OCR
POST /api/ocr/analyze-by-path       // Path-based OCR
GET /api/ocr/prompts                // Available prompts
POST /api/ocr/save-results          // Persist results
GET /api/ocr/saved-data/:imageId    // Retrieve saved data
```

#### Smart Analyzer (NEW)
```typescript
POST /api/smart-analyzer/analyze    // Start folder analysis job
GET /api/smart-analyzer/status/:jobId        // Get job status
POST /api/smart-analyzer/cancel/:jobId       // Cancel running job
GET /api/smart-analyzer/stream/:jobId        // SSE stream for live progress
GET /api/smart-analyzer/results/:jobId       // Get final analysis results
```

#### Model Validation
```typescript
GET /api/model-validation/available     // List available models
POST /api/model-validation/validate     // Validate model list
POST /api/model-validation/auto-fix     // Auto-fix invalid models
DELETE /api/model-validation/cache      // Clear validation cache
```

#### Batch Processing
```typescript
POST /api/batch-ocr/process            // Start batch job
GET /api/batch-ocr/status/:jobId       // Job status
POST /api/batch-ocr/cancel/:jobId      // Cancel job
```

#### File System
```typescript
GET /api/filesystem/drives             // Windows drives
POST /api/filesystem/scan              // Scan directory
GET /api/folders/:id/images            // List images
POST /api/folders/:id/text             // Text file ops
```

#### Image Operations
```typescript
GET /thumbnails/:filename              // Cached thumbnail
GET /previews/:filename                // Full preview
POST /api/images/:id/rotate            // Rotate image
```

## Data Models

### Core Types
```typescript
interface OCRResult {
  firstName: string;
  lastName: string;
  dateOfBirth: string;        // YYYY-MM-DD
  address: string;
  licenseNumber: string;
  expirationDate: string;     // YYYY-MM-DD
  state: string;              // Two-letter code
  confidence: number;         // 0.0-1.0
  rawText: string;
  mode: 'us' | 'australian';
  // Optional fields
  middleName?: string;
  issueDate?: string;         // US mode
  cardNumber?: string;        // AUS mode
}

interface Project {
  id: string;
  name: string;
  rootPath: string;
  createdAt: string;
  updatedAt: string;
  settings?: ProjectSettings;
}

interface FolderNode {
  id: string;
  name: string;
  path: string;
  imageCount: number;
  hasTextFile: boolean;
  children: FolderNode[];
  depth: number;
  isExpanded?: boolean;
}

interface BatchJob {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  totalFolders: number;
  processedFolders: number;
  results: BatchResult[];
  startedAt: string;
  completedAt?: string;
}
```

### Database Schema
- `projects` - Project configurations and settings
- `folders` - Hierarchical folder structure
- `images` - Image metadata, rotation state, OCR results
- `ocr_results` - Extracted data with provider info
- `batch_jobs` - Batch processing state
- `api_usage` - Cost tracking data

## Development Workflow

### Adding New Features

1. **Backend Implementation**:
   - Create service in `backend/services/`
   - Add routes in `backend/routes/`
   - Update database schema if needed
   - Add error handling and logging
   - Write unit tests

2. **Frontend Implementation**:
   - Create components in `src/components/dl-organizer/`
   - Add types in `src/types/`
   - Implement API calls
   - Handle loading/error states
   - Add user feedback (toasts, progress)

3. **Testing**:
   - Unit tests for business logic
   - Integration tests for API endpoints
   - E2E tests for user workflows
   - Visual regression tests for UI

### OCR Provider Integration

1. **Backend Setup**:
   - Add provider config in `ocr-service.js`
   - Implement API client with error handling
   - Add cost tracking metrics
   - Set up rate limiting

2. **Model Configuration**:
   - Define model capabilities
   - Set pricing information
   - Configure retry strategies
   - Add validation logic

3. **Frontend Integration**:
   - Update model selector UI
   - Add provider-specific settings
   - Implement connection testing
   - Show provider status

### Testing Strategy

**Unit Tests** (Jest)
- Business logic isolation
- Service layer testing
- Utility function coverage
- Mock external dependencies

**E2E Tests** (Playwright)
- User workflow validation
- Cross-browser testing
- Visual regression checks
- Performance monitoring

**Test Organization**
```
tests/
├── ocr-testing/          # OCR model tests
├── batch-processing/     # Batch operation tests
├── integration/          # API integration tests
└── *.spec.ts            # Feature-specific tests
```

## Common Patterns

### Error Handling
```javascript
// Consistent error structure
class APIError extends Error {
  constructor(message, statusCode, details) {
    super(message);
    this.statusCode = statusCode;
    this.details = details;
  }
}

// Middleware error handler
app.use((err, req, res, next) => {
  logger.error(err);
  res.status(err.statusCode || 500).json({
    error: err.message,
    details: err.details
  });
});
```

### Progress Tracking
```javascript
// Emit progress events
eventEmitter.emit('progress', {
  jobId,
  current: processedCount,
  total: totalCount,
  percentage: Math.round((processedCount / totalCount) * 100)
});
```

### Caching Strategy
- Thumbnails: File-based with Sharp
- OCR Results: Database with TTL
- Model Validation: In-memory with 5min expiry
- API Responses: Memory cache for static data

## Windows-Specific Considerations

### File System
- Handle UNC paths and network drives
- Support paths >260 characters
- Implement retry logic for locked files
- Normalize path separators

### Security
- Validate all file paths
- Prevent directory traversal
- Handle permission errors gracefully
- Use Windows Credential Manager for secrets

### Performance
- Batch file operations
- Use streaming for large files
- Implement connection pooling
- Cache directory listings

## Production Deployment

### Health Monitoring
- Endpoint: `/api/health`
- Database connectivity check
- Disk space monitoring
- API provider status
- Memory usage tracking

### Logging
- Location: `data/logs/`
- Rotation: Daily with 7-day retention
- Levels: error, warn, info, debug
- Format: JSON for parsing

### Backup Strategy
- Database: SQLite backup API
- Scheduled via Windows Task Scheduler
- Retention: 30 days
- Location: `data/backups/`

## Memory Notes

### Critical Development Warnings
- **NEVER RUN "sudo killall node"** - kills Claude Code CLI environment
- **NEVER manually change ports** in package.json or config files - use port management system only
- **Check port availability** before starting servers with `npm run ports:check`
- **Use bulletproof startup** (`npm run dev`) to avoid port conflicts
- **Monitor memory usage** - Sharp is configured for memory management (512MB cache, concurrency=1)
- **Validate API keys** through settings panel before OCR operations
- **Monitor rate limits** during batch operations and Smart Analyzer runs

### Port Management Best Practices
- Always use the port management system for configuration changes
- Ports are dynamically resolved and stored in `data/port-config.json` and `data/launcher-ports.ps1`
- Use `npm run ports:sync` to update all components after manual port changes
- Use `npm run ports:reset` to start fresh if port configuration gets corrupted
- The launcher.bat and launcher.ps1 scripts handle port synchronization automatically

### Launcher System Architecture
- `launcher.bat` is a simple batch wrapper that calls `launcher.ps1` with PowerShell
- `launcher.ps1` provides a comprehensive menu-driven interface with 30+ options
- Integrated border validation system ensures consistent CLI presentation
- Port configuration is loaded dynamically from `data/launcher-ports.ps1`
- Smart development environment starts with automatic port conflict resolution
- All launcher actions integrate with the bulletproof startup and port management systems

### Smart Analyzer Considerations
- Analysis jobs run concurrently with max 2 jobs (vs 3 for OCR batch processing)
- Uses intelligent caching - subsequent folder scans are instant if cache is valid
- License side detection uses cost-effective models to minimize API usage
- Progress is streamed via Server-Sent Events for real-time feedback
- Jobs have 5-minute timeout to prevent stuck processing

### Performance Optimization Tips
- Enable thumbnail caching for large folders (handled automatically)
- Use Smart Analyzer before batch OCR to identify content types and optimize processing
- Process filtered selections rather than entire folders to reduce API costs
- Use Power-Filter system for sub-100ms filtering on large datasets
- Consider pagination settings (50-500 items) based on folder size and system performance

This documentation provides the essential information needed to work effectively with the DL Organizer codebase while maintaining its robust architecture and Windows-optimized features.