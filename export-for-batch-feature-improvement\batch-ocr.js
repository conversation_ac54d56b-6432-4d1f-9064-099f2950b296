const express = require('express');
const router = express.Router();
const fs = require('fs').promises;
const path = require('path');
const OCRService = require('../services/ocr-service');

// Initialize OCR service
const ocrService = new OCRService();

// Batch OCR processing endpoint
router.post('/batch-process', async (req, res) => {
  try {
    const { folderIds, mode = 'auto-detect', exportFormats = ['json', 'txt'], includeSelfiDescription = false, txtFileOption = 'new' } = req.body;
    
    if (!folderIds || !Array.isArray(folderIds) || folderIds.length === 0) {
      return res.status(400).json({ error: 'folderIds array is required' });
    }

    console.log(`Starting batch OCR processing for ${folderIds.length} folders`);
    
    const results = [];
    const processingStats = {
      totalFolders: folderIds.length,
      processedFolders: 0,
      totalImages: 0,
      processedImages: 0,
      successfulExtractions: 0,
      failedExtractions: 0,
      exportedFiles: 0
    };

    for (const folderId of folderIds) {
      try {
        // Get folder path from database or storage
        const folderPath = await getFolderPath(folderId);
        if (!folderPath) {
          console.warn(`Folder not found: ${folderId}`);
          continue;
        }

        console.log(`Processing folder: ${folderPath}`);
        
        // Scan for images in the folder
        const images = await scanFolderForImages(folderPath);
        processingStats.totalImages += images.length;
        
        const folderResults = [];
        
        for (const imagePath of images) {
          try {
            processingStats.processedImages++;
            
            // Load and analyze the image
            const imageBuffer = await fs.readFile(imagePath);
            
            // Use auto-detection mode if specified, otherwise use the provided mode
            const extractionType = mode === 'auto-detect' ? 'auto_detect' : mode;
            
            const ocrResult = await ocrService.processImage(imageBuffer, {
              extractionType: extractionType,
              includeSelfiDescription: includeSelfiDescription
            });
            
            if (ocrResult && ocrResult.success) {
              // Extract document type from the result if using auto-detection
              const detectedType = ocrResult.result?.documentType || extractionType;
              
              folderResults.push({
                imagePath,
                documentType: detectedType,
                data: ocrResult.result || ocrResult.data,
                confidence: ocrResult.confidence || ocrResult.result?.confidence || 0.9
              });
              processingStats.successfulExtractions++;
            } else {
              processingStats.failedExtractions++;
              console.warn(`Failed to process image: ${imagePath}`, ocrResult?.error);
            }
          } catch (imageError) {
            processingStats.failedExtractions++;
            console.error(`Error processing image ${imagePath}:`, imageError);
          }
        }
        
        // Export results to the folder
        if (folderResults.length > 0) {
          await exportResultsToFolder(folderPath, folderResults, exportFormats, txtFileOption);
          processingStats.exportedFiles += exportFormats.length;
        }
        
        results.push({
          folderId,
          folderPath,
          results: folderResults,
          imageCount: images.length,
          successCount: folderResults.length
        });
        
        processingStats.processedFolders++;
        
      } catch (folderError) {
        console.error(`Error processing folder ${folderId}:`, folderError);
        results.push({
          folderId,
          error: folderError.message,
          imageCount: 0,
          successCount: 0
        });
      }
    }

    console.log('Batch OCR processing completed:', processingStats);
    
    res.json({
      success: true,
      results,
      stats: processingStats,
      message: `Processed ${processingStats.processedFolders} folders with ${processingStats.successfulExtractions} successful extractions`
    });
    
  } catch (error) {
    console.error('Batch OCR processing error:', error);
    res.status(500).json({ 
      error: 'Batch OCR processing failed',
      details: error.message 
    });
  }
});

// Helper function to get folder path from ID
async function getFolderPath(folderId) {
  try {
    // Folder IDs are base64 encoded paths
    const decodedPath = Buffer.from(folderId, 'base64').toString('utf8');
    
    // Verify the path exists
    const stats = await fs.stat(decodedPath);
    if (stats.isDirectory()) {
      return decodedPath;
    }
    
    return null;
  } catch (error) {
    console.warn(`Could not resolve folder path for ID ${folderId}:`, error.message);
    return null;
  }
}

// Helper function to scan folder for images
async function scanFolderForImages(folderPath) {
  try {
    const files = await fs.readdir(folderPath);
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp'];
    
    const images = files
      .filter(file => imageExtensions.includes(path.extname(file).toLowerCase()))
      .map(file => path.join(folderPath, file));
    
    return images;
  } catch (error) {
    console.error(`Error scanning folder ${folderPath}:`, error);
    return [];
  }
}

// Helper function to auto-detect document type
async function autoDetectDocumentType(imagePath) {
  try {
    // For now, use simple heuristics based on image analysis
    // In a real implementation, this would use AI to analyze the image
    const filename = path.basename(imagePath).toLowerCase();
    
    if (filename.includes('selfie') || filename.includes('photo')) {
      return 'selfie';
    } else if (filename.includes('passport')) {
      return 'passport';
    } else if (filename.includes('license') || filename.includes('dl') || filename.includes('id')) {
      return 'driver_license';
    }
    
    // Default to driver's license for unknown types
    return 'driver_license';
  } catch (error) {
    console.error('Error auto-detecting document type:', error);
    return 'driver_license';
  }
}

// Helper function to find existing TXT files in the folder
async function findExistingTxtFile(folderPath) {
  try {
    const files = await fs.readdir(folderPath);
    const txtFiles = files.filter(file => 
      path.extname(file).toLowerCase() === '.txt' && 
      !file.startsWith('ocr-results-') // Exclude our own OCR result files
    );
    
    if (txtFiles.length > 0) {
      // Return the first non-OCR txt file found
      return path.join(folderPath, txtFiles[0]);
    }
    
    return null;
  } catch (error) {
    console.error('Error finding existing TXT files:', error);
    return null;
  }
}

// Helper function to export results to folder
async function exportResultsToFolder(folderPath, results, exportFormats, txtFileOption = 'new') {
  try {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    if (exportFormats.includes('json')) {
      const jsonPath = path.join(folderPath, `ocr-results-${timestamp}.json`);
      await fs.writeFile(jsonPath, JSON.stringify(results, null, 2));
      console.log(`Exported JSON to: ${jsonPath}`);
    }
    
    if (exportFormats.includes('txt')) {
      const txtContent = generateTextReport(results);
      
      if (txtFileOption === 'append') {
        // Look for existing TXT files in the folder
        const existingTxtFile = await findExistingTxtFile(folderPath);
        
        if (existingTxtFile) {
          // Append to existing file
          const separator = '\n\n' + '='.repeat(60) + '\n';
          const appendContent = separator + 'OCR Results Added: ' + new Date().toLocaleString() + '\n' + '='.repeat(60) + '\n\n' + txtContent;
          await fs.appendFile(existingTxtFile, appendContent);
          console.log(`Appended TXT results to: ${existingTxtFile}`);
        } else {
          // No existing file found, create new one
          const txtPath = path.join(folderPath, `ocr-results-${timestamp}.txt`);
          await fs.writeFile(txtPath, txtContent);
          console.log(`Created new TXT file: ${txtPath}`);
        }
      } else {
        // Create new file (default behavior)
        const txtPath = path.join(folderPath, `ocr-results-${timestamp}.txt`);
        await fs.writeFile(txtPath, txtContent);
        console.log(`Exported TXT to: ${txtPath}`);
      }
    }
    
  } catch (error) {
    console.error('Error exporting results:', error);
    throw error;
  }
}

// Helper function to generate text report
function generateTextReport(results) {
  const lines = [];
  lines.push('OCR Processing Results');
  lines.push('='.repeat(50));
  lines.push(`Generated: ${new Date().toLocaleString()}`);
  lines.push(`Total Images Processed: ${results.length}`);
  lines.push('');
  
  results.forEach((result, index) => {
    lines.push(`Image ${index + 1}: ${path.basename(result.imagePath)}`);
    lines.push(`Document Type: ${result.documentType}`);
    lines.push(`Confidence: ${Math.round(result.confidence * 100)}%`);
    lines.push('');
    
    if (result.documentType === 'selfie') {
      lines.push('Selfie Description:');
      lines.push(result.data.description || 'No description available');
      if (result.data.gender) lines.push(`Gender: ${result.data.gender}`);
      if (result.data.estimatedAge) lines.push(`Estimated Age: ${result.data.estimatedAge}`);
      if (result.data.details) {
        lines.push('Details:');
        if (result.data.details.hairColor) lines.push(`  Hair Color: ${result.data.details.hairColor}`);
        if (result.data.details.clothing) lines.push(`  Clothing: ${result.data.details.clothing}`);
        if (result.data.details.accessories) lines.push(`  Accessories: ${result.data.details.accessories}`);
        if (result.data.details.setting) lines.push(`  Setting: ${result.data.details.setting}`);
      }
    } else if (result.documentType === 'driver_license') {
      lines.push('Driver\'s License Information:');
      lines.push(`Name: ${result.data.firstName || ''} ${result.data.lastName || ''}`);
      if (result.data.dateOfBirth) lines.push(`Date of Birth: ${result.data.dateOfBirth}`);
      if (result.data.licenseNumber) lines.push(`License Number: ${result.data.licenseNumber}`);
      if (result.data.state) lines.push(`State: ${result.data.state}`);
      if (result.data.expirationDate) lines.push(`Expiration Date: ${result.data.expirationDate}`);
      if (result.data.address) lines.push(`Address: ${result.data.address}`);
    } else if (result.documentType === 'passport') {
      lines.push('Passport Information:');
      lines.push(`Name: ${result.data.firstName || ''} ${result.data.lastName || ''}`);
      if (result.data.dateOfBirth) lines.push(`Date of Birth: ${result.data.dateOfBirth}`);
      if (result.data.passportNumber) lines.push(`Passport Number: ${result.data.passportNumber}`);
      if (result.data.nationality) lines.push(`Nationality: ${result.data.nationality}`);
      if (result.data.country) lines.push(`Country: ${result.data.country}`);
      if (result.data.expirationDate) lines.push(`Expiration Date: ${result.data.expirationDate}`);
      if (result.data.placeOfBirth) lines.push(`Place of Birth: ${result.data.placeOfBirth}`);
    } else if (result.documentType === 'id_card') {
      lines.push('ID Card Information:');
      lines.push(`Name: ${result.data.firstName || ''} ${result.data.lastName || ''}`);
      if (result.data.dateOfBirth) lines.push(`Date of Birth: ${result.data.dateOfBirth}`);
      if (result.data.idNumber) lines.push(`ID Number: ${result.data.idNumber}`);
      if (result.data.address) lines.push(`Address: ${result.data.address}`);
      if (result.data.extractedText) lines.push(`Extracted Text: ${result.data.extractedText}`);
    } else {
      lines.push('Document Information:');
      lines.push(`Document Type: ${result.documentType}`);
      if (result.data.extractedText) {
        lines.push('Extracted Text:');
        lines.push(result.data.extractedText);
      }
    }
    
    lines.push('');
    lines.push('-'.repeat(30));
    lines.push('');
  });
  
  return lines.join('\n');
}

module.exports = router;