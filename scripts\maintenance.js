const fs = require('fs').promises;
const path = require('path');
const DatabaseManager = require('../backend/config/database');
const ImageProcessor = require('../backend/utils/image-processor');

class MaintenanceTasks {
  constructor() {
    this.dataDir = path.join(__dirname, '..', 'data');
    this.logDir = path.join(this.dataDir, 'logs');
  }
  
  async runAll() {
    console.log('🔧 Running maintenance tasks...\n');
    
    const tasks = [
      { name: 'Database Optimization', fn: () => this.optimizeDatabase() },
      { name: 'Cache Cleanup', fn: () => this.cleanupCache() },
      { name: 'Log Rotation', fn: () => this.rotateLogs() },
      { name: 'Backup Cleanup', fn: () => this.cleanupBackups() },
      { name: 'Temp File Cleanup', fn: () => this.cleanupTempFiles() },
      { name: 'System Health Check', fn: () => this.systemHealthCheck() }
    ];
    
    const results = [];
    
    for (const task of tasks) {
      try {
        console.log(`Running: ${task.name}...`);
        const result = await task.fn();
        results.push({ name: task.name, status: 'success', result });
        console.log(`✅ ${task.name} completed\n`);
      } catch (error) {
        results.push({ name: task.name, status: 'error', error: error.message });
        console.error(`❌ ${task.name} failed: ${error.message}\n`);
      }
    }
    
    // Generate maintenance report
    await this.generateReport(results);
    
    console.log('🎉 Maintenance tasks completed!');
    return results;
  }
  
  async optimizeDatabase() {
    const dbManager = new DatabaseManager();
    await dbManager.initialize();
    
    console.log('  - Running VACUUM...');
    await dbManager.vacuum();
    
    console.log('  - Updating statistics...');
    await dbManager.analyze();
    
    const stats = await dbManager.getStats();
    await dbManager.close();
    
    return {
      message: 'Database optimized',
      stats
    };
  }
  
  async cleanupCache() {
    const imageProcessor = new ImageProcessor();
    
    console.log('  - Cleaning up old cache files...');
    const cleanedCount = await imageProcessor.cleanupCache();
    
    const stats = await imageProcessor.getCacheStats();
    
    return {
      message: `Cleaned ${cleanedCount} cache files`,
      stats
    };
  }
  
  async rotateLogs() {
    const maxLogAge = 30 * 24 * 60 * 60 * 1000; // 30 days
    const maxLogSize = 50 * 1024 * 1024; // 50MB
    
    try {
      const logFiles = await fs.readdir(this.logDir);
      let rotatedCount = 0;
      let deletedCount = 0;
      
      for (const file of logFiles) {
        const filePath = path.join(this.logDir, file);
        const stats = await fs.stat(filePath);
        
        // Delete old logs
        if (Date.now() - stats.mtime.getTime() > maxLogAge) {
          await fs.unlink(filePath);
          deletedCount++;
          console.log(`  - Deleted old log: ${file}`);
          continue;
        }
        
        // Rotate large logs
        if (stats.size > maxLogSize) {
          const timestamp = new Date().toISOString().split('T')[0];
          const rotatedName = `${path.parse(file).name}-${timestamp}${path.parse(file).ext}`;
          const rotatedPath = path.join(this.logDir, rotatedName);
          
          await fs.rename(filePath, rotatedPath);
          rotatedCount++;
          console.log(`  - Rotated large log: ${file} -> ${rotatedName}`);
        }
      }
      
      return {
        message: `Rotated ${rotatedCount} logs, deleted ${deletedCount} old logs`
      };
    } catch (error) {
      if (error.code === 'ENOENT') {
        return { message: 'No logs directory found' };
      }
      throw error;
    }
  }
  
  async cleanupBackups() {
    const backupDir = path.join(this.dataDir, 'backups');
    const maxBackups = 30;
    
    try {
      const files = await fs.readdir(backupDir);
      const backupFiles = files
        .filter(file => file.endsWith('.db'))
        .map(file => ({
          name: file,
          path: path.join(backupDir, file)
        }));
      
      if (backupFiles.length <= maxBackups) {
        return { message: `${backupFiles.length} backups (within limit)` };
      }
      
      // Sort by modification time
      const filesWithStats = await Promise.all(
        backupFiles.map(async (file) => {
          const stats = await fs.stat(file.path);
          return { ...file, mtime: stats.mtime };
        })
      );
      
      filesWithStats.sort((a, b) => a.mtime - b.mtime);
      
      // Remove oldest files
      const filesToRemove = filesWithStats.slice(0, filesWithStats.length - maxBackups);
      
      for (const file of filesToRemove) {
        await fs.unlink(file.path);
        console.log(`  - Removed old backup: ${file.name}`);
      }
      
      return {
        message: `Removed ${filesToRemove.length} old backups, kept ${maxBackups} most recent`
      };
    } catch (error) {
      if (error.code === 'ENOENT') {
        return { message: 'No backups directory found' };
      }
      throw error;
    }
  }
  
  async cleanupTempFiles() {
    const tempDir = path.join(this.dataDir, 'temp');
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    
    try {
      const files = await fs.readdir(tempDir);
      let deletedCount = 0;
      
      for (const file of files) {
        const filePath = path.join(tempDir, file);
        const stats = await fs.stat(filePath);
        
        if (Date.now() - stats.mtime.getTime() > maxAge) {
          await fs.unlink(filePath);
          deletedCount++;
          console.log(`  - Deleted temp file: ${file}`);
        }
      }
      
      return {
        message: `Cleaned ${deletedCount} temporary files`
      };
    } catch (error) {
      if (error.code === 'ENOENT') {
        return { message: 'No temp directory found' };
      }
      throw error;
    }
  }
  
  async systemHealthCheck() {
    const os = require('os');
    const results = {};
    
    // Memory usage
    const memUsage = process.memoryUsage();
    const totalMem = os.totalmem();
    const freeMem = os.freemem();
    
    results.memory = {
      heapUsed: Math.round(memUsage.heapUsed / 1024 / 1024),
      heapTotal: Math.round(memUsage.heapTotal / 1024 / 1024),
      systemTotal: Math.round(totalMem / 1024 / 1024 / 1024),
      systemFree: Math.round(freeMem / 1024 / 1024 / 1024),
      usage: Math.round(((totalMem - freeMem) / totalMem) * 100)
    };
    
    // Disk usage
    try {
      const stats = await fs.stat(this.dataDir);
      results.disk = {
        dataDirectory: this.dataDir,
        accessible: true
      };
    } catch (error) {
      results.disk = {
        dataDirectory: this.dataDir,
        accessible: false,
        error: error.message
      };
    }
    
    // Database health
    try {
      const dbManager = new DatabaseManager();
      await dbManager.initialize();
      const health = await dbManager.healthCheck();
      const stats = await dbManager.getStats();
      await dbManager.close();
      
      results.database = {
        status: health.status,
        stats
      };
    } catch (error) {
      results.database = {
        status: 'error',
        error: error.message
      };
    }
    
    // Process uptime
    results.process = {
      uptime: Math.round(process.uptime()),
      pid: process.pid,
      version: process.version
    };
    
    return results;
  }
  
  async generateReport(results) {
    const report = {
      timestamp: new Date().toISOString(),
      results,
      summary: {
        total: results.length,
        successful: results.filter(r => r.status === 'success').length,
        failed: results.filter(r => r.status === 'error').length
      }
    };
    
    const reportPath = path.join(this.logDir, `maintenance-${new Date().toISOString().split('T')[0]}.json`);
    
    try {
      await fs.mkdir(this.logDir, { recursive: true });
      await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
      console.log(`📊 Maintenance report saved: ${reportPath}`);
    } catch (error) {
      console.warn('⚠️  Could not save maintenance report:', error.message);
    }
    
    return report;
  }
}

// CLI interface
async function main() {
  const maintenance = new MaintenanceTasks();
  
  const command = process.argv[2];
  
  switch (command) {
    case 'all':
    case undefined:
      await maintenance.runAll();
      break;
      
    case 'database':
      await maintenance.optimizeDatabase();
      break;
      
    case 'cache':
      await maintenance.cleanupCache();
      break;
      
    case 'logs':
      await maintenance.rotateLogs();
      break;
      
    case 'health':
      const health = await maintenance.systemHealthCheck();
      console.log(JSON.stringify(health, null, 2));
      break;
      
    default:
      console.log('DL Organizer Maintenance Tool');
      console.log('');
      console.log('Usage:');
      console.log('  node maintenance.js [task]');
      console.log('');
      console.log('Tasks:');
      console.log('  all       - Run all maintenance tasks (default)');
      console.log('  database  - Optimize database');
      console.log('  cache     - Clean up cache files');
      console.log('  logs      - Rotate and clean logs');
      console.log('  health    - System health check');
      break;
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = MaintenanceTasks;