"use client"

import { useMemo } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckSquare, Square, Users, Eye } from 'lucide-react';
import { ImageFile } from '@/types';
import { cn } from '@/lib/utils';

interface SelectionControlsProps {
  filteredImages: ImageFile[];
  totalImages: ImageFile[];
  selectedIds: Set<string>;
  onSelectionChange: (ids: Set<string>) => void;
  className?: string;
}

export default function SelectionControls({
  filteredImages,
  totalImages,
  selectedIds,
  onSelectionChange,
  className
}: SelectionControlsProps) {
  // Memoized calculations for performance
  const visibleIds = useMemo(() => 
    new Set(filteredImages.map(img => img.id)), 
    [filteredImages]
  );

  const selectedVisibleCount = useMemo(() => {
    let count = 0;
    for (const id of selectedIds) {
      if (visibleIds.has(id)) count++;
    }
    return count;
  }, [selectedIds, visibleIds]);

  const allVisibleSelected = filteredImages.length > 0 && selectedVisibleCount === filteredImages.length;
  const someVisibleSelected = selectedVisibleCount > 0;

  // Select all currently visible images (additive)
  const selectAllVisible = () => {
    const newSelection = new Set(selectedIds);
    visibleIds.forEach(id => newSelection.add(id));
    onSelectionChange(newSelection);
  };

  // Deselect all currently visible images
  const deselectAllVisible = () => {
    const newSelection = new Set(selectedIds);
    visibleIds.forEach(id => newSelection.delete(id));
    onSelectionChange(newSelection);
  };

  // Clear all selections
  const clearAllSelections = () => {
    onSelectionChange(new Set());
  };

  // Select all images in the folder (ignoring filters)
  const selectAllInFolder = () => {
    const allIds = new Set(totalImages.map(img => img.id));
    onSelectionChange(allIds);
  };

  if (filteredImages.length === 0) {
    return null;
  }

  return (
    <div className={cn("flex items-center gap-2 p-2 bg-muted/20 rounded-md", className)}>
      {/* Status Display */}
      <div className="flex items-center gap-2">
        <Eye className="h-4 w-4 text-muted-foreground" />
        <span className="text-xs text-muted-foreground">
          Showing {filteredImages.length} of {totalImages.length}
        </span>
        {selectedIds.size > 0 && (
          <Badge variant="secondary" className="bg-primary/10 h-4 px-1.5 text-xs">
            <Users className="h-3 w-3 mr-1" />
            {selectedIds.size} selected
          </Badge>
        )}
      </div>

      {/* Selection Controls */}
      <div className="flex items-center gap-1.5 ml-auto">
        {/* Select/Deselect All Visible */}
        <Button
          size="sm"
          variant={allVisibleSelected ? "default" : "outline"}
          onClick={allVisibleSelected ? deselectAllVisible : selectAllVisible}
          className="flex items-center gap-1.5 h-6 px-2 text-xs"
          disabled={filteredImages.length === 0}
        >
          {allVisibleSelected ? (
            <CheckSquare className="h-3 w-3" />
          ) : (
            <Square className="h-3 w-3" />
          )}
          {allVisibleSelected ? 'Deselect All Visible' : 'Select All Visible'}
          <Badge variant="secondary" className="ml-1 text-xs h-4 px-1">
            {filteredImages.length}
          </Badge>
        </Button>

        {/* Select All in Folder (no filter limit) */}
        {filteredImages.length < totalImages.length && (
          <Button
            size="sm"
            variant="outline"
            onClick={selectAllInFolder}
            className="flex items-center gap-1.5 h-6 px-2 text-xs"
          >
            <CheckSquare className="h-3 w-3" />
            Select All in Folder
            <Badge variant="secondary" className="ml-1 text-xs h-4 px-1">
              {totalImages.length}
            </Badge>
          </Button>
        )}

        {/* Clear All Selections */}
        {selectedIds.size > 0 && (
          <Button
            size="sm"
            variant="ghost"
            onClick={clearAllSelections}
            className="text-muted-foreground hover:text-foreground h-6 px-2 text-xs"
          >
            Clear All ({selectedIds.size})
          </Button>
        )}
      </div>
    </div>
  );
}