#!/usr/bin/env pwsh
# 🚀 Qwen Direct API Client
# Bypasses broken CLI model selection and talks directly to OpenRouter API

param(
    [string]$Model = "qwen/qwen3-coder",
    [string]$Prompt,
    [switch]$Interactive,
    [switch]$Help
)

# Configuration
$API_KEY = "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"
$BASE_URL = "https://openrouter.ai/api/v1/chat/completions"

function Show-Help {
    Write-Host "🚀 Qwen Direct API Client" -ForegroundColor Cyan
    Write-Host "=========================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "This tool bypasses the broken Qwen CLI and talks directly to OpenRouter API." -ForegroundColor Yellow
    Write-Host ""
    Write-Host "USAGE:" -ForegroundColor Yellow
    Write-Host "  qwen-direct.ps1 [OPTIONS]"
    Write-Host ""
    Write-Host "OPTIONS:" -ForegroundColor Yellow
    Write-Host "  -Model <model>     Specify model (default: qwen/qwen3-coder)" -ForegroundColor Green
    Write-Host "  -Prompt <text>     Send a prompt directly" -ForegroundColor Green
    Write-Host "  -Interactive       Start interactive chat mode" -ForegroundColor Green
    Write-Host "  -Help              Show this help" -ForegroundColor Green
    Write-Host ""
    Write-Host "EXAMPLES:" -ForegroundColor Yellow
    Write-Host "  qwen-direct.ps1 -Prompt 'Hello, can you help me code?'" -ForegroundColor Cyan
    Write-Host "  qwen-direct.ps1 -Model 'qwen/qwen3-coder' -Interactive" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "AVAILABLE MODELS:" -ForegroundColor Yellow
    Write-Host "  qwen/qwen3-coder                  - Latest with tool calling (RECOMMENDED)" -ForegroundColor Green
    Write-Host "  qwen/qwen-2.5-coder-32b-instruct - Alternative coder model" -ForegroundColor White
    Write-Host "  qwen/qwen-2.5-72b-instruct       - Larger general model" -ForegroundColor White
}

function Send-ApiRequest {
    param(
        [string]$Model,
        [array]$Messages,
        [int]$MaxTokens = 1000
    )
    
    $body = @{
        model = $Model
        messages = $Messages
        max_tokens = $MaxTokens
        temperature = 0.7
    } | ConvertTo-Json -Depth 10
    
    try {
        $response = Invoke-RestMethod -Uri $BASE_URL -Method POST -Headers @{
            "Authorization" = "Bearer $API_KEY"
            "Content-Type" = "application/json"
        } -Body $body
        
        return $response.choices[0].message.content
    } catch {
        Write-Host "❌ API Error: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response) {
            $errorResponse = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorResponse)
            $errorBody = $reader.ReadToEnd()
            Write-Host "Error details: $errorBody" -ForegroundColor Red
        }
        return $null
    }
}

function Start-InteractiveMode {
    param([string]$Model)
    
    Write-Host ""
    Write-Host "🤖 Qwen Interactive Mode" -ForegroundColor Cyan
    Write-Host "========================" -ForegroundColor Cyan
    Write-Host "Model: $Model" -ForegroundColor Green
    Write-Host "Type 'exit' to quit, 'clear' to clear conversation" -ForegroundColor Yellow
    Write-Host ""
    
    $conversation = @()
    
    # Add system message to configure Qwen as coding assistant
    $conversation += @{
        role = "system"
        content = @"
You are Qwen3-Coder, an advanced AI coding assistant specialized in:
- Agentic Coding: Multi-turn interactions with tools and environment
- Function Calling: Advanced tool use and API integrations  
- Repository Analysis: Understanding large codebases and contexts
- Code Generation: Writing, debugging, and optimizing code
- Software Engineering: Planning, implementing, and testing solutions

Be direct and technical when discussing code. Provide clear explanations for complex concepts.
Show code examples and practical demonstrations. Ask clarifying questions when requirements are unclear.
"@
    }
    
    while ($true) {
        Write-Host "You: " -NoNewline -ForegroundColor Cyan
        $userInput = Read-Host
        
        if ($userInput -eq "exit") {
            Write-Host "Goodbye! 👋" -ForegroundColor Green
            break
        }
        
        if ($userInput -eq "clear") {
            $conversation = $conversation[0] # Keep only system message
            Write-Host "Conversation cleared! 🧹" -ForegroundColor Yellow
            continue
        }
        
        if ([string]::IsNullOrWhiteSpace($userInput)) {
            continue
        }
        
        # Add user message
        $conversation += @{
            role = "user"
            content = $userInput
        }
        
        Write-Host ""
        Write-Host "Qwen: " -ForegroundColor Magenta -NoNewline
        
        $response = Send-ApiRequest -Model $Model -Messages $conversation -MaxTokens 2000
        
        if ($response) {
            Write-Host $response -ForegroundColor White
            
            # Add assistant response to conversation
            $conversation += @{
                role = "assistant"
                content = $response
            }
        } else {
            Write-Host "Failed to get response. Please try again." -ForegroundColor Red
        }
        
        Write-Host ""
    }
}

# Main execution
if ($Help) {
    Show-Help
    exit
}

Write-Host "🚀 Qwen Direct API Client" -ForegroundColor Cyan
Write-Host "==========================" -ForegroundColor Cyan
Write-Host "Model: $Model" -ForegroundColor Green
Write-Host "API: OpenRouter" -ForegroundColor Green
Write-Host ""

if ($Interactive -or (-not $Prompt)) {
    Start-InteractiveMode -Model $Model
} else {
    Write-Host "Sending prompt..." -ForegroundColor Yellow
    
    $messages = @(
        @{
            role = "system"
            content = "You are Qwen3-Coder, an advanced AI coding assistant. Be helpful, direct, and technical."
        },
        @{
            role = "user"
            content = $Prompt
        }
    )
    
    $response = Send-ApiRequest -Model $Model -Messages $messages
    
    if ($response) {
        Write-Host ""
        Write-Host "Response:" -ForegroundColor Green
        Write-Host $response -ForegroundColor White
    } else {
        Write-Host "Failed to get response from API." -ForegroundColor Red
    }
}

Write-Host ""
