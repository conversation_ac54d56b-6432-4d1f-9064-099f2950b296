export interface OCRResult {
  firstName: string;
  lastName: string;
  dateOfBirth: string; // Format: YYYY-MM-DD
  address: string;
  licenseNumber: string;
  expirationDate: string; // Format: YYYY-MM-DD
  issueDate: string; // Format: YYYY-MM-DD
  state: string; // Two-letter state code
  confidence: number;
  rawText: string;
  processingTime: number; // milliseconds
  modelUsed: string;
  cost?: number; // in USD
}

export interface OCRModel {
  id: string;
  name: string;
  provider: 'openai' | 'openrouter' | 'local';
  type: 'free' | 'paid' | 'local';
  endpoint?: string;
  modelName: string;
  description: string;
  accuracy: number; // 0-1
  avgProcessingTime: number; // milliseconds
  costPer1kTokens?: number; // USD
  maxImageSize: number; // bytes
  supportedFormats: string[];
  rateLimit?: {
    requests: number;
    period: number; // seconds
  };
  requirements?: string[];
  isEnabled: boolean;
  priority: number; // 1-10, higher is preferred
}

export interface ModelProvider {
  id: string;
  name: string;
  type: 'api' | 'local';
  baseUrl?: string;
  apiKey?: string;
  models: OCRModel[];
  isConfigured: boolean;
  lastUsed?: string;
  totalCost?: number;
  totalRequests?: number;
}

export interface OCRConfig {
  providers: ModelProvider[];
  defaultModel: string;
  fallbackModels: string[];
  maxRetries: number;
  retryDelay: number;
  timeout: number;
  costLimit: number; // monthly limit in USD
  enableCostTracking: boolean;
  enableRateLimit: boolean;
  batchSize: number;
  enableFallback: boolean;
  localModelPath?: string;
  preferences: {
    prioritizeAccuracy: boolean;
    prioritizeSpeed: boolean;
    prioritizeCost: boolean;
  };
}

export interface ProcessingStats {
  totalProcessed: number;
  successfulProcessed: number;
  failedProcessed: number;
  totalCost: number;
  averageProcessingTime: number;
  modelUsage: Record<string, number>;
  lastProcessed: string;
  monthlyStats: {
    [month: string]: {
      processed: number;
      cost: number;
      errors: number;
    };
  };
}

export interface OCRRequest {
  imageId: string;
  imageData: Buffer | string; // Buffer for local, string for base64
  config?: Partial<OCRConfig>;
  modelOverride?: string;
  metadata?: Record<string, any>;
}

export interface OCRResponse {
  success: boolean;
  result?: OCRResult;
  error?: string;
  modelUsed: string;
  processingTime: number;
  cost?: number;
  confidence: number;
  retryCount: number;
}

export interface ModelCapabilities {
  supportsVision: boolean;
  supportsStructuredOutput: boolean;
  supportsMultimodal: boolean;
  maxTokens: number;
  contextLength: number;
  supportedLanguages: string[];
  accuracyBenchmarks: {
    driverLicense: number;
    generalOCR: number;
    handwriting: number;
  };
}

export interface LocalModelConfig {
  modelPath: string;
  modelType: 'llava' | 'qwen-vl' | 'cogvlm' | 'paddleocr';
  gpuLayers: number;
  contextSize: number;
  batchSize: number;
  precision: 'fp16' | 'fp32' | 'int8';
  enableGPU: boolean;
  vramUsage: number; // GB
  threads: number;
  isLoaded: boolean;
}

export interface RateLimitInfo {
  remaining: number;
  reset: number; // timestamp
  limit: number;
  window: number; // seconds
}

export interface CostTracker {
  dailySpend: number;
  monthlySpend: number;
  totalSpend: number;
  dailyLimit: number;
  monthlyLimit: number;
  transactions: CostTransaction[];
}

export interface CostTransaction {
  timestamp: string;
  modelId: string;
  tokens: number;
  cost: number;
  imageId?: string;
  success: boolean;
}

export interface BatchProcessingJob {
  id: string;
  images: string[];
  modelId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  startTime: string;
  endTime?: string;
  results: OCRResponse[];
  totalCost: number;
  errors: string[];
}