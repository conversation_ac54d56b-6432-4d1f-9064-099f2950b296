# DL Organizer Agents v1.3.1

## Active Specialists (5 Optimized Agents)

🔧 **port-manager** - Windows infrastructure, bulletproof startup system, port management  
🎨 **frontend-guardian** - Next.js 14.2+, React components, Tailwind CSS, TypeScript 5.8+  
🔄 **api-specialist** - Express.js backend, SQLite database, SSE streaming, API integration  
🤖 **ocr-specialist** - Multi-provider OCR (OpenAI GPT-4o, OpenRouter), Smart Analyzer, ReadySearch v3.0  
🧪 **qa-specialist** - Jest unit tests, Playwright E2E, coverage analysis, performance validation  

## Current Project Architecture (v1.3.1)

**Technology Stack:**
- Frontend: Next.js 14.2+ with App Router, TypeScript 5.8+, Tailwind CSS + Radix UI
- Backend: Express.js with SQLite, Sharp image processing, Winston logging
- AI/OCR: OpenAI GPT-4o, OpenRouter models, cost tracking, model validation
- Testing: Jest (unit), Playwright (E2E), SuperTest (integration)
- Windows: Native filesystem APIs, bulletproof startup, port management

**Key Features:**
- Smart Filter Analyzer with AI-powered license side detection
- Power-Filter Workbench with sub-100ms filtering performance
- Multi-provider OCR with real-time model validation
- ReadySearch Australian DL database integration
- Advanced port management and conflict resolution

## Tool Access Matrix

- **port-manager**: Bash, Read, Write, Glob, Grep (Windows system operations)
- **frontend-guardian**: browser, shadcn-ui, Read, Write, Bash (Next.js/React focus)
- **api-specialist**: Read, Write, Bash, Grep, Glob (Express.js/SQLite focus)
- **ocr-specialist**: context7, Read, Write, Bash, Grep (AI/OCR pipeline focus)
- **qa-specialist**: browser, Read, Write, Bash (Jest/Playwright focus)

## Agent Selection Guidelines

- **Port conflicts, startup issues, Windows problems** → port-manager 🔧
- **Next.js, React components, UI/UX, TypeScript** → frontend-guardian 🎨  
- **Express APIs, database, SSE streaming, backend** → api-specialist 🔄
- **OCR processing, AI models, ReadySearch, Smart Analyzer** → ocr-specialist 🤖
- **Jest tests, Playwright E2E, coverage, quality** → qa-specialist 🧪

## Development Commands

```bash
# Development
npm run dev                    # Bulletproof startup
npm run dev:smart             # Smart port management
npm run ports:check           # Port availability check
npm run process:check         # Process monitoring

# Testing  
npm run test                  # Jest unit tests
npm run test:e2e             # Playwright E2E
npm run lint                 # ESLint check
npm run typecheck            # TypeScript validation

# Production
npm run build                # Production build
npm run setup                # Initial setup
npm run health               # Health monitoring
```

## Windows Optimizations

- Use bulletproof startup system for port management
- Leverage existing npm scripts for operations
- Target specific file paths over broad searches
- Use efficient Grep patterns with type filters
- Implement proper Windows filesystem handling