const { spawn } = require('child_process');

console.log('Running TypeScript type checking...\n');

const tscProcess = spawn('npx', ['tsc', '--noEmit'], {
  cwd: process.cwd(),
  stdio: 'inherit',
  shell: true
});

tscProcess.on('error', (error) => {
  console.error('TypeScript check error:', error);
  process.exit(1);
});

tscProcess.on('close', (code) => {
  console.log(`\nTypeScript check exited with code ${code}`);
  if (code === 0) {
    console.log('✅ No TypeScript errors found!');
  } else {
    console.log('❌ TypeScript errors detected.');
  }
  process.exit(code);
});