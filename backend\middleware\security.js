const path = require('path');
const rateLimit = require('express-rate-limit');

// Input validation and sanitization
class SecurityMiddleware {
  // Validate and sanitize file paths to prevent directory traversal
  static validatePath(inputPath) {
    if (!inputPath || typeof inputPath !== 'string') {
      throw new Error('Invalid path provided');
    }
    
    // Handle JSON-mangled Windows paths (missing backslashes)
    let processedPath = inputPath;
    
    // If it looks like a Windows path with missing backslashes, fix it
    if (process.platform === 'win32') {
      // Handle various malformed Windows path patterns
      if (/^[A-Za-z]:[^\\]/.test(processedPath)) {
        // Convert "C:folder\subfolder" to "C:\folder\subfolder"
        processedPath = processedPath.replace(/^([A-Za-z]:)([^\\])/, '$1\\$2');
      }
      
      // Handle the specific case where JSON parsing removes backslashes entirely
      // Convert "C:claudedl-organizer\files" to "C:\claude\dl-organizer\files"
      if (/^[A-Za-z]:claudedl-organizer/.test(processedPath)) {
        processedPath = processedPath.replace(/^([A-Za-z]:)claudedl-organizer/, '$1\\claude\\dl-organizer');
      }
    }
    
    // Normalize path and check for directory traversal attempts
    const normalizedPath = path.normalize(processedPath);
    
    // Enhanced directory traversal detection - distinguish between directory traversal and legitimate filenames
    const directoryTraversalPatterns = [
      '../',    // Unix traversal
      '..\\',   // Windows traversal  
      '/..',    // Hidden unix traversal
      '\\..',   // Hidden windows traversal
      '%2e%2e', // URL encoded ..
      '%2E%2E'  // URL encoded .. (uppercase)
    ];
    
    const hasDirectoryTraversal = directoryTraversalPatterns.some(pattern => 
      normalizedPath.includes(pattern)
    );
    
    if (hasDirectoryTraversal || normalizedPath.includes('~')) {
      throw new Error('Directory traversal attempt detected');
    }
    
    // Windows-specific path validation
    if (process.platform === 'win32') {
      // Check for Windows absolute path
      const windowsAbsoluteRegex = /^[A-Za-z]:[\\\/]|^\\\\[^\\]+\\[^\\]+/;
      
      if (!windowsAbsoluteRegex.test(normalizedPath)) {
        throw new Error(`Path must be absolute Windows path. Got: ${normalizedPath}`);
      }
    } else {
      // Unix-style path validation
      if (!path.isAbsolute(normalizedPath)) {
        throw new Error('Path must be absolute');
      }
    }
    
    return normalizedPath;
  }
  
  // Validate image file extensions
  static validateImageFile(filename) {
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.tiff', '.bmp'];
    const ext = path.extname(filename).toLowerCase();
    
    if (!allowedExtensions.includes(ext)) {
      throw new Error(`Unsupported file type: ${ext}`);
    }
    
    return true;
  }
  
  // Sanitize text input
  static sanitizeText(input) {
    if (typeof input !== 'string') {
      return '';
    }
    
    // Remove potentially dangerous characters
    return input
      .replace(/[<>]/g, '') // Remove HTML tags
      .replace(/[&]/g, '&amp;') // Escape ampersands
      .trim()
      .substring(0, 10000); // Limit length
  }
  
  // Validate OCR data structure
  static validateOCRData(data) {
    const requiredFields = ['firstName', 'lastName', 'licenseNumber'];
    const optionalFields = ['dateOfBirth', 'address', 'expirationDate', 'issueDate', 'state', 'confidence', 'rawText'];
    
    const validatedData = {};
    
    // Check required fields
    for (const field of requiredFields) {
      if (!data[field] || typeof data[field] !== 'string') {
        throw new Error(`Missing or invalid required field: ${field}`);
      }
      validatedData[field] = this.sanitizeText(data[field]);
    }
    
    // Process optional fields
    for (const field of optionalFields) {
      if (data[field] !== undefined) {
        if (field === 'confidence') {
          const confidence = parseFloat(data[field]);
          if (isNaN(confidence) || confidence < 0 || confidence > 1) {
            throw new Error('Confidence must be a number between 0 and 1');
          }
          validatedData[field] = confidence;
        } else if (field === 'dateOfBirth' || field === 'expirationDate' || field === 'issueDate') {
          // Validate date format (YYYY-MM-DD)
          const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
          if (data[field] && !dateRegex.test(data[field])) {
            throw new Error(`Invalid date format for ${field}. Use YYYY-MM-DD`);
          }
          validatedData[field] = data[field];
        } else if (field === 'state') {
          // Validate state code (2 letters)
          const stateRegex = /^[A-Z]{2}$/;
          const stateCode = data[field].toUpperCase();
          if (data[field] && !stateRegex.test(stateCode)) {
            throw new Error('State must be a 2-letter code');
          }
          validatedData[field] = stateCode;
        } else {
          validatedData[field] = this.sanitizeText(data[field]);
        }
      }
    }
    
    return validatedData;
  }
  
  // Rate limiting middleware
  // ULTRA-PERMISSIVE limits for development to handle React Strict Mode double-rendering
  // and bulk operations without false-positive rate limiting
  static createRateLimit(windowMs = 15 * 60 * 1000, max = 100000) {
    return rateLimit({
      windowMs,
      max,
      message: {
        success: false,
        error: 'Too many requests, please try again later'
      },
      standardHeaders: true,
      legacyHeaders: false,
      // Do not count CORS pre-flight requests towards the limit to avoid
      // accidental 429s when the browser issues many OPTIONS requests.
      skip: (req) => req.method === 'OPTIONS'
    });
  }
  
  // OCR-specific rate limiting (ultra-permissive for development)
  static ocrRateLimit = rateLimit({
    windowMs: 60 * 1000, // 1 minute
    max: 10000, // 10000 requests per minute (effectively unlimited for dev)
    message: {
      success: false,
      error: 'OCR rate limit exceeded. Please wait before making more requests.'
    },
    // Skip rate limiting in development for localhost
    skip: (req) => {
      const isLocalhost = req.ip === '127.0.0.1' || req.ip === '::1' || req.ip === '::ffff:127.0.0.1';
      return process.env.NODE_ENV === 'development' && isLocalhost;
    }
  });
  
  // File upload rate limiting
  static uploadRateLimit = rateLimit({
    windowMs: 60 * 1000, // 1 minute
    max: 20, // 20 uploads per minute
    message: {
      success: false,
      error: 'Upload rate limit exceeded. Please wait before uploading more files.'
    }
  });
  
  // Middleware to validate request body
  static validateRequestBody(requiredFields = []) {
    return (req, res, next) => {
      try {
        for (const field of requiredFields) {
          if (!req.body[field]) {
            return res.status(400).json({
              success: false,
              error: `Missing required field: ${field}`
            });
          }
        }
        next();
      } catch (error) {
        res.status(400).json({
          success: false,
          error: error.message
        });
      }
    };
  }
  
  // Middleware to validate file paths in requests
  static validatePathMiddleware(pathField = 'path') {
    return (req, res, next) => {
      try {
        if (req.body[pathField]) {
          req.body[pathField] = this.validatePath(req.body[pathField]);
        }
        if (req.params[pathField]) {
          req.params[pathField] = this.validatePath(req.params[pathField]);
        }
        next();
      } catch (error) {
        res.status(400).json({
          success: false,
          error: error.message
        });
      }
    };
  }
  
  // CORS configuration for local development and ngrok
  static corsOptions = {
    origin: function (origin, callback) {
      // Allow requests with no origin (like mobile apps or curl)
      if (!origin) return callback(null, true);
      
      // Get current ports from port configuration
      const { PORTS } = require('../scripts/port-config.js');
      
      // Allowed origins - include both current ports and legacy ports for compatibility
      const allowedOrigins = [
        // Current configured ports
        `http://localhost:${PORTS.frontend}`,
        `http://127.0.0.1:${PORTS.frontend}`,
        `http://localhost:${PORTS.backend}`,
        `http://127.0.0.1:${PORTS.backend}`,
        // Legacy ports for compatibility
        'http://localhost:3030',
        'http://localhost:3031', 
        'http://localhost:3032',
        'http://127.0.0.1:3030',
        'http://127.0.0.1:3031',
        'http://127.0.0.1:3032',
        // Additional common development ports
        'http://localhost:3110',
        'http://127.0.0.1:3110',
        'http://localhost:3574',
        'http://127.0.0.1:3574'
      ];
      
      // Allow ngrok URLs
      if (origin.includes('.ngrok-free.app') || origin.includes('.ngrok.io')) {
        return callback(null, true);
      }
      
      // Check if origin is in allowed list
      if (allowedOrigins.includes(origin)) {
        return callback(null, true);
      }
      
      // Reject other origins
      callback(new Error('Not allowed by CORS'));
    },
    credentials: true,
    optionsSuccessStatus: 200
  };
  
  // Security headers middleware
  static securityHeaders(req, res, next) {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    
    // Remove server information
    res.removeHeader('X-Powered-By');
    
    next();
  }
}

module.exports = SecurityMiddleware;