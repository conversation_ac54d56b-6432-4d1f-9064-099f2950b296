# Test backend API directly
$body = @{
    folderPath = "C:\claude\dl-organizer\test-samples\i18"
    recursive = $true
    generateThumbnails = $false
} | ConvertTo-Json

Write-Host "Testing API with body: $body"

try {
    $response = Invoke-RestMethod -Uri "http://127.0.0.1:3574/api/filesystem/images" -Method POST -Body $body -ContentType "application/json"
    Write-Host "API Response:"
    $response | ConvertTo-Json -Depth 5
} catch {
    Write-Host "Error: $_"
    Write-Host "Response: $($_.Exception.Response)"
}