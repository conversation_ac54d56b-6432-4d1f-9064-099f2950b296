import { useReducer, useCallback } from 'react';
import { Filter, FilterStack, FilterAction } from '@/types/filters';

// Filter reducer with exhaustive type checking
function filterReducer(state: FilterStack, action: FilterAction): FilterStack {
  switch (action.type) {
    case 'add':
      if (!action.filter) return state;
      return [...state, action.filter];
    
    case 'remove':
      if (action.index === undefined || action.index < 0 || action.index >= state.length) {
        return state;
      }
      return state.filter((_, i) => i !== action.index);
    
    case 'reset':
      return [];
    
    case 'replace':
      if (!action.filters) return state;
      return [...action.filters];
    
    default:
      return state;
  }
}

export function useFilterReducer() {
  const [filters, dispatch] = useReducer(filterReducer, [] as FilterStack);

  const addFilter = useCallback((filter: Filter) => {
    dispatch({ type: 'add', filter });
  }, []);

  const removeFilter = useCallback((index: number) => {
    dispatch({ type: 'remove', index });
  }, []);

  const clearAllFilters = useCallback(() => {
    dispatch({ type: 'reset' });
  }, []);

  const replaceFilters = useCallback((newFilters: Filter[]) => {
    dispatch({ type: 'replace', filters: newFilters });
  }, []);

  return {
    filters,
    addFilter,
    removeFilter,
    clearAllFilters,
    replaceFilters
  };
}