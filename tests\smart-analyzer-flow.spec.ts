import { test, expect } from '@playwright/test';

test.describe('DL Organizer Fixed Functionality Tests', () => {
  test('should verify health endpoint is working', async ({ page }) => {
    // Test the health endpoint
    const response = await page.request.get('http://localhost:3738/api/health');
    expect(response.ok()).toBeTruthy();
    
    const health = await response.json();
    expect(health.status).toBe('healthy');
    console.log('✅ Backend health check passed');
  });

  test('should verify Smart Analyzer API creates jobs successfully', async ({ page }) => {
    // Test Smart Analyzer API directly
    const response = await page.request.post('http://localhost:3079/api/smart-analyzer/analyze', {
      data: {
        folderPath: 'C:/claude/dl-organizer/files/combined_to_do/100',
        forceRefresh: true
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const result = await response.json();
    
    expect(result.jobId).toBeDefined();
    expect(result.totalImages).toBeGreaterThan(0);
    expect(result.status).toBe('started');
    
    console.log(`✅ Smart Analyzer job created: ${result.jobId} with ${result.totalImages} images`);
  });

  test('should verify SSE stream connections work', async ({ page }) => {
    // First create a job
    const createResponse = await page.request.post('http://localhost:3079/api/smart-analyzer/analyze', {
      data: {
        folderPath: 'C:/claude/dl-organizer/files/combined_to_do/100',
        forceRefresh: true
      }
    });
    
    expect(createResponse.ok()).toBeTruthy();
    const { jobId } = await createResponse.json();
    
    // Test SSE stream connection
    const streamResponse = await page.request.get(`http://localhost:3079/api/smart-analyzer/stream/${jobId}`, {
      headers: {
        'Accept': 'text/event-stream',
        'Cache-Control': 'no-cache'
      }
    });
    
    expect(streamResponse.ok()).toBeTruthy();
    expect(streamResponse.headers()['content-type']).toContain('text/event-stream');
    
    console.log(`✅ SSE stream connection established for job: ${jobId}`);
  });

  test('should verify Windows path normalization', async ({ page }) => {
    // Test that forward slash paths work
    const response = await page.request.post('http://localhost:3079/api/smart-analyzer/analyze', {
      data: {
        folderPath: 'C:/claude/dl-organizer/files/combined_to_do/100',
        forceRefresh: true
      }
    });
    
    expect(response.ok()).toBeTruthy();
    const result = await response.json();
    
    expect(result.jobId).toBeDefined();
    expect(result.totalImages).toBe(207); // Expected number of images in test folder
    
    console.log('✅ Windows path normalization working correctly');
  });

  test('should load the main page without errors', async ({ page }) => {
    // Start by going to the home page
    await page.goto('http://localhost:3079');

    // Wait for page to load
    await page.waitForLoadState('networkidle');
    
    // Verify page loads without errors
    const title = await page.title();
    expect(title).toContain('DL Organizer');
    
    console.log('✅ Main page loads successfully');
    
    // Look for key elements to ensure the app is working
    const newProjectButton = page.locator('button:has-text("New Project")');
    await expect(newProjectButton).toBeVisible({ timeout: 10000 });
  });
});