const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const { spawn } = require('child_process');

class WindowsFileSystemUtils {
  // Windows-specific file locking retry logic
  static async withRetry(operation, maxRetries = 5, baseDelay = 100) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        // Check if it's a Windows file locking error
        if (error.code === 'EBUSY' || error.code === 'ENOENT' || error.code === 'EPERM') {
          if (attempt === maxRetries) {
            throw new Error(`Operation failed after ${maxRetries} attempts: ${error.message}`);
          }
          
          // Exponential backoff with jitter
          const delay = baseDelay * Math.pow(2, attempt - 1) + Math.random() * 100;
          await this.sleep(delay);
          continue;
        }
        throw error;
      }
    }
  }
  
  static sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  
  // Check if path is on a network drive
  static isNetworkPath(filePath) {
    return filePath.startsWith('\\\\') || /^[A-Z]:\\/.test(filePath) === false;
  }
  
  // Get drive type (local, network, removable)
  static async getDriveType(driveLetter) {
    return new Promise((resolve) => {
      const cmd = `wmic logicaldisk where "DeviceID='${driveLetter}:'" get DriveType /value`;
      const child = spawn('cmd', ['/c', cmd], { shell: true });
      
      let output = '';
      child.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      child.on('close', (code) => {
        const match = output.match(/DriveType=(\d+)/);
        if (match) {
          const driveType = parseInt(match[1]);
          const types = {
            2: 'removable',
            3: 'local',
            4: 'network',
            5: 'cd'
          };
          resolve(types[driveType] || 'unknown');
        } else {
          resolve('unknown');
        }
      });
    });
  }
  
  // Enhanced directory scanning with Windows optimizations
  static async scanDirectoryOptimized(rootPath, options = {}) {
    const {
      maxDepth = 10,
      includeHidden = false,
      followSymlinks = false,
      batchSize = 100
    } = options;
    
    const results = [];
    const queue = [{ path: rootPath, depth: 0 }];
    
    // Check drive type for optimization
    const driveLetter = path.parse(rootPath).root.charAt(0);
    const driveType = await this.getDriveType(driveLetter);
    
    // Adjust batch size based on drive type
    const effectiveBatchSize = driveType === 'network' ? 50 : batchSize;
    
    while (queue.length > 0) {
      const batch = queue.splice(0, effectiveBatchSize);
      
      await Promise.all(batch.map(async ({ path: currentPath, depth }) => {
        if (depth > maxDepth) return;
        
        try {
          const entries = await this.withRetry(async () => {
            return await fs.readdir(currentPath, { withFileTypes: true });
          });
          
          for (const entry of entries) {
            // Skip hidden files unless requested
            if (!includeHidden && entry.name.startsWith('.')) {
              continue;
            }
            
            const fullPath = path.join(currentPath, entry.name);
            
            if (entry.isDirectory()) {
              const folderInfo = await this.getFolderInfo(fullPath, rootPath, { 
                calculateSize: false // Skip expensive size calculation during scanning
              });
              results.push(folderInfo);
              
              // Add to queue for recursive scanning
              queue.push({ path: fullPath, depth: depth + 1 });
            } else if (entry.isSymbolicLink() && followSymlinks) {
              try {
                const realPath = await fs.realpath(fullPath);
                const stats = await fs.stat(realPath);
                if (stats.isDirectory()) {
                  queue.push({ path: realPath, depth: depth + 1 });
                }
              } catch (error) {
                console.warn(`Could not follow symlink: ${fullPath}`, error.message);
              }
            }
          }
        } catch (error) {
          console.warn(`Could not scan directory: ${currentPath}`, error.message);
        }
      }));
    }
    
    return results;
  }
  
  // Get comprehensive folder information
  static async getFolderInfo(folderPath, rootPath, options = {}) {
    const { calculateSize = true } = options;
    const folderInfo = {
      id: Buffer.from(folderPath).toString('base64'),
      name: path.basename(folderPath),
      path: folderPath,
      relativePath: path.relative(rootPath, folderPath),
      isDirectory: true,
      imageCount: 0,
      hasTextFile: false,
      tags: [],
      metadata: {
        created: null,
        modified: null,
        size: 0,
        permissions: null
      }
    };
    
    try {
      // Get folder stats
      const stats = await this.withRetry(async () => {
        return await fs.stat(folderPath);
      });
      
      folderInfo.metadata.created = stats.birthtime;
      folderInfo.metadata.modified = stats.mtime;
      
      // Scan folder contents
      const contents = await this.withRetry(async () => {
        return await fs.readdir(folderPath);
      });
      
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.tiff', '.bmp'];
      const textExtensions = ['.txt', '.md', '.rtf'];
      
      folderInfo.imageCount = contents.filter(file => 
        imageExtensions.includes(path.extname(file).toLowerCase())
      ).length;
      
      folderInfo.hasTextFile = contents.some(file => 
        textExtensions.includes(path.extname(file).toLowerCase())
      );
      
      // Calculate total size of images (only if requested - expensive operation)
      if (calculateSize) {
        let totalSize = 0;
        for (const file of contents) {
          if (imageExtensions.includes(path.extname(file).toLowerCase())) {
            try {
              const filePath = path.join(folderPath, file);
              const fileStats = await fs.stat(filePath);
              totalSize += fileStats.size;
            } catch (error) {
              // Skip files that can't be accessed
            }
          }
        }
        folderInfo.metadata.size = totalSize;
      }
      
    } catch (error) {
      console.warn(`Could not get folder info for: ${folderPath}`, error.message);
    }
    
    return folderInfo;
  }
  
  // Enhanced image file detection with metadata
  static async getImageFiles(folderPath, options = {}) {
    const { includeMetadata = true, generateHashes = false } = options;
    const sharp = require('sharp');
    
    try {
      const entries = await this.withRetry(async () => {
        return await fs.readdir(folderPath, { withFileTypes: true });
      });
      
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.tiff', '.bmp'];
      const images = [];
      
      for (const entry of entries) {
        if (entry.isFile() && imageExtensions.includes(path.extname(entry.name).toLowerCase())) {
          const fullPath = path.join(folderPath, entry.name);
          
          const imageInfo = {
            id: Buffer.from(fullPath).toString('base64'),
            filename: entry.name,
            path: fullPath,
            extension: path.extname(entry.name).toLowerCase(),
            relativePath: path.relative(folderPath, fullPath),
            width: 0,
            height: 0,
            fileSize: 0,
            lastModified: new Date(),
            rotation: 0,
            // Use cache-key based URLs consistent with image-processor.js
            thumbnailUrl: this.generateThumbnailUrl(fullPath),
            previewUrl: this.generatePreviewUrl(fullPath),
            folderId: Buffer.from(folderPath).toString('base64')
          };
          
          if (includeMetadata) {
            try {
              const stats = await this.withRetry(async () => {
                return await fs.stat(fullPath);
              });
              
              imageInfo.fileSize = stats.size;
              imageInfo.lastModified = stats.mtime;
              imageInfo.created = stats.birthtime;
              
              // Extract image dimensions using Sharp
              try {
                const metadata = await sharp(fullPath).metadata();
                imageInfo.width = metadata.width || 0;
                imageInfo.height = metadata.height || 0;
              } catch (sharpError) {
                console.warn(`Could not extract image dimensions for: ${fullPath}`, sharpError.message);
                // Keep default values of 0
              }
            } catch (error) {
              console.warn(`Could not get stats for image: ${fullPath}`, error.message);
            }
          }
          
          images.push(imageInfo);
        }
      }
      
      return images.sort((a, b) => a.filename.localeCompare(b.filename));
    } catch (error) {
      console.error(`Error reading images from folder: ${folderPath}`, error);
      return [];
    }
  }
  
  // Safe file operations with Windows-specific handling
  static async safeFileOperation(operation, filePath, maxRetries = 3) {
    return await this.withRetry(async () => {
      // Check if file is locked by another process
      try {
        const fd = await fs.open(filePath, 'r+');
        await fd.close();
      } catch (error) {
        if (error.code === 'EBUSY') {
          throw new Error(`File is locked by another process: ${filePath}`);
        }
      }
      
      return await operation();
    }, maxRetries);
  }
  
  // Create backup before destructive operations
  static async createBackup(filePath) {
    const backupPath = `${filePath}.backup.${Date.now()}`;
    try {
      await this.withRetry(async () => {
        await fs.copyFile(filePath, backupPath);
      });
      return backupPath;
    } catch (error) {
      console.warn(`Could not create backup for: ${filePath}`, error.message);
      return null;
    }
  }
  
  // Clean up old backup files
  static async cleanupBackups(directory, maxAge = 7 * 24 * 60 * 60 * 1000) { // 7 days
    try {
      const entries = await fs.readdir(directory);
      const now = Date.now();
      
      for (const entry of entries) {
        if (entry.includes('.backup.')) {
          const filePath = path.join(directory, entry);
          const stats = await fs.stat(filePath);
          
          if (now - stats.mtime.getTime() > maxAge) {
            await fs.unlink(filePath);
            console.log(`Cleaned up old backup: ${filePath}`);
          }
        }
      }
    } catch (error) {
      console.warn(`Error cleaning up backups in: ${directory}`, error.message);
    }
  }
  
  // Check available disk space
  static async getAvailableSpace(drivePath) {
    return new Promise((resolve) => {
      const drive = path.parse(drivePath).root;
      const cmd = `dir "${drive}" /-c | find "bytes free"`;
      
      const child = spawn('cmd', ['/c', cmd], { shell: true });
      
      let output = '';
      child.stdout.on('data', (data) => {
        output += data.toString();
      });
      
      child.on('close', (code) => {
        const match = output.match(/(\d+) bytes free/);
        if (match) {
          resolve(parseInt(match[1]));
        } else {
          resolve(null);
        }
      });
    });
  }
  
  // Validate Windows path format
  static validateWindowsPath(inputPath) {
    // Check for valid Windows path format
    const windowsPathRegex = /^[A-Za-z]:\\|^\\\\[^\\]+\\[^\\]+/;
    
    if (!windowsPathRegex.test(inputPath)) {
      throw new Error('Invalid Windows path format');
    }
    
    // Check for invalid characters
    const invalidChars = /[<>:"|?*]/;
    if (invalidChars.test(inputPath)) {
      throw new Error('Path contains invalid characters');
    }
    
    // Check path length (Windows has a 260 character limit for full paths)
    if (inputPath.length > 260) {
      throw new Error('Path exceeds Windows maximum length (260 characters)');
    }
    
    return path.normalize(inputPath);
  }

  // Generate cache key for thumbnails (consistent with ImageProcessor)
  static generateCacheKey(imagePath, operation, params = {}) {
    const hash = crypto.createHash('md5');
    hash.update(imagePath);
    hash.update(operation);
    hash.update(JSON.stringify(params));
    return hash.digest('hex');
  }

  // Generate thumbnail URL (consistent with ImageProcessor)
  static generateThumbnailUrl(imagePath) {
    const thumbnailSize = 200;
    const quality = 85;
    const cacheKey = this.generateCacheKey(imagePath, 'thumbnail', { size: thumbnailSize, quality });
    return `/thumbnails/${cacheKey}.jpg`;
  }

  // Generate preview URL (consistent with ImageProcessor)
  static generatePreviewUrl(imagePath) {
    const maxWidth = 800;
    const maxHeight = 600;
    const quality = 85;
    const cacheKey = this.generateCacheKey(imagePath, 'preview', { maxWidth, maxHeight, quality });
    return `/previews/${cacheKey}.jpg`;
  }
}

module.exports = WindowsFileSystemUtils;