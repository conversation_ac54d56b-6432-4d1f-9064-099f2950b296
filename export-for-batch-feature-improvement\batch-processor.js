const EventEmitter = require('events');
const path = require('path');
const fs = require('fs').promises;

class BatchProcessor extends EventEmitter {
  constructor(ocrService, localModelService, costTracker) {
    super();
    this.ocrService = ocrService;
    this.localModelService = localModelService;
    this.costTracker = costTracker;
    this.jobs = new Map();
    this.isProcessing = false;
    this.maxConcurrentJobs = 3;
    this.activeJobs = 0;
    this.queue = [];
  }

  async createJob(options) {
    const job = {
      id: this.generateJobId(),
      ...options,
      status: 'pending',
      progress: 0,
      startTime: null,
      endTime: null,
      results: [],
      errors: [],
      totalCost: 0,
      processedCount: 0
    };

    this.jobs.set(job.id, job);
    this.queue.push(job.id);
    
    // Start processing if not already running
    if (!this.isProcessing) {
      this.processQueue();
    }

    return job;
  }

  generateJobId() {
    return `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  async processQueue() {
    if (this.isProcessing || this.queue.length === 0) return;
    
    this.isProcessing = true;
    
    while (this.queue.length > 0 && this.activeJobs < this.maxConcurrentJobs) {
      const jobId = this.queue.shift();
      const job = this.jobs.get(jobId);
      
      if (job && job.status === 'pending') {
        this.activeJobs++;
        this.processJob(job);
      }
    }
    
    // Check if we need to stop processing
    if (this.activeJobs === 0 && this.queue.length === 0) {
      this.isProcessing = false;
    }
  }

  async processJob(job) {
    try {
      job.status = 'processing';
      job.startTime = new Date().toISOString();
      
      this.emit('jobStarted', job);
      
      const { images, modelId, extractionType = 'driver_license', options = {} } = job;
      
      for (let i = 0; i < images.length; i++) {
        const imageId = images[i];
        
        try {
          // Check cost and rate limits before processing
          const limitsCheck = await this.checkLimits(modelId, options.estimatedCost || 0);
          if (!limitsCheck.allowed) {
            throw new Error(`Limits exceeded: ${limitsCheck.violations.map(v => v.type).join(', ')}`);
          }
          
          // Load image data
          const imageData = await this.loadImageData(imageId);
          
          // Process with OCR
          const result = await this.ocrService.processImage(imageData, {
            modelOverride: modelId,
            extractionType,
            ...options
          });
          
          job.results.push({
            imageId,
            ...result
          });
          
          if (result.success) {
            job.totalCost += result.cost || 0;
            job.processedCount++;
            
            // Record cost tracking
            if (result.cost) {
              await this.costTracker.recordTransaction({
                modelId: result.modelUsed,
                providerId: result.modelUsed.split('-')[0] || 'unknown',
                cost: result.cost,
                tokens: 0,
                success: true,
                extractionType,
                imageId,
                batchJobId: job.id
              });
            }
            
            // Auto-save if enabled
            if (options.autoSave) {
              await this.saveOCRResult(imageId, result.result);
            }
          } else {
            job.errors.push({
              imageId,
              error: result.error || 'Unknown error'
            });
          }
          
          // Update progress
          job.progress = Math.round(((i + 1) / images.length) * 100);
          this.emit('jobProgress', job);
          
          // Respect rate limits
          if (options.delayBetweenRequests) {
            await this.sleep(options.delayBetweenRequests);
          }
          
        } catch (error) {
          job.errors.push({
            imageId,
            error: error.message
          });
          
          // Continue processing other images unless it's a critical error
          if (error.message.includes('Limits exceeded') || 
              error.message.includes('Rate limit')) {
            // Stop processing this job
            job.status = 'failed';
            job.endTime = new Date().toISOString();
            this.emit('jobFailed', job);
            break;
          }
        }
      }
      
      // Mark job as completed
      if (job.status === 'processing') {
        job.status = 'completed';
        job.endTime = new Date().toISOString();
        this.emit('jobCompleted', job);
      }
      
    } catch (error) {
      job.status = 'failed';
      job.endTime = new Date().toISOString();
      job.errors.push({
        error: error.message,
        critical: true
      });
      this.emit('jobFailed', job);
    } finally {
      this.activeJobs--;
      this.processQueue(); // Continue processing remaining jobs
    }
  }

  async checkLimits(modelId, estimatedCost) {
    // Check cost limits
    const costCheck = this.costTracker.checkLimits(modelId, 'batch', estimatedCost);
    
    // Check rate limits
    const model = this.ocrService.findModel(modelId);
    let rateCheck = { allowed: true };
    
    if (model && model.rateLimit) {
      rateCheck = this.costTracker.checkRateLimit(modelId, model.rateLimit);
    }
    
    return {
      allowed: costCheck.allowed && rateCheck.allowed,
      violations: [
        ...(costCheck.violations || []),
        ...(rateCheck.allowed ? [] : [{ type: 'rate_limit', resetAt: rateCheck.resetAt }])
      ]
    };
  }

  async loadImageData(imageId) {
    // This would typically load image data from database or filesystem
    // For now, we'll assume imageId is a file path
    try {
      return await fs.readFile(imageId);
    } catch (error) {
      throw new Error(`Failed to load image ${imageId}: ${error.message}`);
    }
  }

  async saveOCRResult(imageId, ocrResult) {
    // Save OCR result to database and/or generate TXT file
    // This would integrate with your existing save logic
    try {
      // Generate TXT file content
      const txtContent = this.generateTxtContent(ocrResult);
      
      // Save to folder (assuming imageId contains path info)
      const imagePath = path.dirname(imageId);
      const txtPath = path.join(imagePath, 'ocr_results.txt');
      
      await fs.writeFile(txtPath, txtContent, 'utf8');
      
      return { success: true, txtPath };
    } catch (error) {
      console.error('Failed to save OCR result:', error);
      return { success: false, error: error.message };
    }
  }

  generateTxtContent(ocrResult) {
    return `
Driver's License Information
===========================

Name: ${ocrResult.firstName} ${ocrResult.lastName}
Date of Birth: ${ocrResult.dateOfBirth}
License Number: ${ocrResult.licenseNumber}
State: ${ocrResult.state}
Issue Date: ${ocrResult.issueDate}
Expiration Date: ${ocrResult.expirationDate}
Address: ${ocrResult.address}

Confidence: ${(ocrResult.confidence * 100).toFixed(1)}%
Processed: ${new Date().toISOString()}

Raw Text:
${ocrResult.rawText}
`.trim();
  }

  async cancelJob(jobId) {
    const job = this.jobs.get(jobId);
    if (!job) {
      throw new Error('Job not found');
    }
    
    if (job.status === 'processing') {
      job.status = 'cancelled';
      job.endTime = new Date().toISOString();
      this.emit('jobCancelled', job);
    }
    
    return job;
  }

  async retryJob(jobId) {
    const job = this.jobs.get(jobId);
    if (!job) {
      throw new Error('Job not found');
    }
    
    if (job.status === 'failed' || job.status === 'cancelled') {
      // Reset job state
      job.status = 'pending';
      job.progress = 0;
      job.startTime = null;
      job.endTime = null;
      job.results = [];
      job.errors = [];
      job.totalCost = 0;
      job.processedCount = 0;
      
      // Add back to queue
      this.queue.push(jobId);
      this.processQueue();
    }
    
    return job;
  }

  getJob(jobId) {
    return this.jobs.get(jobId);
  }

  getAllJobs() {
    return Array.from(this.jobs.values()).sort((a, b) => 
      new Date(b.startTime || 0).getTime() - new Date(a.startTime || 0).getTime()
    );
  }

  getJobsByStatus(status) {
    return this.getAllJobs().filter(job => job.status === status);
  }

  deleteJob(jobId) {
    const job = this.jobs.get(jobId);
    if (job && job.status !== 'processing') {
      this.jobs.delete(jobId);
      return true;
    }
    return false;
  }

  getQueueStatus() {
    return {
      queueLength: this.queue.length,
      activeJobs: this.activeJobs,
      maxConcurrentJobs: this.maxConcurrentJobs,
      isProcessing: this.isProcessing,
      totalJobs: this.jobs.size,
      completedJobs: this.getJobsByStatus('completed').length,
      failedJobs: this.getJobsByStatus('failed').length,
      pendingJobs: this.getJobsByStatus('pending').length
    };
  }

  async sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Batch processing for specific scenarios
  async processFolderImages(folderPath, options = {}) {
    try {
      // Scan folder for images
      const files = await fs.readdir(folderPath);
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.tiff'];
      
      const imageFiles = files.filter(file => 
        imageExtensions.includes(path.extname(file).toLowerCase())
      ).map(file => path.join(folderPath, file));
      
      if (imageFiles.length === 0) {
        throw new Error('No images found in folder');
      }
      
      // Create batch job
      const job = await this.createJob({
        images: imageFiles,
        modelId: options.modelId || 'llama-3.2-90b-vision-instruct:free',
        extractionType: options.extractionType || 'driver_license',
        options: {
          autoSave: options.autoSave !== false,
          delayBetweenRequests: options.delayBetweenRequests || 2000,
          ...options
        }
      });
      
      return job;
    } catch (error) {
      throw new Error(`Failed to process folder: ${error.message}`);
    }
  }

  async generateReport(jobId) {
    const job = this.jobs.get(jobId);
    if (!job) {
      throw new Error('Job not found');
    }
    
    const successfulResults = job.results.filter(r => r.success);
    const failedResults = job.results.filter(r => !r.success);
    
    const report = {
      jobId: job.id,
      status: job.status,
      duration: job.endTime ? 
        new Date(job.endTime).getTime() - new Date(job.startTime).getTime() : null,
      totalImages: job.images.length,
      successfullyProcessed: successfulResults.length,
      failed: failedResults.length,
      totalCost: job.totalCost,
      averageCostPerImage: job.processedCount > 0 ? job.totalCost / job.processedCount : 0,
      averageConfidence: successfulResults.length > 0 ? 
        successfulResults.reduce((sum, r) => sum + (r.result?.confidence || 0), 0) / successfulResults.length : 0,
      errors: job.errors,
      completionRate: (successfulResults.length / job.images.length) * 100
    };
    
    return report;
  }
}

module.exports = BatchProcessor;