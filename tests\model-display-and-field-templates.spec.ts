import { test, expect } from '@playwright/test'

test.describe('Model Display and Field Templates', () => {
  test.beforeEach(async ({ page }) => {
    // Start the application and wait for it to load
    await page.goto('http://localhost:3000')
    
    // Wait for the page to load
    await page.waitForLoadState('networkidle')
    
    // Create a test project if we're on the project overview page
    // Try clicking the "Create First Project" button first (more prominent)
    const createFirstButton = page.locator('button:has-text("Create First Project")')
    const newProjectButton = page.locator('button:has-text("New Project")').first()
    
    if (await createFirstButton.isVisible()) {
      await createFirstButton.click()
    } else if (await newProjectButton.isVisible()) {
      await newProjectButton.click()
    }
    
    // Check if we opened the project creation dialog
    const projectNameInput = page.locator('input[placeholder*="project name"], input[placeholder*="Project name"]')
    if (await projectNameInput.isVisible({ timeout: 5000 })) {
      await projectNameInput.fill('Test Project')
      await page.fill('input[placeholder*="folder path"], input[placeholder*="Folder path"]', 'C:\\claude\\dl-organizer\\files')
      await page.click('button:has-text("Create Project"), button:has-text("Create")')
      await page.waitForTimeout(3000)
      
      // Wait for project to load and show main interface
      await page.waitForSelector('h1', { timeout: 10000 })
    }
  })

  test('should display current model dynamically on main page', async ({ page }) => {
    // Wait for main page to load
    await page.waitForSelector('h1')
    
    // Check if the current model display is visible
    const modelDisplay = page.locator('div:has-text("Current Model:")')
    await expect(modelDisplay).toBeVisible()
    
    // Check if model badges are present
    const modelBadges = page.locator('[class*="badge"]').filter({ hasText: /Gemini|GPT|Claude|Llama|Qwen/ })
    await expect(modelBadges.first()).toBeVisible()
    
    // Check if provider badge is present
    const providerBadges = page.locator('[class*="badge"]').filter({ hasText: /Google|OpenAI|Anthropic|Meta|Qwen/ })
    await expect(providerBadges.first()).toBeVisible()
    
    // Check if connection status indicator is present
    const connectionIndicator = page.locator('div[title*="Connection:"]')
    await expect(connectionIndicator).toBeVisible()
  })

  test('should show model display in OCR panel', async ({ page }) => {
    // Navigate to OCR panel
    await page.click('text=OCR')
    await page.waitForTimeout(1000)
    
    // Check if model display is present in OCR panel
    const ocrModelDisplay = page.locator('div:has-text("Model:")')
    await expect(ocrModelDisplay).toBeVisible()
    
    // Check if model badges are present in OCR panel
    const modelBadges = page.locator('[class*="badge"]').filter({ hasText: /Gemini|GPT|Claude|Llama|Qwen/ })
    await expect(modelBadges.first()).toBeVisible()
    
    // Check if "Change Model" button is present
    const changeModelButton = page.locator('button:has-text("Change Model")')
    await expect(changeModelButton).toBeVisible()
  })

  test('should open and display field extraction templates in settings', async ({ page }) => {
    // Open settings
    const settingsButton = page.locator('button:has-text("Settings")')
    await expect(settingsButton).toBeVisible()
    await settingsButton.click()
    
    // Wait for settings dialog to open
    await page.waitForSelector('text=Application Settings')
    
    // Click on General tab
    await page.click('button[role="tab"]:has-text("General")')
    await page.waitForTimeout(1000)
    
    // Check if Field Extraction Templates section is visible
    const templatesSection = page.locator('text=Field Extraction Templates')
    await expect(templatesSection).toBeVisible()
    
    // Check if document type tabs are present
    await expect(page.locator('button:has-text("US Driver License")')).toBeVisible()
    await expect(page.locator('button:has-text("Australian Driver License")')).toBeVisible()
    await expect(page.locator('button:has-text("Passport")')).toBeVisible()
    await expect(page.locator('button:has-text("Auto-Detect")')).toBeVisible()
  })

  test('should display US driver license fields correctly', async ({ page }) => {
    // Open settings and navigate to general tab
    await page.click('button:has-text("Settings")')
    await page.waitForSelector('text=Application Settings')
    await page.click('button[role="tab"]:has-text("General")')
    
    // Ensure US Driver License tab is selected (should be default)
    await page.click('button:has-text("US Driver License")')
    
    // Check for US-specific fields
    await expect(page.locator('text=First Name *')).toBeVisible()
    await expect(page.locator('text=Middle Name')).toBeVisible()
    await expect(page.locator('text=Last Name *')).toBeVisible()
    await expect(page.locator('text=Date of Birth *')).toBeVisible()
    await expect(page.locator('text=License Number *')).toBeVisible()
    await expect(page.locator('text=State *')).toBeVisible()
    await expect(page.locator('text=Issue Date')).toBeVisible()
    await expect(page.locator('text=Expiration Date')).toBeVisible()
    
    // Check for Edit Prompt button
    const editPromptButton = page.locator('button:has-text("Edit Prompt")').first()
    await expect(editPromptButton).toBeVisible()
  })

  test('should display Australian driver license fields correctly', async ({ page }) => {
    // Open settings and navigate to general tab
    await page.click('button:has-text("Settings")')
    await page.waitForSelector('text=Application Settings')
    await page.click('button[role="tab"]:has-text("General")')
    
    // Click on Australian Driver License tab
    await page.click('button:has-text("Australian Driver License")')
    
    // Check for Australian-specific fields
    await expect(page.locator('text=Surname *')).toBeVisible()
    await expect(page.locator('text=Given Names *')).toBeVisible()
    await expect(page.locator('text=Driver License Number *')).toBeVisible()
    await expect(page.locator('text=Card Number')).toBeVisible()
    await expect(page.locator('text=State/Territory *')).toBeVisible()
    
    // Check for the "Recently Added: Card Number Field" highlight
    await expect(page.locator('text=Recently Added: Card Number Field')).toBeVisible()
    
    // Check for Edit Prompt button
    const editPromptButton = page.locator('button:has-text("Edit Prompt")').first()
    await expect(editPromptButton).toBeVisible()
  })

  test('should open prompt editor when clicking Edit Prompt', async ({ page }) => {
    // Open settings and navigate to general tab
    await page.click('button:has-text("Settings")')
    await page.waitForSelector('text=Application Settings')
    await page.click('button[role="tab"]:has-text("General")')
    
    // Click Edit Prompt button for US Driver License
    const editPromptButton = page.locator('button:has-text("Edit Prompt")').first()
    await editPromptButton.click()
    
    // Wait for prompt editor dialog to open
    await page.waitForSelector('text=Edit US Driver License Template')
    
    // Check if prompt editor sections are visible
    await expect(page.locator('text=Field Configuration')).toBeVisible()
    await expect(page.locator('text=AI Prompt Template')).toBeVisible()
    await expect(page.locator('text=Standard Fields')).toBeVisible()
    await expect(page.locator('text=Custom Fields')).toBeVisible()
    
    // Check if prompt textarea is visible
    const promptTextarea = page.locator('textarea[id="promptEditor"]')
    await expect(promptTextarea).toBeVisible()
    
    // Check if the textarea contains some content (the US prompt)
    const textareaContent = await promptTextarea.textContent()
    expect(textareaContent).toContain('US driver\'s license')
    expect(textareaContent).toContain('firstName')
    expect(textareaContent).toContain('lastName')
  })

  test('should allow adding custom fields', async ({ page }) => {
    // Open settings and navigate to general tab
    await page.click('button:has-text("Settings")')
    await page.waitForSelector('text=Application Settings')
    await page.click('button[role="tab"]:has-text("General")')
    
    // Click Edit Prompt button for US Driver License
    const editPromptButton = page.locator('button:has-text("Edit Prompt")').first()
    await editPromptButton.click()
    
    // Wait for prompt editor dialog to open
    await page.waitForSelector('text=Edit US Driver License Template')
    
    // Click Add Field button
    await page.click('button:has-text("Add Field")')
    
    // Fill in custom field details
    await page.fill('input[id="fieldName"]', 'Emergency Contact')
    await page.fill('input[id="fieldDescription"]', 'Emergency contact information if present')
    
    // Select field type
    await page.click('button[role="combobox"]')
    await page.click('text=Text')
    
    // Check required checkbox
    await page.check('input[id="fieldRequired"]')
    
    // Click Add Field button in the form
    await page.click('button:has-text("Add Field"):not(:has-text("Cancel"))')
    
    // Verify the custom field was added
    await expect(page.locator('text=Emergency Contact')).toBeVisible()
    
    // Check if it shows as a custom field (should have blue styling)
    const customFieldBadge = page.locator('[class*="bg-blue-100"]:has-text("Emergency Contact")')
    await expect(customFieldBadge).toBeVisible()
  })

  test('should allow removing custom fields', async ({ page }) => {
    // Open settings and navigate to general tab
    await page.click('button:has-text("Settings")')
    await page.waitForSelector('text=Application Settings')
    await page.click('button[role="tab"]:has-text("General")')
    
    // Click Edit Prompt button for US Driver License
    const editPromptButton = page.locator('button:has-text("Edit Prompt")').first()
    await editPromptButton.click()
    
    // Wait for prompt editor dialog to open
    await page.waitForSelector('text=Edit US Driver License Template')
    
    // Add a custom field first
    await page.click('button:has-text("Add Field")')
    await page.fill('input[id="fieldName"]', 'Test Field')
    await page.fill('input[id="fieldDescription"]', 'Test field for removal')
    await page.click('button:has-text("Add Field"):not(:has-text("Cancel"))')
    
    // Verify the field was added
    await expect(page.locator('text=Test Field')).toBeVisible()
    
    // Click the delete button (trash icon) for the custom field
    const deleteButton = page.locator('button:has([data-testid="trash-icon"], svg[class*="lucide-trash"])').first()
    await deleteButton.click()
    
    // Verify the field was removed
    await expect(page.locator('text=Test Field')).not.toBeVisible()
  })

  test('should allow editing and previewing prompts', async ({ page }) => {
    // Open settings and navigate to general tab
    await page.click('button:has-text("Settings")')
    await page.waitForSelector('text=Application Settings')
    await page.click('button[role="tab"]:has-text("General")')
    
    // Click Edit Prompt button for US Driver License
    const editPromptButton = page.locator('button:has-text("Edit Prompt")').first()
    await editPromptButton.click()
    
    // Wait for prompt editor dialog to open
    await page.waitForSelector('text=Edit US Driver License Template')
    
    // Get the prompt textarea and add some custom text
    const promptTextarea = page.locator('textarea[id="promptEditor"]')
    await promptTextarea.fill('This is a test prompt for the US driver license template.')
    
    // Click Preview button
    await page.click('button:has-text("Preview")')
    
    // Check if preview mode is active and shows the custom text
    await expect(page.locator('text=Preview')).toBeVisible()
    await expect(page.locator('text=This is a test prompt for the US driver license template.')).toBeVisible()
    
    // Click Edit button to go back to edit mode
    await page.click('button:has-text("Edit")')
    
    // Verify we're back in edit mode
    await expect(promptTextarea).toBeVisible()
    
    // Click Reset button to restore default
    await page.click('button:has-text("Reset")')
    
    // Verify the prompt was reset (should contain original content)
    const resetContent = await promptTextarea.textContent()
    expect(resetContent).toContain('US driver\'s license')
  })

  test('should display different templates for different document types', async ({ page }) => {
    // Open settings and navigate to general tab
    await page.click('button:has-text("Settings")')
    await page.waitForSelector('text=Application Settings')
    await page.click('button[role="tab"]:has-text("General")')
    
    // Test US Driver License template
    await page.click('button:has-text("US Driver License")')
    await expect(page.locator('text=First Name *')).toBeVisible()
    
    // Test Australian Driver License template
    await page.click('button:has-text("Australian Driver License")')
    await expect(page.locator('text=Surname *')).toBeVisible()
    await expect(page.locator('text=Given Names *')).toBeVisible()
    await expect(page.locator('text=Card Number')).toBeVisible()
    
    // Test Passport template
    await page.click('button:has-text("Passport")')
    await expect(page.locator('text=Passport Number *')).toBeVisible()
    await expect(page.locator('text=Nationality')).toBeVisible()
    await expect(page.locator('text=Place of Birth')).toBeVisible()
    
    // Test Auto-Detect template
    await page.click('button:has-text("Auto-Detect")')
    await expect(page.locator('text=Supported Document Types')).toBeVisible()
    await expect(page.locator('text=Auto-Detection Features')).toBeVisible()
    await expect(page.locator('text=Document type confidence scoring')).toBeVisible()
  })

  test('should update model display when settings change', async ({ page }) => {
    // Get initial model display
    const initialModelDisplay = page.locator('div:has-text("Current Model:")').first()
    await expect(initialModelDisplay).toBeVisible()
    
    // Open settings
    await page.click('button:has-text("Settings")')
    await page.waitForSelector('text=Application Settings')
    
    // Should be on OpenRouter AI tab by default
    // Try to change the model selection
    const modelSelect = page.locator('button[role="combobox"]').first()
    if (await modelSelect.isVisible()) {
      await modelSelect.click()
      
      // Select a different model
      const modelOption = page.locator('text=GPT-4o Mini').first()
      if (await modelOption.isVisible()) {
        await modelOption.click()
        
        // Save the configuration
        await page.click('button:has-text("Save Configuration")')
        await page.waitForTimeout(2000)
        
        // Close settings dialog
        await page.click('button:has-text("Close")')
        
        // Verify the model display updated
        await expect(page.locator('text=GPT-4o Mini')).toBeVisible()
      }
    }
  })

  test('should show custom field configuration section', async ({ page }) => {
    // Open settings and navigate to general tab
    await page.click('button:has-text("Settings")')
    await page.waitForSelector('text=Application Settings')
    await page.click('button[role="tab"]:has-text("General")')
    
    // Check if Custom Field Configuration section is visible
    await expect(page.locator('text=Custom Field Configuration')).toBeVisible()
    await expect(page.locator('button:has-text("Add Custom Field")')).toBeVisible()
    await expect(page.locator('text=Add custom fields to extract additional information')).toBeVisible()
  })
})