# Test frontend debug API
$body = @{
    folderPath = "C:\claude\dl-organizer\test-samples\i18"
    recursive = $false
} | ConvertTo-Json

Write-Host "Testing frontend debug API with body: $body"

try {
    $response = Invoke-RestMethod -Uri "http://127.0.0.1:3110/api/filesystem/images-debug" -Method POST -Body $body -ContentType "application/json"
    Write-Host "Frontend Debug API Response:"
    $response | ConvertTo-Json -Depth 5
} catch {
    Write-Host "Frontend Debug API Error: $_"
    if ($_.Exception.Response) {
        $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
        $responseBody = $reader.ReadToEnd()
        Write-Host "Response Body: $responseBody"
    }
}