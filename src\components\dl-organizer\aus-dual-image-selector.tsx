"use client"

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ImageFile } from '@/types'
import Image from 'next/image'
import { FileImage, X } from 'lucide-react'
import { cn } from '@/lib/utils'

interface AUSDualImageSelectorProps {
  images: ImageFile[]
  onSelectFront: (image: ImageFile | null) => void
  onSelectBack: (image: ImageFile | null) => void
  selectedFront: ImageFile | null
  selectedBack: ImageFile | null
  className?: string
}

export function AUSDualImageSelector({
  images,
  onSelectFront,
  onSelectBack,
  selectedFront,
  selectedBack,
  className
}: AUSDualImageSelectorProps) {
  const [selectingFor, setSelectingFor] = useState<'front' | 'back' | null>(null)

  const handleImageSelect = (image: ImageFile) => {
    if (selectingFor === 'front') {
      onSelectFront(image)
    } else if (selectingFor === 'back') {
      onSelectBack(image)
    }
    setSelectingFor(null)
  }

  const handleRemove = (side: 'front' | 'back') => {
    if (side === 'front') {
      onSelectFront(null)
    } else {
      onSelectBack(null)
    }
  }

  return (
    <div className={cn("space-y-4", className)}>
      <div className="grid grid-cols-2 gap-4">
        {/* Front Side */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <span className="text-xl">🇦🇺</span>
              Front Side
            </CardTitle>
          </CardHeader>
          <CardContent>
            {selectedFront ? (
              <div className="relative group">
                <div className="aspect-video relative overflow-hidden rounded-md bg-muted" style={{ position: 'relative' }}>
                  <Image
                    src={selectedFront.thumbnailUrl}
                    alt="Front side"
                    fill
                    className="object-cover"
                  />
                </div>
                <Button
                  variant="destructive"
                  size="icon"
                  className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => handleRemove('front')}
                >
                  <X className="h-4 w-4" />
                </Button>
                <p className="text-xs text-muted-foreground mt-2 truncate">
                  {selectedFront.filename}
                </p>
              </div>
            ) : (
              <Button
                variant="outline"
                className="w-full h-32 flex flex-col items-center justify-center gap-2"
                onClick={() => setSelectingFor('front')}
              >
                <FileImage className="h-8 w-8 text-muted-foreground" />
                <span className="text-sm">Select Front</span>
              </Button>
            )}
          </CardContent>
        </Card>

        {/* Back Side */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <span className="text-xl">🇦🇺</span>
              Back Side
            </CardTitle>
          </CardHeader>
          <CardContent>
            {selectedBack ? (
              <div className="relative group">
                <div className="aspect-video relative overflow-hidden rounded-md bg-muted" style={{ position: 'relative' }}>
                  <Image
                    src={selectedBack.thumbnailUrl}
                    alt="Back side"
                    fill
                    className="object-cover"
                  />
                </div>
                <Button
                  variant="destructive"
                  size="icon"
                  className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => handleRemove('back')}
                >
                  <X className="h-4 w-4" />
                </Button>
                <p className="text-xs text-muted-foreground mt-2 truncate">
                  {selectedBack.filename}
                </p>
              </div>
            ) : (
              <Button
                variant="outline"
                className="w-full h-32 flex flex-col items-center justify-center gap-2"
                onClick={() => setSelectingFor('back')}
              >
                <FileImage className="h-8 w-8 text-muted-foreground" />
                <span className="text-sm">Select Back</span>
              </Button>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Image selector modal */}
      {selectingFor && (
        <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
          <div className="fixed inset-4 z-50 overflow-hidden">
            <Card className="h-full flex flex-col">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Select {selectingFor === 'front' ? 'Front' : 'Back'} Side</span>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => setSelectingFor(null)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="flex-1 overflow-auto">
                <div className="grid grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {images.map((image) => (
                    <button
                      key={image.id}
                      className={cn(
                        "relative aspect-square overflow-hidden rounded-md bg-muted transition-all hover:ring-2 hover:ring-primary",
                        (selectingFor === 'front' && selectedFront?.id === image.id) ||
                        (selectingFor === 'back' && selectedBack?.id === image.id)
                          ? "ring-2 ring-primary"
                          : ""
                      )}
                      style={{ position: 'relative' }}
                      onClick={() => handleImageSelect(image)}
                    >
                      <Image
                        src={image.thumbnailUrl}
                        alt={image.filename}
                        fill
                        className="object-cover"
                      />
                    </button>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      )}
    </div>
  )
}