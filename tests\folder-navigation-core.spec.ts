import { test, expect } from '@playwright/test';
import { ensureTestProject, deleteTestProject, createSampleImages } from './test-utils';

test.describe('Enhanced Image Grid - Core Folder Navigation', () => {
  const testProjectName = 'Core Navigation Test';
  const testProjectPath = 'C:\\claude\\dl-organizer\\test-samples\\core-nav-test';

  test.beforeAll(async () => {
    // Set up test project with sample images and folder structure
    await ensureTestProject(testProjectName, testProjectPath);
    await createSampleImages(testProjectPath, 6);
    
    // Create nested folder structure
    const fs = require('fs/promises');
    const path = require('path');
    
    try {
      const subFolder1 = path.join(testProjectPath, 'subfolder-1');
      const subFolder2 = path.join(testProjectPath, 'subfolder-2');
      
      await fs.mkdir(subFolder1, { recursive: true });
      await fs.mkdir(subFolder2, { recursive: true });
      
      await createSampleImages(subFolder1, 4);
      await createSampleImages(subFolder2, 3);
      
      console.log('Core navigation test structure created');
    } catch (error) {
      console.log('Test structure setup error:', error);
    }
  });

  test.afterAll(async () => {
    await deleteTestProject(testProjectPath);
  });

  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:3423');
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(1500);
  });

  test('core folder navigation workflow', async ({ page }) => {
    console.log('Testing core folder navigation workflow...');
    
    // Step 1: Open or create test project
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    let projectOpened = false;
    
    if (cardCount > 0) {
      // Look for existing test project
      for (let i = 0; i < cardCount; i++) {
        const projectCard = projectCards.nth(i);
        const projectTitle = await projectCard.locator('h3, .project-title, [data-testid="project-title"]').textContent();
        
        if (projectTitle?.includes('Core Navigation') || projectTitle?.includes('nav-test')) {
          console.log('Found existing test project, opening...');
          await projectCard.locator('button:has-text("Open")').click();
          await page.waitForTimeout(2000);
          projectOpened = true;
          break;
        }
      }
    }
    
    if (!projectOpened) {
      // Create new project
      const newProjectButton = page.locator('button:has-text("New Project"), button:has-text("Create Project")');
      if (await newProjectButton.isVisible()) {
        console.log('Creating new test project...');
        await newProjectButton.click();
        await page.waitForTimeout(1000);
        
        const nameInput = page.locator('input[placeholder*="name"], input[name="name"]').first();
        if (await nameInput.isVisible()) {
          await nameInput.fill(testProjectName);
        }
        
        const pathInput = page.locator('input[placeholder*="path"], input[placeholder*="folder"], input[name="path"]').first();
        if (await pathInput.isVisible()) {
          await pathInput.fill(testProjectPath);
        }
        
        const createButton = page.locator('button:has-text("Create"), button:has-text("Save")');
        if (await createButton.isVisible()) {
          await createButton.click();
          await page.waitForTimeout(3000);
          projectOpened = true;
        }
      }
    }
    
    expect(projectOpened).toBeTruthy();
    console.log('✓ Project opened successfully');
    
    // Step 2: Verify folder tree is displayed
    await page.waitForTimeout(2000);
    
    const folderElements = page.locator(
      '[data-testid="folder-item"], .folder-item, [class*="folder"], ' +
      'button:has([class*="folder"]), [role="treeitem"]'
    );
    const folderCount = await folderElements.count();
    
    console.log(`Found ${folderCount} folder elements`);
    expect(folderCount).toBeGreaterThan(0);
    console.log('✓ Folder tree displayed');
    
    // Step 3: Click on a folder to select it
    const firstFolder = folderElements.first();
    const folderName = await firstFolder.textContent();
    console.log(`Clicking on folder: ${folderName?.slice(0, 50)}...`);
    
    await firstFolder.click();
    await page.waitForTimeout(2000);
    console.log('✓ Folder clicked successfully');
    
    // Step 4: Verify enhanced image grid loads
    await page.waitForTimeout(2000);
    
    // Look for images or empty state
    const images = page.locator('img, [data-testid="image-item"], [class*="image-item"]');
    const imageCount = await images.count();
    
    const emptyState = page.locator(
      ':text("No images"), :text("empty"), :text("contains no images")'
    );
    const hasEmptyState = await emptyState.isVisible();
    
    if (imageCount > 0) {
      console.log(`✓ Images loaded: ${imageCount} images found`);
      
      // Step 5: Test image interaction
      const firstImage = images.first();
      await firstImage.click();
      await page.waitForTimeout(1000);
      console.log('✓ Image click interaction successful');
      
    } else if (hasEmptyState) {
      console.log('✓ Empty state properly displayed');
    } else {
      console.log('! No images or empty state found, but folder was selected');
    }
    
    // Step 6: Test basic controls if available
    const searchBox = page.locator('input[placeholder*="search"], input[type="search"]');
    const searchVisible = await searchBox.isVisible();
    
    if (searchVisible) {
      console.log('Testing search functionality...');
      await searchBox.fill('test');
      await page.waitForTimeout(1000);
      await searchBox.clear();
      console.log('✓ Search functionality works');
    }
    
    console.log('✓ Core folder navigation workflow completed successfully');
  });

  test('folder selection changes image display', async ({ page }) => {
    console.log('Testing folder selection changes image display...');
    
    // Navigate to project
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(2000);
    }
    
    // Get available folders
    const folderElements = page.locator(
      '[data-testid="folder-item"], .folder-item, [class*="folder"], ' +
      'button:has([class*="folder"])'
    );
    const totalFolders = await folderElements.count();
    
    console.log(`Testing selection across ${totalFolders} folders`);
    
    // Test selecting different folders
    for (let i = 0; i < Math.min(totalFolders, 2); i++) {
      const folder = folderElements.nth(i);
      const folderText = await folder.textContent();
      
      console.log(`Selecting folder ${i + 1}: ${folderText?.slice(0, 30)}...`);
      await folder.click();
      await page.waitForTimeout(1500);
      
      // Verify the image display updates
      const images = page.locator('img, [data-testid="image-item"]');
      const imageCount = await images.count();
      
      console.log(`  - Images found: ${imageCount}`);
      
      // Each folder selection should result in some response (images or empty state)
      const emptyState = page.locator(':text("No images"), :text("empty")');
      const hasEmptyState = await emptyState.isVisible();
      
      expect(imageCount >= 0 || hasEmptyState).toBeTruthy();
    }
    
    console.log('✓ Folder selection changes image display test completed');
  });

  test('image grid basic functionality', async ({ page }) => {
    console.log('Testing image grid basic functionality...');
    
    // Navigate to project with images
    const projectCards = page.locator('[data-testid="project-card"]');
    const cardCount = await projectCards.count();
    
    if (cardCount > 0) {
      await projectCards.first().locator('button:has-text("Open")').click();
      await page.waitForTimeout(2000);
    }
    
    // Select folder
    const folderElements = page.locator('[data-testid="folder-item"], .folder-item');
    const folderCount = await folderElements.count();
    
    if (folderCount > 0) {
      await folderElements.first().click();
      await page.waitForTimeout(2000);
    }
    
    // Test basic grid functionality
    const images = page.locator('img');
    const imageCount = await images.count();
    
    if (imageCount > 0) {
      console.log(`Testing grid with ${imageCount} images`);
      
      // Test image click
      const firstImage = images.first();
      await firstImage.click();
      await page.waitForTimeout(1000);
      console.log('✓ Image click works');
      
      // Look for grid controls
      const filterButton = page.locator('button:has-text("Filter")');
      if (await filterButton.isVisible()) {
        await filterButton.click();
        await page.waitForTimeout(500);
        await filterButton.click();
        console.log('✓ Filter toggle works');
      }
      
      // Look for pagination if present
      const paginationControls = page.locator('[class*="pagination"], button:has-text("Next")');
      const hasPagination = await paginationControls.count() > 0;
      console.log(`Pagination available: ${hasPagination}`);
      
      console.log('✓ Image grid basic functionality verified');
    } else {
      console.log('No images to test grid functionality');
    }
  });
});