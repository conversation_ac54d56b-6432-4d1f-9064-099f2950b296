@echo off
title DL Organizer - Performance Optimizer
chcp 65001 >nul 2>&1

echo.
echo DL Organizer Performance Optimizer
echo ====================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Python not found. Please install Python first.
    pause
    exit /b 1
)

echo Python detected
echo.

REM Run performance monitor
echo Running performance analysis...
echo.
python "C:\claude\dl-organizer\performance-monitor.py"

echo.
echo ====================================
echo QUICK FIXES FOR SLOW CLAUDE CODE:
echo.
echo 1. Kill stuck sessions:
echo    taskkill /F /IM node.exe /T
echo.
echo 2. Start with agent routing:
echo    win-claude-code "infrastructure-specialist: Fix startup issues"
echo.
echo 3. Use token-efficient commands:
echo    win-claude-code "ui-design-specialist: Fix TypeScript only"
echo.
echo 4. Check memory usage:
echo    tasklist /FI "IMAGENAME eq node.exe"
echo.
echo ====================================
pause
