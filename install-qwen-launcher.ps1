#!/usr/bin/env pwsh
# 🚀 Qwen Launcher Installer
# Adds qwen command to your PATH for system-wide access

Write-Host "🚀 Qwen Launcher Installer" -ForegroundColor Cyan
Write-Host "==========================" -ForegroundColor Cyan
Write-Host ""

$qwenLauncherDir = "C:\claude\dl-organizer"
$pathToAdd = $qwenLauncherDir

# Check if directory exists
if (-not (Test-Path $pathToAdd)) {
    Write-Host "❌ Directory not found: $pathToAdd" -ForegroundColor Red
    exit 1
}

# Check if files exist
$requiredFiles = @("qwen.bat", "qwen-launcher.ps1")
foreach ($file in $requiredFiles) {
    if (-not (Test-Path "$pathToAdd\$file")) {
        Write-Host "❌ Required file not found: $file" -ForegroundColor Red
        exit 1
    }
}

Write-Host "✅ All required files found" -ForegroundColor Green

# Get current PATH
$currentPath = [Environment]::GetEnvironmentVariable("Path", "User")

# Check if already in PATH
if ($currentPath -like "*$pathToAdd*") {
    Write-Host "✅ Qwen Launcher directory already in PATH" -ForegroundColor Green
} else {
    Write-Host "📝 Adding Qwen Launcher to user PATH..." -ForegroundColor Yellow
    
    # Add to user PATH
    $newPath = $currentPath + ";" + $pathToAdd
    [Environment]::SetEnvironmentVariable("Path", $newPath, "User")
    
    Write-Host "✅ Added to PATH: $pathToAdd" -ForegroundColor Green
}

Write-Host ""
Write-Host "🎉 Installation Complete!" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green
Write-Host ""
Write-Host "You can now use these commands anywhere:" -ForegroundColor White
Write-Host "  qwen              - Start Qwen with qwen/qwen3-coder model" -ForegroundColor Cyan
Write-Host "  qwen models       - Show available models" -ForegroundColor Cyan  
Write-Host "  qwen settings     - Open settings menu" -ForegroundColor Cyan
Write-Host "  qwen help         - Show help" -ForegroundColor Cyan
Write-Host ""
Write-Host "⚠️  IMPORTANT: Restart your terminal/PowerShell to use the new commands!" -ForegroundColor Yellow
Write-Host ""
Write-Host "📋 What was installed:" -ForegroundColor White
Write-Host "  • qwen.bat - Quick launcher script" -ForegroundColor Gray
Write-Host "  • qwen-launcher.ps1 - Advanced launcher with model selection" -ForegroundColor Gray
Write-Host "  • Added C:\claude\dl-organizer to your PATH" -ForegroundColor Gray
Write-Host ""

# Test the installation
Write-Host "🧪 Testing installation..." -ForegroundColor Yellow
try {
    $testResult = & "$pathToAdd\qwen.bat" help 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Installation test passed!" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Installation test had issues, but files are in place" -ForegroundColor Yellow
    }
} catch {
    Write-Host "⚠️  Could not test installation, but files are configured" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🚀 Ready to use Qwen Launcher!" -ForegroundColor Magenta
Write-Host "   Restart your terminal and type: qwen" -ForegroundColor White
