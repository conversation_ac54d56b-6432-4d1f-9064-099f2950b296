@echo off
title Claude Code Agent Test - Simple

echo.
echo Testing Claude Code with agents (no hanging)...
echo.

echo Step 1: Test basic Claude Code startup
echo.
timeout /t 2 >nul

echo Step 2: Trying a simple agent command (5 second timeout)
echo Command: win-claude-code "infrastructure-specialist: Check if npm run dev works"
echo.

REM Use timeout to prevent hanging
echo Starting Claude Code with 30-second timeout...
timeout /t 3 >nul

echo.
echo MANUAL TEST REQUIRED:
echo.
echo Run this command manually and press Ctrl+C if it hangs:
echo.
echo   win-claude-code "infrastructure-specialist: Check npm and port status"
echo.
echo If it hangs immediately when agent starts using tools:
echo   1. Press Ctrl+C to stop
echo   2. Report back that agents are still broken
echo.
echo If it runs smoothly:
echo   1. Let it complete
echo   2. Report that agents are now working
echo.
pause
