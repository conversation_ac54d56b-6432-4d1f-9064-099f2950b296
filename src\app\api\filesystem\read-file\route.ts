import { NextRequest, NextResponse } from 'next/server'
import fs from 'fs'
import path from 'path'

export async function POST(request: NextRequest) {
  try {
    const { filePath } = await request.json()

    if (!filePath) {
      return NextResponse.json(
        { error: 'File path is required' }, 
        { status: 400 }
      )
    }

    // Security check: ensure file path is safe
    const normalizedPath = path.normalize(filePath)
    
    // Basic security checks
    if (normalizedPath.includes('..') || !path.isAbsolute(normalizedPath)) {
      return NextResponse.json(
        { error: 'Invalid file path' }, 
        { status: 400 }
      )
    }

    // Check if file exists
    if (!fs.existsSync(normalizedPath)) {
      return NextResponse.json(
        { error: 'File not found' }, 
        { status: 404 }
      )
    }

    // Read file content
    const content = fs.readFileSync(normalizedPath, 'utf-8')

    return NextResponse.json({
      success: true,
      content,
      filePath: normalizedPath
    })

  } catch (error) {
    console.error('Error reading file:', error)
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    return NextResponse.json(
      { error: `Failed to read file: ${errorMessage}` }, 
      { status: 500 }
    )
  }
}