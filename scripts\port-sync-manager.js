#!/usr/bin/env node

/**
 * Unified Port Synchronization Manager
 * 
 * This is the single source of truth for all port management in the DL Organizer system.
 * It ensures perfect synchronization between:
 * - PowerShell launcher (launcher.ps1)
 * - Backend server (backend/server.js)
 * - Frontend Next.js (API routes)
 * - Package.json scripts
 * - Environment variables
 * 
 * Key Features:
 * - Single source of truth: data/port-config.json
 * - Automatic conflict resolution
 * - Real-time port discovery and allocation
 * - Cross-process synchronization
 * - Health checking and validation
 * - Emergency fallback strategies
 */

const fs = require('fs');
const path = require('path');
const { exec, spawn } = require('child_process');
const util = require('util');
const net = require('net');

const execAsync = util.promisify(exec);

class UnifiedPortSyncManager {
    constructor() {
        this.projectRoot = path.resolve(__dirname, '..');
        this.configPath = path.join(this.projectRoot, 'data', 'port-config.json');
        this.lockPath = path.join(this.projectRoot, 'data', '.port-lock');
        
        // Default port configuration
        this.defaultPorts = {
            frontend: 3030,
            backend: 3003,
            ngrok: 4040
        };
        
        // Ensure data directory exists
        const dataDir = path.dirname(this.configPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }
    }

    /**
     * Get current port configuration with real-time validation
     * @returns {Promise<Object>} Current port configuration
     */
    async getCurrentConfig() {
        try {
            // Check for existing configuration
            if (fs.existsSync(this.configPath)) {
                const config = JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
                
                // Validate the configuration is still valid (ports not taken by others)
                const validationResult = await this.validateConfiguration(config);
                
                if (validationResult.valid) {
                    console.log('✅ Using existing valid port configuration');
                    return config;
                } else {
                    console.log('⚠️ Existing configuration invalid, resolving conflicts...');
                    // Configuration invalid, need to resolve
                    return await this.resolveAndCreateConfig();
                }
            } else {
                console.log('📋 No existing configuration found, creating new one...');
                return await this.resolveAndCreateConfig();
            }
        } catch (error) {
            console.error('❌ Error reading port configuration:', error.message);
            return await this.createFallbackConfig();
        }
    }

    /**
     * Validate that a port configuration is still valid
     * @param {Object} config - Port configuration to validate
     * @returns {Promise<Object>} Validation result
     */
    async validateConfiguration(config) {
        const ports = config.ports || {};
        const conflicts = [];
        
        // Check if our configured ports are still available or in use by our processes
        for (const [service, port] of Object.entries(ports)) {
            if (await this.isPortInUse(port)) {
                const processInfo = await this.getPortProcessInfo(port);
                
                // Check if it's our own process (acceptable)
                if (processInfo && this.isOurProcess(processInfo)) {
                    console.log(`✅ Port ${port} (${service}) in use by our process: ${processInfo.name}`);
                } else {
                    console.log(`⚠️ Port ${port} (${service}) taken by external process: ${processInfo ? processInfo.name : 'unknown'}`);
                    conflicts.push({ service, port, processInfo });
                }
            }
        }
        
        return {
            valid: conflicts.length === 0,
            conflicts,
            timestamp: new Date().toISOString()
        };
    }

    /**
     * Check if a process belongs to our project
     * @param {Object} processInfo - Process information
     * @returns {boolean} True if it's our process
     */
    isOurProcess(processInfo) {
        if (!processInfo.commandLine) return false;
        
        const ourIndicators = [
            'dl-organizer',
            'backend/server.js',
            'next dev',
            'nodemon',
            this.projectRoot.replace(/\\/g, '/')
        ];
        
        return ourIndicators.some(indicator => 
            processInfo.commandLine.toLowerCase().includes(indicator.toLowerCase())
        );
    }

    /**
     * Advanced port availability check
     * @param {number} port - Port to check
     * @returns {Promise<boolean>} True if port is in use
     */
    async isPortInUse(port) {
        return new Promise((resolve) => {
            const server = net.createServer();
            
            server.listen(port, () => {
                server.once('close', () => resolve(false));
                server.close();
            });
            
            server.on('error', () => resolve(true));
            
            // Timeout protection
            setTimeout(() => {
                server.close();
                resolve(true);
            }, 1000);
        });
    }

    /**
     * Get detailed process information for a port
     * @param {number} port - Port number
     * @returns {Promise<Object|null>} Process information
     */
    async getPortProcessInfo(port) {
        try {
            // Use netstat to find the process
            const { stdout } = await execAsync(`netstat -ano | findstr ":${port}"`, { timeout: 3000 });
            const lines = stdout.trim().split('\n');
            
            if (lines.length > 0) {
                const parts = lines[0].trim().split(/\s+/);
                const pid = parseInt(parts[parts.length - 1]);
                
                if (!isNaN(pid)) {
                    // Get process details
                    try {
                        const { stdout: processInfo } = await execAsync(
                            `powershell -Command "Get-Process -Id ${pid} -ErrorAction SilentlyContinue | Select-Object Name, ProcessName, CommandLine | ConvertTo-Json"`,
                            { timeout: 2000 }
                        );
                        
                        const processData = JSON.parse(processInfo);
                        return {
                            pid,
                            name: processData.ProcessName || processData.Name,
                            commandLine: processData.CommandLine || ''
                        };
                    } catch (processError) {
                        return { pid, name: 'unknown', commandLine: '' };
                    }
                }
            }
        } catch (error) {
            // Port not in use or error getting info
        }
        
        return null;
    }

    /**
     * Find an available port starting from a base port
     * @param {number} basePort - Starting port
     * @param {number} maxTries - Maximum attempts
     * @param {Array<number>} excludePorts - Ports to avoid
     * @returns {Promise<number>} Available port
     */
    async findAvailablePort(basePort, maxTries = 50, excludePorts = []) {
        for (let port = basePort; port < basePort + maxTries; port++) {
            if (excludePorts.includes(port)) continue;
            
            if (!(await this.isPortInUse(port))) {
                return port;
            }
        }
        
        throw new Error(`No available port found in range ${basePort}-${basePort + maxTries}`);
    }

    /**
     * Resolve port conflicts and create new configuration
     * @returns {Promise<Object>} New port configuration
     */
    async resolveAndCreateConfig() {
        console.log('🔍 Resolving port conflicts and creating configuration...');
        
        const config = {
            ports: {},
            environment: {},
            processInfo: {},
            timestamp: new Date().toISOString(),
            pid: process.pid
        };
        
        const allocatedPorts = [];
        
        // Resolve ports for each service
        for (const [service, defaultPort] of Object.entries(this.defaultPorts)) {
            console.log(`🔍 Resolving port for ${service} (default: ${defaultPort})...`);
            
            let assignedPort = defaultPort;
            
            if (await this.isPortInUse(defaultPort)) {
                const processInfo = await this.getPortProcessInfo(defaultPort);
                
                if (processInfo && this.isOurProcess(processInfo)) {
                    console.log(`✅ Port ${defaultPort} (${service}) already in use by our process`);
                    assignedPort = defaultPort;
                } else {
                    console.log(`⚠️ Port ${defaultPort} (${service}) in use by: ${processInfo ? processInfo.name : 'unknown process'}`);
                    
                    // Find alternative port
                    try {
                        assignedPort = await this.findAvailablePort(defaultPort + 1, 50, allocatedPorts);
                        console.log(`✅ Found alternative port ${assignedPort} for ${service}`);
                    } catch (error) {
                        console.warn(`❌ Could not find alternative for ${service}, using default ${defaultPort}`);
                        assignedPort = defaultPort;
                    }
                }
            } else {
                console.log(`✅ Port ${defaultPort} (${service}) is available`);
            }
            
            config.ports[service] = assignedPort;
            allocatedPorts.push(assignedPort);
        }
        
        // Create environment variables
        config.environment = {
            FRONTEND_PORT: config.ports.frontend.toString(),
            BACKEND_PORT: config.ports.backend.toString(),
            NGROK_PORT: config.ports.ngrok.toString(),
            PORT: config.ports.frontend.toString()
        };
        
        // Add process information for tracking
        config.processInfo = {
            managerPid: process.pid,
            nodeVersion: process.version,
            platform: process.platform
        };
        
        // Save configuration
        await this.saveConfiguration(config);
        
        console.log('✅ Port configuration resolved and saved!');
        console.log(`📊 Final ports: Frontend=${config.ports.frontend}, Backend=${config.ports.backend}, Ngrok=${config.ports.ngrok}`);
        
        return config;
    }

    /**
     * Create fallback configuration when all else fails
     * @returns {Promise<Object>} Fallback configuration
     */
    async createFallbackConfig() {
        console.log('🆘 Creating fallback port configuration...');
        
        const config = {
            ports: this.defaultPorts,
            environment: {
                FRONTEND_PORT: this.defaultPorts.frontend.toString(),
                BACKEND_PORT: this.defaultPorts.backend.toString(),
                NGROK_PORT: this.defaultPorts.ngrok.toString(),
                PORT: this.defaultPorts.frontend.toString()
            },
            processInfo: {
                managerPid: process.pid,
                nodeVersion: process.version,
                platform: process.platform,
                fallback: true
            },
            timestamp: new Date().toISOString()
        };
        
        await this.saveConfiguration(config);
        return config;
    }

    /**
     * Save configuration to file with locking
     * @param {Object} config - Configuration to save
     * @returns {Promise<void>}
     */
    async saveConfiguration(config) {
        try {
            // Simple file locking mechanism
            if (fs.existsSync(this.lockPath)) {
                console.log('⏳ Waiting for configuration lock...');
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            // Create lock
            fs.writeFileSync(this.lockPath, process.pid.toString());
            
            // Write configuration
            fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
            
            // Remove lock
            if (fs.existsSync(this.lockPath)) {
                fs.unlinkSync(this.lockPath);
            }
            
            console.log(`✅ Configuration saved to ${this.configPath}`);
        } catch (error) {
            console.error('❌ Failed to save configuration:', error.message);
            
            // Remove lock on error
            if (fs.existsSync(this.lockPath)) {
                fs.unlinkSync(this.lockPath);
            }
            
            throw error;
        }
    }

    /**
     * Update all system components with new port configuration
     * @param {Object} config - Port configuration
     * @returns {Promise<boolean>} Success status
     */
    async updateSystemComponents(config) {
        console.log('🔄 Updating system components with new port configuration...');
        
        const updateResults = await Promise.allSettled([
            this.updatePackageJsonScripts(config),
            this.updateAPIProxyRoutes(config),
            this.updateLauncherPorts(config)
        ]);
        
        const failures = updateResults.filter(result => result.status === 'rejected');
        
        if (failures.length > 0) {
            console.warn(`⚠️ ${failures.length} component updates failed`);
            failures.forEach((failure, index) => {
                console.warn(`   Update ${index + 1} failed:`, failure.reason.message);
            });
        }
        
        const successCount = updateResults.length - failures.length;
        console.log(`✅ ${successCount}/${updateResults.length} components updated successfully`);
        
        return failures.length === 0;
    }

    /**
     * Update package.json scripts with dynamic ports
     * @param {Object} config - Port configuration
     * @returns {Promise<void>}
     */
    async updatePackageJsonScripts(config) {
        try {
            const packagePath = path.join(this.projectRoot, 'package.json');
            const packageContent = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            
            // Update scripts with dynamic ports
            packageContent.scripts['dev:frontend'] = `next dev -p ${config.ports.frontend}`;
            packageContent.scripts['dev:backend'] = `nodemon backend/server.js`;
            packageContent.scripts['start:frontend'] = `next start -p ${config.ports.frontend}`;
            
            fs.writeFileSync(packagePath, JSON.stringify(packageContent, null, 2));
            console.log(`✅ Updated package.json with frontend port ${config.ports.frontend}`);
        } catch (error) {
            console.error('❌ Failed to update package.json:', error.message);
            throw error;
        }
    }

    /**
     * Update API proxy routes with correct backend port
     * @param {Object} config - Port configuration
     * @returns {Promise<void>}
     */
    async updateAPIProxyRoutes(config) {
        try {
            const routes = [
                'src/app/api/thumbnails/[...path]/route.ts',
                'src/app/api/previews/[...path]/route.ts',
                'src/app/api/[...slug]/route.ts'
            ];
            
            for (const routePath of routes) {
                const fullPath = path.join(this.projectRoot, routePath);
                
                if (fs.existsSync(fullPath)) {
                    let content = fs.readFileSync(fullPath, 'utf8');
                    
                    // Update the BACKEND_URL fallback to use correct port
                    const oldPattern = /: 'http:\/\/127\.0\.0\.1:\d+'/;
                    const newFallback = `: 'http://127.0.0.1:${config.ports.backend}'`;
                    
                    content = content.replace(oldPattern, newFallback);
                    
                    fs.writeFileSync(fullPath, content);
                    console.log(`✅ Updated ${routePath} with backend port ${config.ports.backend}`);
                }
            }
        } catch (error) {
            console.error('❌ Failed to update API proxy routes:', error.message);
            throw error;
        }
    }

    /**
     * Update launcher configuration (creates PowerShell-readable file)
     * @param {Object} config - Port configuration
     * @returns {Promise<void>}
     */
    async updateLauncherPorts(config) {
        try {
            // Create PowerShell-readable port configuration
            const psConfigPath = path.join(this.projectRoot, 'data', 'launcher-ports.ps1');
            
            const psConfig = `# Auto-generated port configuration for launcher.ps1
# This file is automatically updated by port-sync-manager.js
# DO NOT EDIT MANUALLY

$script:devFrontendPort = ${config.ports.frontend}
$script:prodFrontendPort = ${config.ports.frontend}
$script:backendPort = ${config.ports.backend}
$script:ngrokPort = ${config.ports.ngrok}
$script:portsToCheck = @($script:devFrontendPort, $script:prodFrontendPort, $script:backendPort)

# Environment variables for processes
$env:FRONTEND_PORT = "${config.ports.frontend}"
$env:BACKEND_PORT = "${config.ports.backend}"
$env:NGROK_PORT = "${config.ports.ngrok}"
$env:PORT = "${config.ports.frontend}"

Write-Host "📋 Loaded dynamic ports: Frontend=${config.ports.frontend}, Backend=${config.ports.backend}" -ForegroundColor Green
`;
            
            fs.writeFileSync(psConfigPath, psConfig);
            console.log(`✅ Created launcher port configuration at ${psConfigPath}`);
        } catch (error) {
            console.error('❌ Failed to update launcher ports:', error.message);
            throw error;
        }
    }

    /**
     * Perform comprehensive port health check
     * @returns {Promise<Object>} Health check results
     */
    async performHealthCheck() {
        console.log('🔍 Performing comprehensive port health check...');
        
        const config = await this.getCurrentConfig();
        const results = {
            timestamp: new Date().toISOString(),
            overall: 'unknown',
            ports: {},
            conflicts: [],
            recommendations: []
        };
        
        // Check each configured port
        for (const [service, port] of Object.entries(config.ports)) {
            const inUse = await this.isPortInUse(port);
            const processInfo = inUse ? await this.getPortProcessInfo(port) : null;
            
            results.ports[service] = {
                port,
                inUse,
                processInfo,
                isOurProcess: processInfo ? this.isOurProcess(processInfo) : false
            };
            
            if (inUse && processInfo && !this.isOurProcess(processInfo)) {
                results.conflicts.push({
                    service,
                    port,
                    conflictingProcess: processInfo
                });
            }
        }
        
        // Generate recommendations
        if (results.conflicts.length > 0) {
            results.overall = 'conflict';
            results.recommendations.push('Run port resolution to fix conflicts');
        } else {
            results.overall = 'healthy';
        }
        
        // Check for stale configuration
        const configAge = Date.now() - new Date(config.timestamp).getTime();
        if (configAge > 60 * 60 * 1000) { // 1 hour
            results.recommendations.push('Configuration is stale, consider refreshing');
        }
        
        return results;
    }

    /**
     * CLI command handler
     * @param {Array<string>} args - Command line arguments
     * @returns {Promise<void>}
     */
    async handleCommand(args) {
        const command = args[0] || 'sync';
        
        switch (command) {
            case 'sync':
                console.log('🔄 Starting unified port synchronization...');
                const config = await this.getCurrentConfig();
                await this.updateSystemComponents(config);
                console.log('✅ Port synchronization completed!');
                console.log(`📊 Ports: Frontend=${config.ports.frontend}, Backend=${config.ports.backend}`);
                break;
                
            case 'check':
                const healthResults = await this.performHealthCheck();
                console.log('\n📊 PORT HEALTH CHECK RESULTS:');
                console.log(`Overall Status: ${healthResults.overall.toUpperCase()}`);
                
                for (const [service, info] of Object.entries(healthResults.ports)) {
                    const status = info.inUse 
                        ? (info.isOurProcess ? '🟢 OUR PROCESS' : '🔴 CONFLICT') 
                        : '🟡 NOT IN USE';
                    console.log(`  ${service.padEnd(10)} (${info.port}): ${status}`);
                    
                    if (info.processInfo && !info.isOurProcess) {
                        console.log(`    └─ Conflicting: ${info.processInfo.name} (PID: ${info.processInfo.pid})`);
                    }
                }
                
                if (healthResults.recommendations.length > 0) {
                    console.log('\n💡 Recommendations:');
                    healthResults.recommendations.forEach(rec => console.log(`  • ${rec}`));
                }
                break;
                
            case 'resolve':
                console.log('🔧 Forcing port conflict resolution...');
                await this.resolveAndCreateConfig();
                break;
                
            case 'reset':
                console.log('🔄 Resetting port configuration...');
                if (fs.existsSync(this.configPath)) {
                    fs.unlinkSync(this.configPath);
                }
                await this.resolveAndCreateConfig();
                break;
                
            default:
                console.log(`
Unified Port Synchronization Manager

Commands:
  sync     - Synchronize all components with current configuration (default)
  check    - Perform comprehensive port health check
  resolve  - Force resolution of port conflicts
  reset    - Reset configuration and resolve from scratch

This ensures perfect port synchronization between:
  • PowerShell launcher
  • Backend server
  • Frontend Next.js
  • API proxy routes
  • Environment variables
`);
                break;
        }
    }
}

// CLI interface
if (require.main === module) {
    const manager = new UnifiedPortSyncManager();
    const args = process.argv.slice(2);
    
    manager.handleCommand(args)
        .then(() => {
            process.exit(0);
        })
        .catch(error => {
            console.error('❌ Port sync manager failed:', error.message);
            process.exit(1);
        });
}

module.exports = UnifiedPortSyncManager;