#!/bin/bash
# Quick fix for Qwen Code CLI setup
# This script helps configure Qwen Code with working API endpoints

echo "🔧 Qwen Code Configuration Helper"
echo "=================================="

# Method 1: Use OpenAI API (most reliable for tool calling)
cat > qwen-openai-config.env << 'EOF'
# OpenAI Configuration (Recommended)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# Alternative models that support tool calling:
# - gpt-4o
# - gpt-4-turbo  
# - gpt-3.5-turbo
EOF

# Method 2: Use Alibaba Cloud (if you have access)
cat > qwen-alibaba-config.env << 'EOF'
# Alibaba Cloud Configuration
# China mainland: https://bailian.console.aliyun.com/
# International: https://modelstudio.console.alibabacloud.com/
QWEN_API_KEY=your_alibaba_api_key_here
QWEN_BASE_URL=https://modelstudio.aliyuncs.com/v1/chat/completions
EOF

# Method 3: Local model setup
cat > qwen-local-config.env << 'EOF'
# Local Model Configuration (using Ollama)
# Install: https://ollama.com/
# Then run: ollama pull qwen2.5-coder
QWEN_API_KEY=not_required
QWEN_BASE_URL=http://localhost:11434/v1
EOF

echo ""
echo "✅ Created configuration files:"
echo "   • qwen-openai-config.env (recommended)"
echo "   • qwen-alibaba-config.env"
echo "   • qwen-local-config.env"
echo ""
echo "📝 To use:"
echo "1. Choose one configuration file"
echo "2. Add your API key"
echo "3. Copy contents to your .env file"
echo "4. Test with: qwen --prompt \"Hello, this is a test\""
echo ""
echo "🚫 Current issue: OpenRouter free tier doesn't support tool calling"
echo "   You need a paid API that supports function calling for qwen-code to work"
